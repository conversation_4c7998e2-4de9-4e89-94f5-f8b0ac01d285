package com.timevale.saas.common.manage.common.service.integration;

import com.google.common.collect.Lists;
import com.timevale.billing.manager.facade.api.RpcOrder;
import com.timevale.billing.manager.facade.model.MultiSceneReq;
import com.timevale.billing.manager.facade.model.OrderIdsReq;
import com.timevale.billing.manager.facade.model.OrderQuery;
import com.timevale.billing.manager.facade.model.PageResult;
import com.timevale.billing.manager.facade.model.Result;
import com.timevale.billing.manager.facade.model.order.EffectiveOrderMoel;
import com.timevale.billing.manager.facade.model.order.EffectiveOrderQuery;
import com.timevale.billing.manager.facade.model.order.OrderModel;
import com.timevale.billing.manager.sdk.api.OrderAuthAPI;
import com.timevale.billing.manager.sdk.api.OrderBaseAPI;
import com.timevale.billing.manager.sdk.api.OrderSearchAPI;
import com.timevale.billing.manager.sdk.enums.UseScopeEnum;
import com.timevale.billing.manager.sdk.enums.order.StatusEnum;
import com.timevale.billing.manager.sdk.model.AggreEffectiveOrderModel;
import com.timevale.billing.manager.sdk.model.order.SceneInfoReq;
import com.timevale.billing.manager.sdk.model.order.SimpleOrderModel;
import com.timevale.billing.manager.sdk.request.AggregateMarginQuery;
import com.timevale.billing.manager.sdk.request.MultiSceneInfoParam;
import com.timevale.billing.manager.sdk.model.order.auth.OrderAuthRulesSyncParam;
import com.timevale.billing.manager.sdk.model.order.auth.UserAuthSyncParam;
import com.timevale.billing.manager.sdk.request.OrderPurchaseReq;
import com.timevale.billing.manager.sdk.response.OrderPurchaseResultDTO;
import com.timevale.billing.promotion.common.service.api.RpcActiveRecord;
import com.timevale.billing.promotion.common.service.model.ActiveDetailModel;
import com.timevale.billing.promotion.common.service.model.ActiveRecordModel;
import com.timevale.esign.platform.toolkit.utils.exceptions.BizException;
import com.timevale.mandarin.base.enums.BaseResultCodeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.BusinessResult;
import com.timevale.mandarin.common.result.ListResult;
import com.timevale.mandarin.common.result.QueryResult;
import com.timevale.product.facade.api.v2.CommodityRPCService;
import com.timevale.product.facade.model.response.showcase.CommodityInfo;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.saas.common.manage.common.service.enums.OrderStatusEnum;

import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-01-30
 */
@Service
@Slf4j
public class BillingClient {

    @Autowired private RpcActiveRecord rpcActiveRecord;
    @Autowired private RpcOrder rpcOrder;
    @Autowired private OrderBaseAPI orderBaseAPI;
    @Autowired private OrderSearchAPI orderSearchAPI;
    @Autowired private OrderAuthAPI orderAuthAPI;
    @Autowired private CommodityRPCService commodityRPCService;

    public static final Long SAAS_PRODUCT_ID = 64L;

//    public static final Long SAAS_GLOBAL_PRODUCT_ID = 579L;

    /** 支付过期时间，单位分钟 */
    public static final Integer PAY_EXPIRE_MINUTES = 30;

    public ActiveDetailModel queryActiveByNo(String activeNo) {
        return rpcActiveRecord.queryActiveByNo(activeNo).getData();
    }

    public ActiveRecordModel queryActiveInfo(String activeNo, String gid) {
        return rpcActiveRecord.queryActiveInfo(activeNo, gid).getData();
    }

    /**
     * 校验赠送活动是否存在
     *
     * @param activeNo
     * @return
     */
    public boolean checkActiveNoExisted(String activeNo) {
        try {
            rpcActiveRecord.queryActiveInfo(activeNo, null);
        } catch (BizException e) {
            log.warn("queryActiveInfo failed, activeNo:{}", activeNo, e);
            return false;
        }
        return true;
    }

    /**
     * 查询轩辕域产品余额
     *
     * @param gid
     * @param useScopeList
     * @return
     */
    public AggreEffectiveOrderModel queryEffectiveOrderList(
            String gid, List<Integer> useScopeList, SceneInfoReq sceneInfoReq, Long productId) {
        // 异常处理
        try {
            Map<Long, AggreEffectiveOrderModel> mapResult= getAggregateEffectiveMargin(gid,useScopeList,sceneInfoReq);
            return MapUtils.isEmpty(mapResult)?null:mapResult.get(productId);
        } catch (Exception e) {
            ExceptionLogUtil.messageLogIfWarn(log, e, "order service error {}", e.getMessage());
            // 服务异常 返回-1 前端展示 "-"
            AggreEffectiveOrderModel moel = new AggreEffectiveOrderModel();
            moel.setMargin(new BigDecimal(-1));
            return moel;
        }
    }

    public Map<Long, AggreEffectiveOrderModel> queryAllEffectiveOrderList(
            String gid, List<Integer> useScopeList, SceneInfoReq sceneInfoReq) {
        // 异常处理
        Map<Long, AggreEffectiveOrderModel> mapResult = getAggregateEffectiveMargin(gid, useScopeList, sceneInfoReq);
        return Optional.ofNullable(mapResult).orElse(new HashMap<>());
    }

    /**
     * 有效订单聚合查询,支持隔离场景
     *
     * @return
     */
   public Map<Long, AggreEffectiveOrderModel> getAggregateEffectiveMargin(
            String gid, List<Integer> useScopeList, SceneInfoReq sceneInfoReq) {
        AggregateMarginQuery aggregateMarginQuery = new AggregateMarginQuery();
        aggregateMarginQuery.setGid(gid);
        aggregateMarginQuery.setUseScopeList(useScopeList);
        if (sceneInfoReq != null) {
            MultiSceneInfoParam sceneInfoParam = new MultiSceneInfoParam();
            sceneInfoParam.setSceneInfos(Lists.newArrayList(sceneInfoReq));
            aggregateMarginQuery.setMultiSceneInfos(Lists.newArrayList(sceneInfoParam));
        }
        QueryResult<Map<Long, AggreEffectiveOrderModel>> queryResult =
                orderSearchAPI.getAggregateEffectiveMargin(aggregateMarginQuery);
        return queryResult == null ? null : queryResult.getResultObject();
    }

    /**
     * 获取最近结束的订单，如果都用完了，那最近结束订单是updateTime最晚的；
     *
     * <p>如果有没用完的，那最近结束订单就是endTime最晚的；
     *
     * @param orgGid
     * @return
     */
    public OrderModel getLatestFinishedOrder(
            String orgGid, List<Integer> useScopeList, List<MultiSceneReq> multiSceneInfos, Long productId) {
        OrderModel latestEndOrder =
                queryLatestOrder(
                        orgGid,
                        OrderStatusEnum.FINISHED.getStatus(),
                        BillingClient.SortFieldEnum.END_TIME.getCode(),
                        useScopeList,
                        multiSceneInfos, productId);
        if (Objects.isNull(latestEndOrder)) {
            return null;
        }

        if (greaterThan(latestEndOrder.getMargin(), BigDecimal.ZERO)) {
            return latestEndOrder;
        }

        OrderModel latestUpdateOrder =
                queryLatestOrder(
                        orgGid,
                        OrderStatusEnum.FINISHED.getStatus(),
                        BillingClient.SortFieldEnum.UPDATE_TIME.getCode(),
                        useScopeList,
                        multiSceneInfos, productId);
        if (Objects.isNull(latestUpdateOrder)) {
            // 这种情况应该不会出现
            return latestEndOrder;
        }

        // 提前用完，返回最后更新的订单
        if (valueEquals(latestEndOrder.getMargin(), BigDecimal.ZERO)
                && valueEquals(latestUpdateOrder.getMargin(), BigDecimal.ZERO)) {
            return latestUpdateOrder;
        }

        return latestEndOrder;
    }

    /**
     * 查询最近生效过的套餐
     *
     * @param orgGid
     * @param status {@link OrderStatusEnum}
     * @param sortBy 默认0，0-创建时间倒序，1-订单结束时间倒序 {@link SortFieldEnum}
     * @param useScopeList 使用范围
     * @return
     */
    public OrderModel queryLatestOrder(
            String orgGid,
            int status,
            int sortBy,
            List<Integer> useScopeList,
            List<MultiSceneReq> multiSceneInfos, Long productId) {
        if (StringUtils.isBlank(orgGid)) {
            return null;
        }
        // 获取已失效的最近订单
        OrderQuery query = new OrderQuery();
        query.setGid(orgGid);
        query.setProductId(Optional.ofNullable(productId).orElse(SAAS_PRODUCT_ID));
        query.setStatus(status);
        query.setSortBy(sortBy);
        query.setUseScopeList(useScopeList);
        query.setMultiSceneInfos(multiSceneInfos);
        Result<OrderModel> effectedResult = rpcOrder.querySingleOrder(query);

        return Optional.ofNullable(effectedResult).map(Result::getData).orElse(null);
    }

    /**
     * 获取待支付订单,支持隔离场景
     *
     * @return
     */
    public List<OrderModel> listPendingOrder(
            String orgGid,
            String userOid,
            List<Integer> useScopeList,
            List<MultiSceneReq> multiSceneInfos) {
        if (StringUtils.isBlank(orgGid)) {
            return Collections.emptyList();
        }
        OrderQuery query = new OrderQuery();
        query.setMultiSceneInfos(processMultiSceneInfos(multiSceneInfos,useScopeList));
        query.setUseScopeList(useScopeList);
        query.setGid(orgGid);
        query.setProductId(SAAS_PRODUCT_ID);
        query.setPurchaserOid(userOid);
        query.setPayStatus(0);
        // 认为用户不会在30分钟内下100单
        query.setPageSize(100);
        PageResult<List<OrderModel>> pageResult = rpcOrder.queryAuthedOrderListForCustomer(query);

        List<OrderModel> orders =
                Optional.ofNullable(pageResult)
                        .map(Result::getData)
                        .orElse(Collections.emptyList());

        // 获取最近30分钟内未支付订单
        Date pendingTime = DateUtils.addMinutes(new Date(), -PAY_EXPIRE_MINUTES);

        return orders.stream()
                .filter(order -> order.getCreateTime().after(pendingTime))
                .collect(Collectors.toList());
    }


    /**
     * 获取所有生效订单，支持隔离场景
     * @param orgGid 企业gid
     * @param useScopeList 使用范围
     * @param multiSceneInfos 隔离场景，不隔离情况下传null
     * @return
     */
    public List<EffectiveOrderMoel> queryEffectiveOrders(
            String orgGid, List<Integer> useScopeList, List<MultiSceneReq> multiSceneInfos) {
        EffectiveOrderQuery query = new EffectiveOrderQuery();
        query.setGid(orgGid);
        query.setProductId(SAAS_PRODUCT_ID);
        query.setUseScopeList(useScopeList);
        query.setMultiSceneInfos(multiSceneInfos);
        Result<List<EffectiveOrderMoel>> result = rpcOrder.queryEffectiveOrderList(query);

        return Optional.ofNullable(result).map(Result::getData).orElse(Collections.emptyList());
    }


    public List<OrderModel> getOrdersByBatchId(List<String> ids){
        OrderIdsReq orderIdsReq = new OrderIdsReq();
        orderIdsReq.setIds(ids);
        return rpcOrder.getOrdersByBatchId(orderIdsReq).getData();
    }
    
    /**
     * 判断用户是否购买过套餐
     *
     * @param orgGid
     * @return
     */
    public boolean hasPurchased(String orgGid) {
        OrderQuery query = new OrderQuery();
        query.setGid(orgGid);
        query.setProductId(SAAS_PRODUCT_ID);

        PageResult<List<OrderModel>> pageResult = rpcOrder.queryOrderList(query);

        return Optional.ofNullable(pageResult)
                .map(PageResult::getData)
                .filter(CollectionUtils::isNotEmpty)
                .isPresent();
    }

    public SimpleOrderModel queryByShowId(String showId) {
        try {
            SimpleOrderModel simpleOrderModel = orderBaseAPI.queryByShowId(showId);
            return Optional.ofNullable(simpleOrderModel).orElse(null);
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "orderBaseAPI.queryByShowId fail  showId:{}", showId);
            throw e;
        }
    }

    /**
     * 根据橱窗id获取商品信息
     *
     * @param showcaseNo
     * @return
     */
   public List<CommodityInfo> getCommodityWithShowCaseNo(String showcaseNo) {
        ListResult<CommodityInfo> result = commodityRPCService.getCommoditysOfShowcase(showcaseNo);
        return result == null ? Lists.newArrayList() : result.getResultList();
    }

    /**
     * a > b ?
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean greaterThan(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }
        return a.compareTo(b) > 0;
    }

    /**
     * a = b ?
     *
     * <p>这里是对值进行比较，如果直接用BigDecimal.equals()，会比较scale（小数位数）
     *
     * <p>例如：BiDecimal.ONE.equals(new BigDecimal("1.0"))，会返回false
     *
     * @param a
     * @param b
     * @return
     */
    public static boolean valueEquals(BigDecimal a, BigDecimal b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }
        return a.compareTo(b) == 0;
    }

    /** 订单排序规则 */
    @Getter
    @AllArgsConstructor
    public enum SortFieldEnum {
        CREATE_TIME(0, "createTime"),
        END_TIME(1, "endTime"),
        UPDATE_TIME(2, "updateTime"),
        ;

        private Integer code;
        private String filedName;
    }

    public void syncUserAuth(UserAuthSyncParam param){
        orderAuthAPI.syncUserAuth(param);
    }

    public void syncOrderAuthRules(OrderAuthRulesSyncParam param){
        orderAuthAPI.syncOrderAuthRules(param);
    }


    /**
     * 是否购买过商品
     */
    public boolean whetherToPurchase(String gid, Long productId) {
        OrderPurchaseReq orderPurchaseReq = new OrderPurchaseReq();
        orderPurchaseReq.setGid(gid);
        orderPurchaseReq.setProductId(productId);
        orderPurchaseReq.setStatusList(Arrays.asList(
                StatusEnum.VALID.getCode(),
                StatusEnum.DELETE_VALID.getCode(),
                StatusEnum.RENEWAL_INIT.getCode(),
                StatusEnum.FREEZE.getCode()));
        BusinessResult<OrderPurchaseResultDTO> result = orderSearchAPI.queryOrderPurchaseHistory(orderPurchaseReq);
        if (BaseResultCodeEnum.SUCCESS.getNCode() != result.getCode()) {
            throw new SaasCommonBizException(String.valueOf(result.getCode()), result.getMessage());
        }
        return Boolean.TRUE.equals(result.getData().getHasOrder());
    }

    /**
     * 隔离场景处理，主要针对计费特殊情况，无强隔离下去除隔离场景
     *
     * @param multiSceneInfos
     * @param useScopeList
     * @return
     */
    private List<MultiSceneReq> processMultiSceneInfos(
            List<MultiSceneReq> multiSceneInfos, List<Integer> useScopeList) {
        if (CollectionUtils.isEmpty(multiSceneInfos) || CollectionUtils.isEmpty(useScopeList)) {
            return multiSceneInfos;
        }

        // 检查使用范围列表是否包含强隔离代码，不包含去除隔离场景信息
        boolean hasIsolation = useScopeList.contains(UseScopeEnum.ISOLATION.getCode());
        return hasIsolation ? multiSceneInfos : null;
    }
}
