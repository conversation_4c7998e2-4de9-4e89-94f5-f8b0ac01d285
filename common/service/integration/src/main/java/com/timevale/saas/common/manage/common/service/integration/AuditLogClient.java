package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.dayu.config.result.EventDataResult;
import com.timevale.dayu.config.service.DataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 审计日志相关
 *
 * <AUTHOR>
 * @since 2025/2/20 下午2:26
 */
@Service
@Slf4j
public class AuditLogClient {

    @Autowired
    private DataService dataService;

    public EventDataResult querySubscribeEvent() {
        return dataService.querySubscribeEvent();
    }
}
