package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.shortlink.common.service.api.ShortLinkRpcService;
import com.timevale.shortlink.common.service.request.ShortenCodeRequest;
import com.timevale.shortlink.common.service.request.ShortenRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> on 2023/11/0
 */
@Service
public class ShortUrlClient {

    @Resource
    private ShortLinkRpcService shortLinkRpcService;

    /**
     * 长链转换成短链
     *
     * @param longUrl 长链接
     * @return 短链接
     */
    public String convertShortUrl(String longUrl) {
        ShortenRequest request = new ShortenRequest();
        request.setUrl(longUrl);
        return shortLinkRpcService.getShortLink(request).getHttpsShortlink();
    }

    /**
     * 获取短码
     *
     * @param longCode 长码
     * @return 短码
     */
    public String convertShortCode(String longCode) {
        ShortenCodeRequest request = new ShortenCodeRequest();
        request.setLongCode(longCode);
        return shortLinkRpcService.getShortCode(request).getShortCode();
    }
}
