package com.timevale.saas.common.manage.common.service.integration.client;

import com.timevale.saas.common.manage.common.service.exception.ResultEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @since 2022/7/7 IntegrationClient 调用的业务结果校验
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface ICResultCheck {
    /**
     * 校验失败的异常信息
     *
     * @return
     */
    ResultEnum value() default ResultEnum.UNCATCHD_EXCEPTION;

    /**
     * 校验返回结果不为空
     *
     * 默认 true 不允许为空，如果返回是void类型，需要设置为false，不做校验
     * @return
     */
    boolean isNotNull() default true;

    /**
     * 是否使用 value 对应的错误异常，默认使用
     *
     * @return
     */
    boolean useResultEnum() default true;

    /**
     * 指定的 errorCode ， useResultEnum 为false时使用
     *
     * @return
     */
    int errorCode() default 70001000;

    /**
     * 指定的 errorMsg ， useResultEnum 为false时使用
     *
     * @return
     */
    String errorMsg() default "服务异常";
}
