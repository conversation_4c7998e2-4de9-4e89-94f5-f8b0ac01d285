package com.timevale.saas.common.manage.common.service.integration;

import com.google.common.collect.Lists;
import com.timevale.account.service.api.RpcICUserService;
import com.timevale.account.service.enums.AccountType;
import com.timevale.account.service.enums.RealnameStatus;
import com.timevale.account.service.exception.Errors;
import com.timevale.account.service.exception.data.ICUserIdExistException;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.builder.acc.BizICUserCreateInputBuilder;
import com.timevale.account.service.model.service.biz.helper.acc.BizICUserOutputHelper;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUserCreateInput;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUserGetInput;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUserGlobalBatchGetInput;
import com.timevale.account.service.model.service.biz.icuser.input.BizICUsersGetInput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserBaseOutput;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.account.service.model.service.mods.icuser.ICUserGlobalId;
import com.timevale.account.service.model.service.mods.icuser.ICUserId;
import com.timevale.account.service.model.service.mods.icuser.ICUserOpenId;
import com.timevale.account.service.model.service.mods.icuser.share.GlobalCredentials;
import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.easun.service.api.*;
import com.timevale.easun.service.enums.DeleteStatusEnum;
import com.timevale.easun.service.model.IdcardTypeEnum;
import com.timevale.easun.service.model.account.BizAccCreateRequest;
import com.timevale.easun.service.model.account.input.BizAccountGetByIdcardInput;
import com.timevale.easun.service.model.account.input.BizGetIcUserOrgListInput;
import com.timevale.easun.service.model.account.input.BizICUsersEasunGetInput;
import com.timevale.easun.service.model.account.input.OrgICUserCreateRequest;
import com.timevale.easun.service.model.account.output.BizAccountRealNameOutput;
import com.timevale.easun.service.model.account.output.BizOrgBaseInfoOutput;
import com.timevale.easun.service.model.account.output.BizOrganOuid;
import com.timevale.easun.service.model.account.output.BizUserInfoOutput;
import com.timevale.easun.service.model.esearch.EasunEsearchAccountOutput;
import com.timevale.easun.service.model.esearch.EsOrgSearchInput;
import com.timevale.easun.service.model.esearch.EsSearchInput;
import com.timevale.easun.service.model.esearch.input.BizEsAccountIdcardSearchInput;
import com.timevale.easun.service.model.esearch.input.EsGetMembersByOidInput;
import com.timevale.easun.service.model.esearch.output.EsAccountIdcardInfoOutput;
import com.timevale.easun.service.model.esearch.output.EsOrgSearhOutput;
import com.timevale.easun.service.model.organization.ICOrg.input.BizIcDeleteIcMemberBatchInput;
import com.timevale.easun.service.model.organization.OrganAccountDetail;
import com.timevale.easun.service.model.privilege.mods.BizBuiltinPrivilegeDetail;
import com.timevale.easun.service.model.privilege.output.BizEasunGetBuiltinPrivilegesOutput;
import com.timevale.easun.service.model.role.input.IcRole.EasunGetIcUserRolesByOrgInput;
import com.timevale.easun.service.model.role.output.RoleInfo;
import com.timevale.easun.service.model.role.output.UserRoleOutput;
import com.timevale.easun.service.utils.CreateSourceUtil;
import com.timevale.esign.platform.toolkit.utils.exceptions.BizException;
import com.timevale.esign.platform.toolkit.utils.mods.page.PagerResult;
import com.timevale.esign.platform.toolkit.utils.page.Pages;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.timevale.saas.common.manage.common.service.exception.ResultEnum.*;

/**
 * <AUTHOR>
 * @since 2020-08-26
 */
@Slf4j
@Service
public class UserCenterClient {

    @Autowired private RpcEsAccountService rpcEsAccountService;
    @Autowired private RpcOrgPlusService rpcOrgPlusService;
    @Autowired private RpcIcUserPlusService rpcIcUserPlusService;
    @Autowired private RpcGlobalUserPlusService rpcGlobalUserPlusService;
    @Autowired private RpcAccountPlusService rpcAccountPlusService;
    @Autowired private RpcICUserService rpcICUserService;
    @Autowired private RpcIcOrgPlusService rpcIcOrgPlusService;
    @Autowired private RpcRolePrivilegePlusService rpcRolePrivilegePlusService;
    @Autowired private RpcIcRolePrivilegePlusService rpcIcRolePrivilegePlusService;

    public BizICUserBaseOutput getAccountBaseByOid(String userOid) {
        RpcInput<BizICUserGetInput> out = new RpcInput<>();
        BizICUserGetInput input = new BizICUserGetInput();
        ICUserOpenId icUserOpenId = new ICUserOpenId();
        icUserOpenId.setOuid(userOid);
        input.setData(icUserOpenId);
        out.setInput(input);
        RpcOutput<BizICUserBaseOutput> rpcOutput = rpcIcUserPlusService.getICUserBaseByOuid(out);
        return rpcOutput.getData();
    }

    public BizICUserOutput getAccountByOid(String userOid) {
        if(StringUtils.isBlank(userOid)){
            throw new SaasCommonBizException(ACCOUNT_ID_INVALID);
        }
        RpcInput<BizICUserGetInput> out = new RpcInput<>();
        BizICUserGetInput input = new BizICUserGetInput();
        ICUserOpenId icUserOpenId = new ICUserOpenId();
        icUserOpenId.setOuid(userOid);
        input.setData(icUserOpenId);
        out.setInput(input);
        RpcOutput<BizICUserOutput> rpcOutput = rpcIcUserPlusService.getICUserByOuid(out);
        return rpcOutput.getData();
    }

    public BizAccountRealNameOutput getAccountDetailByOid(String userOid) {
        RpcInput<BizICUserGetInput> out = new RpcInput<>();
        BizICUserGetInput input = new BizICUserGetInput();
        ICUserOpenId icUserOpenId = new ICUserOpenId();
        icUserOpenId.setOuid(userOid);
        input.setData(icUserOpenId);
        out.setInput(input);
        RpcOutput<BizAccountRealNameOutput> rpcOutput = rpcIcUserPlusService.getRealNameByOuid(out);
        return rpcOutput.getData();
    }

    public List<BizAccountRealNameOutput> queryAccountDetailListByOidList(List<String> oidList) {
        RpcInput<BizICUsersGetInput> inputRpcInput = new RpcInput<>();
        BizICUsersGetInput bizICUsersGetInput = new BizICUsersGetInput(oidList);
        inputRpcInput.setInput(bizICUsersGetInput);
        RpcOutput<List<BizAccountRealNameOutput>> rpcOutput = rpcIcUserPlusService.getRealNameByOuids(inputRpcInput);
        return rpcOutput.getData();
    }

    public List<BizICUserOutput> getAccountDetailsByGid(String userGid) {
        RpcInput<ICUserGlobalId> out = new RpcInput<>();
        ICUserGlobalId globalId = new ICUserGlobalId();
        globalId.setGuid(userGid);
        out.setInput(globalId);
        RpcOutput<List<BizICUserOutput>> rpcOutput = rpcIcUserPlusService.getICUserByGuid(out);
        return rpcOutput.getData();
    }

    public BizAccountRealNameOutput getAccountDetailByGid(String userGid){
        RpcInput<ICUserGlobalId> out = new RpcInput<>();
        ICUserGlobalId globalId = new ICUserGlobalId();
        globalId.setGuid(userGid);
        out.setInput(globalId);
        RpcOutput<List<BizICUserOutput>> rpcOutput = rpcIcUserPlusService.getICUserByGuid(out);
        List<BizICUserOutput> accounts = rpcOutput.getData();
        if(accounts==null||CollectionUtils.isEmpty(accounts)) {
            return null;
        }

        BizICUserOutput icUserOutput = accounts.get(0);
        return getAccountDetailByOid(icUserOutput.getId().getOuid());
    }

    /** 查询账号基本信息 */
    public BizICUserOutputHelper getAccountDetailByOuid(String oid) {
        try {
            RpcInput<BizICUserGetInput> out = new RpcInput<>();
            BizICUserGetInput getInput = new BizICUserGetInput(oid);
            out.setInput(getInput);
            RpcOutput<BizICUserOutput> rpcOutput = rpcIcUserPlusService.getICUserByOuid(out);
            BizICUserOutputHelper helper = new BizICUserOutputHelper(rpcOutput.getData());
            return helper;
        } catch (Exception e) {
            log.warn("rpcIcUserPlusService error oid = {}", oid, e);
            return null;
        }
    }

    public BizAccountRealNameOutput getLatestAccountDetailByGid(String gid){
        RpcInput<BizICUserGlobalBatchGetInput> input = new RpcInput<>();
        BizICUserGlobalBatchGetInput globalBatchGetInput = new BizICUserGlobalBatchGetInput();
        ICUserGlobalId globalId = new ICUserGlobalId();
        globalId.setGuid(gid);
        globalBatchGetInput.setData(Arrays.asList(globalId));
        input.setInput(globalBatchGetInput);
        RpcOutput<Map<String, BizICUserOutput>> output = rpcGlobalUserPlusService.getLatestCommonByGuidV2(input);
        Map<String, BizICUserOutput> gidUserMap = output.getData();
        if (!gidUserMap.containsKey(gid)) {
            //如果账户已注销，上面接口不会返回数据，走老接口重新查一次
            return getAccountDetailByGid(gid);
        }

        BizICUserOutput icUserOutput = gidUserMap.get(gid);
        return getAccountDetailByOid(icUserOutput.getId().getOuid());
    }

    public Map<String, BizICUserOutput> queryICUserByGidList(List<String> gidList) {
        BizICUserGlobalBatchGetInput globalBatchGetInput = new BizICUserGlobalBatchGetInput(gidList);
        RpcInput<BizICUserGlobalBatchGetInput> input = new RpcInput<>(globalBatchGetInput);
        RpcOutput<Map<String, BizICUserOutput>> output = rpcGlobalUserPlusService.getLatestCommonByGuidV2(input);
        return output.getData();
    }

    /**
     * 根据IDcard获取用户信息
     * <AUTHOR>
     * @date 2020/1/14 15:11
     * @param idcard
     * @param idcardTypeEnum idcard类型
     * @return com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput
     */
    public BizUserInfoOutput getUserInfoByIdcard(String idcard, IdcardTypeEnum idcardTypeEnum){
        RpcInput<BizAccountGetByIdcardInput> rpcInput = new RpcInput<>();
        BizAccountGetByIdcardInput bizAccountGetByIdcardInput = new BizAccountGetByIdcardInput();
        rpcInput.setInput(bizAccountGetByIdcardInput);

        bizAccountGetByIdcardInput.setIdcard(idcard);
        bizAccountGetByIdcardInput.setType(idcardTypeEnum.name());

        RpcOutput<BizUserInfoOutput> rpcOutput;
        try {
            rpcOutput = rpcAccountPlusService.getUserInfoByIdcard(rpcInput);
            return rpcOutput.getData();
        } catch (BaseRuntimeException e) {
            ExceptionLogUtil.messageLogIfWarn(log, e, " query user info by idCard {} ,  msg:{}",idcard, e.getMessage());
            throw  e;
        }
    }

    /**
     * 通过联系方式创建账号
     *
     * @param contact 联系方式
     * @return 返回三个id
     */
    public ICUserId createByContact(String contact){
        boolean isMobile = Pattern.matches("^\\d{11}$", contact);
        ICUserId icUserId;
        BizICUserCreateInput input =
                BizICUserCreateInputBuilder.builder()
                        // 设置账号类型<br/>
                        .setType(AccountType.PERSON)
                        // 设置登录手机 和 密码 （没有则不填），默认会被同时设置到联系方式属性中<br/>
                        .buildIdcardMobile()
                        .set(isMobile ? contact : "")
                        .finish()
                        // 设置登录邮箱（没有则不填），默认会被同时设置到联系方式属性中<br/>
                        .buildIdcardEmail()
                        .set(isMobile ? "" : contact)
                        .finish()
                        .finish();
        try {
            RpcOutput<ICUserId> bizAccountOutputRpcOutput = rpcICUserService.createICUser(new RpcInput<>(input));
            icUserId = bizAccountOutputRpcOutput.getData();
        } catch (ICUserIdExistException existExc) {
            ICUserId existUserId = existExc.getData();
            log.info(
                    "create account by contact failed, user exist , contact:{}, userOid:{}",
                    contact,
                    existUserId != null ? existUserId.getOuid() : "");
            icUserId = existUserId;
        } catch (BaseRuntimeException e) {
            ExceptionLogUtil.messageLogIfWarn(log, e, " create account by contact error ,  msg:{}", e.getMessage());
            throw new SaasCommonBizException(ResultEnum.CREATE_PSN_ACCOUNT_FAIL);
        }
        return icUserId;
    }
    public String createOrgByNameAndCertNo(String name, String certType, String certNo, String projectId, String accountId, String registerSource) {
        // 创建账号详情，同e签宝账号创建，因为不需要全局互通标识
        // 但是必须要设置应用标识
        BizICUserCreateInputBuilder builder = BizICUserCreateInputBuilder.builder();

        // 设置账号类型<br/>
        builder.setType(AccountType.ORGANIZE);

        builder.buildProductApp().finish(projectId);
        BizICUserCreateInput input = builder.finish();

        // 复制属性
        List<Property> properties = new ArrayList<>(8);
        if (StringUtils.isNotBlank(name)) {
            properties = buildProperty(BuiltinProperty.INFO_NAME, name, properties);
        }
        //设置clientId
        properties = buildProperty(BuiltinProperty.INFO_REGISTER_SOURCE, CreateSourceUtil.genCreateSourceEndPointStr(registerSource), properties);
        input.setProperties(properties);

        if (StringUtils.isNotBlank(certNo)) {
            List<Property> credentials = Lists.newArrayList();
            credentials = buildProperty(certType, certNo, credentials);
            input.setCredentials(credentials);

            // 要生成gid，设置global credentials
            GlobalCredentials gc = new GlobalCredentials();
            gc.setType(certType);
            gc.setValue(certNo);
            input.setGlobalCredentials(gc);
        }

        BizAccCreateRequest<BizICUserCreateInput> request = new BizAccCreateRequest<>();
        // 设置账号信息
        request.setAccount(input);
        // 组装请求参数
        OrgICUserCreateRequest createRequest = new OrgICUserCreateRequest();
        createRequest.setCreateOuid(accountId);
        createRequest.setAccountSourceRequest(request);
        // 创建企业账号
        RpcOutput<BizOrganOuid> output = rpcOrgPlusService.createICOrgAndCompanyWithSource(new RpcInput(createRequest));
        return output.getData().getOuid();
    }

    /**
     * 根据姓名或证件号创建个人账号
     * @param name
     * @param certType
     * @param certNo
     * @param projectId
     * @return
     */
    public String createPsnByCertNo(String name, String certType, String certNo, String projectId) {
        // 创建账号详情，同e签宝账号创建，因为不需要全局互通标识
        // 但是必须要设置应用标识
        BizICUserCreateInputBuilder builder = BizICUserCreateInputBuilder.builder();
        // 设置账号类型
        builder.setType(AccountType.PERSON);
        // 设置应用id
        builder.buildProductApp().finish(projectId);
        BizICUserCreateInput input = builder.finish();
        // 复制属性
        List<Property> properties = new ArrayList<>(8);
        if (StringUtils.isNotBlank(name)) {
            properties = buildProperty(BuiltinProperty.INFO_NAME, name, properties);
        }
        input.setProperties(properties);
        // 如果证件号不为空
        if (StringUtils.isNotBlank(certNo)) {
            // 设置证件号
            input.setCredentials(buildProperty(certType, certNo, Lists.newArrayList()));
            // 要生成gid，设置global credentials
            GlobalCredentials gc = new GlobalCredentials();
            gc.setType(certType);
            gc.setValue(certNo);
            input.setGlobalCredentials(gc);
        }
        // 创建个人账号
        RpcOutput<ICUserId> output =
                rpcIcUserPlusService.createICUserAll(new RpcInput(input));
        return output.getData().getOuid();
    }

    public EsOrgSearhOutput getEsOrganInfo(String orgName, String certType, String certNo, boolean realNamed) {
        RpcInput<EsOrgSearchInput> rpcInput = new RpcInput<>();
        EsOrgSearchInput input = new EsOrgSearchInput();
        input.setName(orgName);
        input.setOrganGuidExist(realNamed);
        input.setCodeType(certType);
        input.setCodeValue(certNo);
        rpcInput.setInput(input);
        PagerResult<EsOrgSearhOutput> rpcOutput =
                rpcEsAccountService.getOrgsByEs(rpcInput).getData();
        if (CollectionUtils.isNotEmpty(rpcOutput.getItems())) {
            return rpcOutput.getItems().get(0);
        }
        return null;
    }

    /**
     * 批量查询gid获取名称对应的激活态实名组织,目前只返回property信息
     * gid和name二选一，同时选的话查询为或的查询条件
     * @param guidList
     * @return
     */
    public List<EsOrgSearhOutput> batchGetActiveOrgByGuidsOrName(List<String> guidList,List<String> nameList) {
        EsOrgSearchInput input = new EsOrgSearchInput();
        input.setActivate(1);
        input.setNeedDefalutAppId(false);
        input.setSourceData(Lists.newArrayList("user.propertyMaps", "user.id.guid"));
        input.setGuidList(guidList);
        input.setNames(nameList);
        Integer orgByEsCount = ConfigService.getAppConfig().getIntProperty("org.by.es.count", 1000);
        input.setPages(new Pages(0,orgByEsCount));
        RpcOutput<PagerResult<EsOrgSearhOutput>> resultRpcOutput =
                rpcEsAccountService.getOrgsByEs(new RpcInput<>(input));
        return resultRpcOutput == null || resultRpcOutput.getData() == null
                ? Lists.newArrayList()
                : resultRpcOutput.getData().getItems();
    }

    /**
     * 根据证件号及证件类型查询个人账号信息
     * @param certType
     * @param certNo
     * @param realNamed
     * @return
     */
    public EasunEsearchAccountOutput getEsPsnInfo(String certType, String certNo, boolean realNamed) {
        RpcInput<EsSearchInput> rpcInput = new RpcInput<>();
        EsSearchInput input = new EsSearchInput();
        if (realNamed) {
            input.setRealNameStatus(RealnameStatus.ACCEPT);
        }
        input.setCodeType(certType);
        input.setCodeValue(certNo);
        rpcInput.setInput(input);
        PagerResult<EasunEsearchAccountOutput> rpcOutput =
                rpcEsAccountService.getEsAccount(rpcInput).getData();
        if (CollectionUtils.isNotEmpty(rpcOutput.getItems())) {
            return rpcOutput.getItems().get(0);
        }
        return null;
    }

    public List<BizOrgBaseInfoOutput> getIcUserOrganList(String userOid) {
        try {
            BizGetIcUserOrgListInput input = new BizGetIcUserOrgListInput();
            input.setAccountId(userOid);
            RpcOutput<PagerResult<BizOrgBaseInfoOutput>> output =
                    rpcIcOrgPlusService.getIcUserOrganList(new RpcInput<>(input));
            return output == null || output.getData() == null ? null : output.getData().getItems();
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "getIcUserOrganList error");
            throw new SaasCommonBizException(ResultEnum.USER_CENTER_GET_USER_SUBJECTS_ERROR);
        }
    }

    /**
     * 批量删除用户
     *
     * @param orgOid
     * @param userOidList
     */
    public void batchDeleteMember(String orgOid, List<String> userOidList) {
        try {
            RpcInput<BizIcDeleteIcMemberBatchInput> input = new RpcInput<>();
            input.setInput(new BizIcDeleteIcMemberBatchInput(orgOid, userOidList));
            rpcIcOrgPlusService.deleteIcMemberBatch(input);
        } catch (BaseBizRuntimeException e) {
            throw e;
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "usercenter deleteIcMemberBatch error");
            throw new SaasCommonBizException(ORG_BATCH_DELETE_USER_ERROR);
        }
    }

    /**
     * 查询所有的数据权限
     * @return
     */
    public List<BizBuiltinPrivilegeDetail> getPrivileges() {
        try {
            RpcOutput<BizEasunGetBuiltinPrivilegesOutput> output =
                    rpcRolePrivilegePlusService.getPrivileges(new RpcInput<>());
            return output == null || output.getData() == null
                    ? Collections.emptyList()
                    : output.getData().getPrivilegeList();
        } catch (Exception e) {
            ExceptionLogUtil.traceLog(log, e, "usercenter getPrivileges error");
            throw new SaasCommonBizException(GET_USER_CENTER_PRIVILEGES_ERROR);
        }
    }

    private List<Property> buildProperty(String type, String value, List<Property> properties) {

        if (properties == null) {
            properties = new ArrayList<>();
        }
        if (StringUtils.isNotBlank(value)) {
            Property property = new Property();
            property.setType(type);
            property.setValue(value);
            properties.add(property);
        }
        return properties;
    }

    /**
     * 根据oid查询账号信息（es）
     *
     * @param oidList
     * @return[
     */
    public List<BizICUserOutput> getAccountByOidEs(List<String> oidList) {
        try {
            RpcInput<BizICUsersEasunGetInput> input = new RpcInput<>();
            BizICUsersEasunGetInput bizICUsersEasunGetInput = new BizICUsersEasunGetInput();
            bizICUsersEasunGetInput.setData(
                    oidList.stream().map(ICUserOpenId::new).collect(Collectors.toList()));
            bizICUsersEasunGetInput.setDeleteStatusEnum(DeleteStatusEnum.ALL);
            input.setInput(bizICUsersEasunGetInput);
            RpcOutput<List<BizICUserOutput>> output = rpcIcUserPlusService.getICUsersByEs(input);
            return output != null && output.getData() != null
                    ? output.getData()
                    : new ArrayList<>();
        } catch (Exception e) {
            log.warn("query user center es error", e);
            return new ArrayList<>();
        }
    }

    public List<RoleInfo> listCachedUserRoles(String subjectOid, String accountOid) {
        EasunGetIcUserRolesByOrgInput input = new EasunGetIcUserRolesByOrgInput();
        input.setAccountId(accountOid);
        input.setOrgId(subjectOid);
        try {
            RpcOutput<UserRoleOutput> rpcOutput =
                    rpcIcRolePrivilegePlusService.getUserRolesByCache(new RpcInput<>(input));

            return Optional.ofNullable(rpcOutput)
                    .map(RpcOutput::getData)
                    .map(UserRoleOutput::getRoleInfos)
                    .orElse(Collections.emptyList());
        } catch (Exception e) {
            log.warn("query user role list failed orgId: {} accountId: {}", subjectOid, accountOid, e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据oid查询所在企业列表（es）
     *
     * @param accountId
     * @return[
     */
    public PagerResult<OrganAccountDetail> getAccountOrgList(
            String accountId, Integer offset, Integer pageSize) {
        try {
            EsGetMembersByOidInput input = new EsGetMembersByOidInput();
            input.setMemberOid(accountId);
            if (null != offset) {
                input.setOffset(offset);
            }
            if (null != pageSize) {
                input.setSize(pageSize);
            }
            RpcOutput<PagerResult<OrganAccountDetail>> output =
                    rpcIcOrgPlusService.getMemberOrganList(new RpcInput<>(input));
            return output != null && output.getData() != null
                    ? output.getData()
                    : new PagerResult<>();
        } catch (Errors.AccOpenUserNotExistWith e) {
            throw new SaasCommonBizException(ACCOUNT_ID_INVALID);
        } catch (Exception e) {
            throw new SaasCommonBizException(ResultEnum.USER_CENTER_GET_USER_SUBJECTS_ERROR);
        }
    }

  /**
   * 根据gid和idCardKey查询三方id
   *
   * @param idCardKey
   * @return gid
   */
  public List<EsAccountIdcardInfoOutput> getAccountIdCardInfoByEs( String gid,String idCardKey) {
    if ( StringUtils.isBlank(gid)) {
      // 由于是es查询，参数如果缺少一个也会查询出来数据，不符合预期，需要提前判断下
      return Lists.newArrayList();
    }
    BizEsAccountIdcardSearchInput input = new BizEsAccountIdcardSearchInput();
    input.setGuid(gid);
    input.setIdCardKey(idCardKey);
    RpcOutput<PagerResult<EsAccountIdcardInfoOutput>> output =
        rpcEsAccountService.getAccountIdcardInfoByEs(new RpcInput<>(input));
    return output == null || output.getData() == null
        ? Collections.emptyList()
        : output.getData().getItems();
  }

}
