package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.saas.auth.api.facade.api.RpcDingInfoService;
import com.timevale.saas.auth.api.facade.req.corp.GetDingCorpPayerInfoReq;
import com.timevale.saas.auth.api.facade.result.corp.GetDingCorpPayerInfoRsp;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/9/20 钉签相关服务client类
 */
@Component
public class DingSignClient {

    @Autowired private RpcDingInfoService rpcDingInfoService;

    /**
     * 获取钉签crop付费方信息
     *
     * @param req 入参
     * @return 钉签crop付费方信息
     */
    public GetDingCorpPayerInfoRsp getDingCorpPayerInfo(GetDingCorpPayerInfoReq req) {
        return rpcDingInfoService.getDingCorpPayerInfo(req);
    }
}
