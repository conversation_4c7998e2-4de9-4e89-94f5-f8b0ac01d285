package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.bizcommon.result.notification.NotificationResultCodeEnum;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.notificationmanager.service.api.BizTemplateService;
import com.timevale.notificationmanager.service.enums.NotifyChannelTypeEnum;
import com.timevale.notificationmanager.service.model.Result;
import com.timevale.notificationmanager.service.model.optimize.ChannelTemplate;
import com.timevale.notificationmanager.service.model.optimize.response.BizTemplateDetailResponse;
import com.timevale.tpi.facade.api.wechat.RpcWechatOfficialService;
import com.timevale.tpi.facade.enums.WeChatClientTypeEnum;
import com.timevale.tpi.facade.enums.WeChatQrcodeTypeEnum;
import com.timevale.tpi.facade.req.wechat.WeChatOfficialQrcodeReq;
import com.timevale.tpi.facade.rsp.wechat.WeChatOfficialQrcodeRsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 *
 * @date 2022/5/31
 */
@Slf4j
@Component
public class ToolCenterClient {

    @Autowired RpcWechatOfficialService rpcWechatOfficialService;
    @Autowired BizTemplateService bizTemplateService;

    /**
     * 获取微信服务号二维码
     *
     * @param sceneId
     * @param sceneValue
     * @param expire
     * @return
     */
    public String getWeChatQrcode(String sceneId, String sceneValue, Long expire) {
        WeChatOfficialQrcodeReq req = new WeChatOfficialQrcodeReq();
        req.setSceneId(sceneId);
        req.setSceneValue(sceneValue);
        req.setExpireSeconds(expire);
        req.setClientType(WeChatClientTypeEnum.E_SERVICE.getClientType());
        req.setQrcodeType(WeChatQrcodeTypeEnum.QR_STR_SCENE.getScene());
        try {
            WeChatOfficialQrcodeRsp rsp = rpcWechatOfficialService.getQrcode(req);
            return rsp.getUrl();
        } catch (Exception e) {
            log.warn("生成微信动态二维码失败 message:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 查询消息中心短信模版
     * @param templateId
     * @return
     */
    public String getSMSTemplate(String templateId) {
        try {
            Result<BizTemplateDetailResponse> result =
                    bizTemplateService.getBizTemplateDetail(templateId);
            if (Objects.equals(result.getCode(), NotificationResultCodeEnum.SUCCESS.getNCode())
                    && result.getData() != null
                    && CollectionUtils.isNotEmpty(result.getData().getChannelTemplates())) {
                return result.getData().getChannelTemplates().stream()
                        .filter(p -> Objects.equals(p.getChannel(), NotifyChannelTypeEnum.MOBILE))
                        .map(ChannelTemplate::getContent)
                        .findFirst()
                        .orElse(null);
            }
        } catch (Exception e) {
            log.warn("获取消息中心短信模版失败 message:{}", e.getMessage());
        }
        return null;
    }
}
