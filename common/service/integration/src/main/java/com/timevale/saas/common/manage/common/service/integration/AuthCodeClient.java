package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.authcode.service.api.AuthCodeService;
import com.timevale.authcode.service.enums.CallTypeEnum;
import com.timevale.authcode.service.enums.SendTypeEnum;
import com.timevale.authcode.service.model.AuthV2Model;
import com.timevale.authcode.service.model.QueryModel;
import com.timevale.authcode.service.model.SendModel;
import com.timevale.authcode.service.result.AuthResult;
import com.timevale.authcode.service.result.QueryResult;
import com.timevale.authcode.service.result.RpcResponse;
import com.timevale.authcode.service.result.SendResult;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import com.timevale.saas.common.manage.common.service.integration.bo.SendAuthCodeBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 验证码客户端
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
@Slf4j
public class AuthCodeClient {

    @Autowired
    private AuthCodeService authCodeService;

    /**
     * 发送验证码
     *
     * @param sendAuthCodeBO
     * @return
     */
    public String sendAuthCode(SendAuthCodeBO sendAuthCodeBO) {
        SendModel sendModel = new SendModel();
        sendModel.setSendType(SendTypeEnum.SMS.name());
        sendModel.setReceiver(sendAuthCodeBO.getPhone());
        sendModel.setDisplayName(sendAuthCodeBO.getDisplayName());
        sendModel.setCallType(CallTypeEnum.DEFAULT);
        sendModel.setBizId(sendAuthCodeBO.getBizType());
        sendModel.setBizType(sendAuthCodeBO.getBizType());
        sendModel.setBizOperation(sendAuthCodeBO.getBizOperation());
        sendModel.setCodeExpire(sendAuthCodeBO.getCodeExpire());
        if (StringUtils.isNotBlank(sendAuthCodeBO.getTemplateId())) {
            sendModel.setTemplateId(sendAuthCodeBO.getTemplateId());
        }

        RpcResponse<SendResult> rpcResponse = authCodeService.send(sendModel);
        if (rpcResponse.isSuccess()) {
            return rpcResponse.getData().getAuthCodeId();
        } else {
            throw new SaasCommonBizException(ResultEnum.AUTH_CODE_SEND_FAIL, rpcResponse.getMessage());
        }
    }

    /**
     * 校验验证码
     *
     * @param authCodeId
     * @return
     */
    public boolean authCode(String authCodeId, String authCode) {
        AuthV2Model model = new AuthV2Model();
        model.setAuthCodeId(authCodeId);
        model.setAuthCode(authCode);
        RpcResponse<AuthResult> rpcResponse = authCodeService.authV2(model);
        if (rpcResponse.isSuccess()) {
            return rpcResponse.getData().isCorrect();
        } else {
            throw new SaasCommonBizException(ResultEnum.AUTH_CODE_FAIL, rpcResponse.getMessage());
        }
    }

    public QueryResult queryByAuthCodeId(String authCodeId) {
        QueryModel queryModel = new QueryModel();
        queryModel.setAuthCodeId(authCodeId);
        RpcResponse<QueryResult> rpcResponse = authCodeService.query(queryModel);
        if (rpcResponse.isSuccess()) {
            return rpcResponse.getData();
        } else {
            throw new SaasCommonBizException(ResultEnum.QUERY_AUTH_CODE_SEND_INFO_FAIL, rpcResponse.getMessage());
        }
    }
}
