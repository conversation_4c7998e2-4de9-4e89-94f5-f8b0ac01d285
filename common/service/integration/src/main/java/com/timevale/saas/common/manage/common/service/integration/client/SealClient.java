package com.timevale.saas.common.manage.common.service.integration.client;

import com.timevale.footstone.seal.facade.dto.request.v3.orgseal.GetOrgOwnSealListInput;
import com.timevale.footstone.seal.facade.dto.response.v3.orgseal.GetOrgOwnSealListOutput;
import com.timevale.footstone.seal.facade.openv3.OpenV3OrgSealFacade;
import com.timevale.footstone.seal.facade.saas.LegalPersonFacade;
import com.timevale.footstone.seal.facade.saas.RuleGrantFacade;
import com.timevale.footstone.seal.facade.saas.SealAuditFacade;
import com.timevale.footstone.seal.facade.saas.SealOrganizationFacade;
import com.timevale.footstone.seal.facade.saas.SealPlatformFacade;
import com.timevale.footstone.seal.facade.saas.SecondSealGrantFacade;
import com.timevale.footstone.seal.facade.saas.input.AddRulesInput;
import com.timevale.footstone.seal.facade.saas.input.BatchUpdateRuleGrantInput;
import com.timevale.footstone.seal.facade.saas.input.BindRuleGrantInput;
import com.timevale.footstone.seal.facade.saas.input.CancelLegalSealAuthInput;
import com.timevale.footstone.seal.facade.saas.input.ChangeNotifySettingInput;
import com.timevale.footstone.seal.facade.saas.input.CreateLegalTemplateSealInput;
import com.timevale.footstone.seal.facade.saas.input.CreateOfficialTemplateSealInput;
import com.timevale.footstone.seal.facade.saas.input.CreateOrgLegalRepImageInput;
import com.timevale.footstone.seal.facade.saas.input.CreateOrgSealImageInput;
import com.timevale.footstone.seal.facade.saas.input.CreateRuleGrantsInput;
import com.timevale.footstone.seal.facade.saas.input.DeleteByGrantedInput;
import com.timevale.footstone.seal.facade.saas.input.DeleteRuleGrantInput;
import com.timevale.footstone.seal.facade.saas.input.DeleteSealInput;
import com.timevale.footstone.seal.facade.saas.input.DeleteSecondSealGrantInput;
import com.timevale.footstone.seal.facade.saas.input.GrantFlowDownloadUrlInput;
import com.timevale.footstone.seal.facade.saas.input.GrantedDetailInput;
import com.timevale.footstone.seal.facade.saas.input.LegalPersonSignInput;
import com.timevale.footstone.seal.facade.saas.input.LegalSealAuthUrgeInput;
import com.timevale.footstone.seal.facade.saas.input.LegalTemplateDetailInput;
import com.timevale.footstone.seal.facade.saas.input.OffLineLegalAuthApplyInput;
import com.timevale.footstone.seal.facade.saas.input.OfficialTemplateDetailInput;
import com.timevale.footstone.seal.facade.saas.input.OrgSealNumsInput;
import com.timevale.footstone.seal.facade.saas.input.PreviewLegalTemplateSealInput;
import com.timevale.footstone.seal.facade.saas.input.PreviewOfficialTemplateSealInput;
import com.timevale.footstone.seal.facade.saas.input.QueryGrantedSealsInput;
import com.timevale.footstone.seal.facade.saas.input.QueryLegalAuthInput;
import com.timevale.footstone.seal.facade.saas.input.QuerySealPageInput;
import com.timevale.footstone.seal.facade.saas.input.QuerySealPersonalBySealsInput;
import com.timevale.footstone.seal.facade.saas.input.RecoverLegalAuthApplyInput;
import com.timevale.footstone.seal.facade.saas.input.RuleGrantCountInput;
import com.timevale.footstone.seal.facade.saas.input.RuleGrantCountV2Input;
import com.timevale.footstone.seal.facade.saas.input.SealAuditReasonInput;
import com.timevale.footstone.seal.facade.saas.input.SealCompressInput;
import com.timevale.footstone.seal.facade.saas.input.SealCreateConfigInput;
import com.timevale.footstone.seal.facade.saas.input.SealDetailInput;
import com.timevale.footstone.seal.facade.saas.input.SealGrantDownloadInput;
import com.timevale.footstone.seal.facade.saas.input.SealGrantRuleUpdateInput;
import com.timevale.footstone.seal.facade.saas.input.SecondSealGrantAddInput;
import com.timevale.footstone.seal.facade.saas.input.SecondSealGrantCountInput;
import com.timevale.footstone.seal.facade.saas.input.SecondSealGrantPageInput;
import com.timevale.footstone.seal.facade.saas.input.SecondSealGrantUpdateInput;
import com.timevale.footstone.seal.facade.saas.input.SetDefaultSealInput;
import com.timevale.footstone.seal.facade.saas.input.UpdateSealAliasInput;
import com.timevale.footstone.seal.facade.saas.output.AddRulesOutput;
import com.timevale.footstone.seal.facade.saas.output.BatchUpdateRuleGrantOutput;
import com.timevale.footstone.seal.facade.saas.output.BindRuleOutput;
import com.timevale.footstone.seal.facade.saas.output.CreateLegalTemplateSealOutput;
import com.timevale.footstone.seal.facade.saas.output.CreateOfficialTemplateSealOutput;
import com.timevale.footstone.seal.facade.saas.output.CreateOrgLegalRepImageOutput;
import com.timevale.footstone.seal.facade.saas.output.CreateOrgSealImageOutput;
import com.timevale.footstone.seal.facade.saas.output.CreateRuleGrantsOutput;
import com.timevale.footstone.seal.facade.saas.output.DeleteRuleGrantOutput;
import com.timevale.footstone.seal.facade.saas.output.GrantFlowDownloadUrlOutput;
import com.timevale.footstone.seal.facade.saas.output.GrantedSealsOutput;
import com.timevale.footstone.seal.facade.saas.output.LegalAuthApplyOutput;
import com.timevale.footstone.seal.facade.saas.output.LegalPersonSignOutput;
import com.timevale.footstone.seal.facade.saas.output.LegalSealAuthInfoOutput;
import com.timevale.footstone.seal.facade.saas.output.LegalSealAuthStatusOutput;
import com.timevale.footstone.seal.facade.saas.output.LegalTemplateDetailOutput;
import com.timevale.footstone.seal.facade.saas.output.OfficialTemplateDetailOutput;
import com.timevale.footstone.seal.facade.saas.output.OrgSealNumsOutput;
import com.timevale.footstone.seal.facade.saas.output.PreviewLegalTemplateSealOutput;
import com.timevale.footstone.seal.facade.saas.output.PreviewOfficialTemplateSealOutput;
import com.timevale.footstone.seal.facade.saas.output.RuleGrantByResourceIdOutput;
import com.timevale.footstone.seal.facade.saas.output.RuleGrantCountOutput;
import com.timevale.footstone.seal.facade.saas.output.SaasSealGrantSecondListOutput;
import com.timevale.footstone.seal.facade.saas.output.SealAuditImgOutput;
import com.timevale.footstone.seal.facade.saas.output.SealCommonInfoOutput;
import com.timevale.footstone.seal.facade.saas.output.SealCompressOutput;
import com.timevale.footstone.seal.facade.saas.output.SealCreateConfigOutput;
import com.timevale.footstone.seal.facade.saas.output.SealDetailOutput;
import com.timevale.footstone.seal.facade.saas.output.SealGrantDownloadOutput;
import com.timevale.footstone.seal.facade.saas.output.SealGrantSecondUpdateOutput;
import com.timevale.footstone.seal.facade.saas.output.SealPageOutput;
import com.timevale.footstone.seal.facade.saas.output.SecondSealGrantBatchAddOutput;
import com.timevale.footstone.seal.facade.saas.page.ApiPageResult;
import com.timevale.saas.common.manage.common.service.exception.SealExceptionConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/7/4 印章服务相关的client
 */
@Slf4j
@Component
public class SealClient {
    @Autowired private SealOrganizationFacade sealOrganizationFacade;
    @Autowired private LegalPersonFacade legalPersonFacade;
    @Autowired private RuleGrantFacade ruleGrantFacade;
    @Autowired private SealPlatformFacade sealPlatformFacade;
    @Autowired private SealAuditFacade sealAuditFacade;

    @Autowired
    private SecondSealGrantFacade secondSealGrantFacade;

    @Autowired
    private OpenV3OrgSealFacade openV3OrgSealFacade;

    /**
     * 获取企业印章列表
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_ORG_SEAL_LIST_ERROR)
    public SealPageOutput pageSeal(QuerySealPageInput input) {
        return sealOrganizationFacade.pageSeal(input);
    }

    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_ORG_SEAL_LIST_ERROR)
    public GetOrgOwnSealListOutput queryOrgSealListByBizTypes(GetOrgOwnSealListInput input) {
        return openV3OrgSealFacade.getOrgOwnSealList(input).getData();
    }

    /**
     * 根据印章id列表查询印章异常
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_SEAL_BY_SEAL_IDS_ERROR)
    public SealCommonInfoOutput queryBySealIds(QuerySealPersonalBySealsInput input) {
        return sealOrganizationFacade.queryBySealIds(input);
    }

    /**
     * 创建企业的模版印章
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.CREATE_OFFICIAL_TEMPLATE_SEAL_ERROR)
    public CreateOfficialTemplateSealOutput createOfficialTemplateSeal(
            CreateOfficialTemplateSealInput input) {
        return sealOrganizationFacade.createOfficialTemplateSeal(input);
    }

    /**
     * 创建企业图片印章
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.CREATE_ORG_IMG_SEAL_ERROR)
    public CreateOrgSealImageOutput createOrgSealImage(CreateOrgSealImageInput input) {
        return sealOrganizationFacade.createOrgSealImage(input);
    }

    /**
     * 设置企业默认印章
     *
     * @param orgOid
     * @param sealId
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.SET_DEFAULT_ORG_SEAL_ERROR,
            isNotNull = false)
    public void setDefaultSeal(SetDefaultSealInput input) {
        sealOrganizationFacade.setDefaultSealV2(input);
    }

    /**
     * 删除企业章
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.DELETE_ORG_SEAL_ERROR,
            isNotNull = false)
    public void deleteSeal(DeleteSealInput input) {
        sealOrganizationFacade.deleteSeal(input);
    }

    /**
     * 更新企业章别名
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.UPDATE_OFFICIAL_SEAL_ALIAS_ERROR,
            isNotNull = false)
    public void updateAlias(UpdateSealAliasInput input) {
        sealOrganizationFacade.updateOfficialSealAlias(input);
    }

    /**
     * 获取企业模板印章详情
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_OFFICIAL_TEMPLATE_DETAIL_ERROR)
    public OfficialTemplateDetailOutput getOfficialTemplateDetail(
            OfficialTemplateDetailInput input) {
        return sealOrganizationFacade.getOfficialTemplateDetail(input);
    }

    /**
     * 获取印章创建配置异常
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_SEAL_CREATE_CONFIG_ERROR)
    public SealCreateConfigOutput getSealCreateConfig(SealCreateConfigInput input) {
        return sealPlatformFacade.getSealCreateConfig(input);
    }

    /**
     * 获取印章审核拒绝原因
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_SEAL_AUDIT_REASON_ERROR)
    public SealAuditImgOutput getSealAuditReason(SealAuditReasonInput input) {
        return sealAuditFacade.getSealAuditReason(input);
    }

    /**
     * 获取法人章详情
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_LEGAL_TEMPLATE_DETAIL_ERROR)
    public LegalTemplateDetailOutput getLegalTemplateDetail(LegalTemplateDetailInput input) {
        return legalPersonFacade.getLegalTemplateDetail(input);
    }

    /**
     * 企业模版章预览
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.PREVIEW_OFFICIAL_SEAL_ERROR)
    public PreviewOfficialTemplateSealOutput previewOfficialTemplateSeal(
            PreviewOfficialTemplateSealInput input) {
        return sealOrganizationFacade.previewOfficialTemplateSeal(input);
    }

    /**
     * 法人章线上授权申请
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.ONLINE_LEGAL_SIGN_ERROR)
    public LegalPersonSignOutput createOnlineLegalSign(LegalPersonSignInput input) {
        return ruleGrantFacade.createOnlineLegalSign(input);
    }

    /**
     * 法人章线上授权签署催签
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.LEGAL_SEAL_AUTH_URGE_ERROR,
            isNotNull = false)
    public void legalSealAuthUrge(LegalSealAuthUrgeInput input) {
        ruleGrantFacade.legalSealAuthUrge(input);
    }

    /**
     * 法人章线下授权申请
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.OFFLINE_LEGAL_AUTH_APPLY_ERROR)
    public LegalAuthApplyOutput offLineLegalAuthApply(OffLineLegalAuthApplyInput input) {
        return ruleGrantFacade.offLineLegalAuthApply(input);
    }

    /**
     * 法人模版章创建
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.CREATE_LEGAL_TEMPLATE_SEAL_ERROR)
    public CreateLegalTemplateSealOutput createLegalTemplateSeal(
            CreateLegalTemplateSealInput input) {
        return legalPersonFacade.createLegalTemplateSeal(input);
    }

    /**
     * 法人图片章创建
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.CREATE_LEGAL_IMG_SEAL_ERROR)
    public CreateOrgLegalRepImageOutput createOrgLegalSealImage(CreateOrgLegalRepImageInput input) {
        return legalPersonFacade.createOrgLegalSealImage(input);
    }

    /**
     * 法人章预览
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.PREVIEW_LEGAL_SEAL_ERROR)
    public PreviewLegalTemplateSealOutput previewLegalTemplateSeal(
            PreviewLegalTemplateSealInput input) {
        return legalPersonFacade.previewLegalTemplateSeal(input);
    }

    /**
     * 查询法人章授权详情
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.QUERY_LEGAL_AUTH_DETAIL_ERROR)
    public LegalSealAuthInfoOutput queryLegalAuthInfo(QueryLegalAuthInput input) {
        return ruleGrantFacade.queryLegalAuthInfo(input);
    }

    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.QUERY_LEGAL_AUTH_INFO_ERROR)
    public LegalSealAuthStatusOutput queryLegalAuthInfoWithAuthOid(QueryLegalAuthInput input) {
        return ruleGrantFacade.queryLegalAuthInfoWithAuthOid(input);
    }

    /**
     * 撤销法人章授权 (包括线上和线下)
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.CANCEL_LEGAL_SEAL_AUTH_ERROR,
            isNotNull = false)
    public void cancelLegalSealAuth(CancelLegalSealAuthInput input) {
        ruleGrantFacade.cancelLegalSealAuth(input);
    }

    /**
     * 法人章授权重置
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.RECOVER_LEGAL_AUTH_APPLY_ERROR,
            isNotNull = false)
    public void recoverLegalAuthApply(RecoverLegalAuthApplyInput input) {
        ruleGrantFacade.recoverLegalAuthApply(input);
    }

    /**
     * 印章图片压缩、透明化处理
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.COMPRESS_SEAL_ERROR)
    public SealCompressOutput compressSeal(SealCompressInput input) {
        return sealPlatformFacade.compressSeal(input);
    }

    /** 印章授权相关 */

    /**
     * 查询被授权企业列表
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.QUERY_SEAL_GRANTED_ERROR)
    public GrantedSealsOutput queryGrantedSealsList(QueryGrantedSealsInput input) {
        return sealOrganizationFacade.queryGrantedSealsList(input);
    }

    /**
     * 授权的企业/成员信息
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.QUERY_SEAL_GRANT_ERROR)
    public RuleGrantByResourceIdOutput getRuleGrantByResourceId(GrantedDetailInput input) {
        return ruleGrantFacade.getRuleGrantByResourceId(input);
    }

    /**
     * 设置审批通知
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.CHANGE_NOTIFY_SETTING_ERROR,
            isNotNull = false)
    public void changeNotifySetting(ChangeNotifySettingInput input) {
        ruleGrantFacade.changeNotifySetting(input);
    }

    /**
     * 新增印章授权
     *
     * @param input
     * @see #addSealGrant
     */
    @Deprecated
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.ADD_RULE_GRANTS_ERROR)
    public CreateRuleGrantsOutput addRuleGrants(CreateRuleGrantsInput input) {
        return ruleGrantFacade.addRuleGrants(input);
    }

    /**
     * 新增印章授权新增二次授权
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.ADD_RULE_GRANTS_ERROR)
    public AddRulesOutput addSealGrant(AddRulesInput input) {
        return ruleGrantFacade.addSealGrant(input);
    }

    /**
     * 编辑印章授权
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.UPDATE_RULE_GRANT_ERROR)
    public BindRuleOutput updateRuleGrant(BindRuleGrantInput input) {
        return ruleGrantFacade.updateRuleGrant(input);
    }

    /**
     * 批量编辑印章授权
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.BATCH_UPDATE_RULE_GRANT_ERROR)
    public BatchUpdateRuleGrantOutput batchUpdateRuleGrant(BatchUpdateRuleGrantInput input) {
        return ruleGrantFacade.batchUpdateRuleGrant(input);
    }

    /**
     * 删除印章授权
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.DELETE_RULE_GRANT_ERROR)
    public DeleteRuleGrantOutput deleteRuleGrant(DeleteRuleGrantInput input) {
        return ruleGrantFacade.deleteRuleGrant(input);
    }

    /**
     * 被授权信息删除
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.DELETE_RULE_GRANT_ERROR,
            isNotNull = false)
    public void deleteByGranted(DeleteByGrantedInput input) {
        ruleGrantFacade.deleteByGranted(input);
    }

    /**
     * 获取印章授权书下载地址
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_GRANT_FLOW_DOWNLOAD_ERROR)
    public GrantFlowDownloadUrlOutput getGrantFlowDownloadUrl(GrantFlowDownloadUrlInput input) {
        return ruleGrantFacade.getGrantFlowDownloadUrl(input);
    }

    /**
     * 获取印章详情
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_SEAL_DETAIL_ERROR)
    public SealDetailOutput getSealDetail(SealDetailInput input) {
        return sealOrganizationFacade.getSealDetail(input);
    }

    /**
     * 获取企业印章数量
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_ORG_SEAL_COUNT_ERROR)
    public OrgSealNumsOutput getOrgSealCounts(OrgSealNumsInput input) {
        return sealOrganizationFacade.getCurrentOrgSealNums(input);
    }

    /**
     * 获取印章授权数量
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_SEAL_GRANT_COUNT_ERROR)
    public RuleGrantCountOutput getRuleGrantCount(RuleGrantCountInput input) {
        return ruleGrantFacade.getRuleGrantCount(input);
    }

    /*
     * 二次授权相关操作
     */

    /**
     * 新增二次授权
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.ADD_SECOND_GRANT_ERROR)
    public SecondSealGrantBatchAddOutput addGrantOfInternal(SecondSealGrantAddInput input) {
        return secondSealGrantFacade.addGrantOfInternal(input);
    }

    /**
     * 查询二次授权列表信息
     * 默认不需要印章服务鉴权
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_SECOND_GRANT_LIST_ERROR)
    public ApiPageResult<SaasSealGrantSecondListOutput> pageSecondSealGrant(
            SecondSealGrantPageInput input) {
        return secondSealGrantFacade.pageSecondSealGrant(input);
    }

    /**
     * 删除印章二次授权
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.DELETE_SECOND_GRANT_ERROR,
            isNotNull = false)
    public void deleteSecondSealGrantOfInternal(DeleteSecondSealGrantInput input) {
        secondSealGrantFacade.deleteSecondSealGrantOfInternal(input);
    }

    /**
     * 更新二次授权
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.UPDATE_SECOND_GRANT_ERROR)
    public SealGrantSecondUpdateOutput updateGrantOfInternal(SecondSealGrantUpdateInput input) {
        return secondSealGrantFacade.updateGrantOfInternal(input);
    }

    /**
     * 更新二次授权规则
     *
     * @param input
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.UPDATE_SECOND_GRANT_RULE_ERROR,
            isNotNull = false)
    public void updateSealGrantRule(SealGrantRuleUpdateInput input) {
        secondSealGrantFacade.updateSealGrantRule(input);
    }

    /**
     * 获取二级授权书下载地址
     *
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.GET_SECOND_GRANT_FLOW_DOWNLOAD_ERROR)
    public SealGrantDownloadOutput getSealGrantDownloadUrl(SealGrantDownloadInput input) {
        return secondSealGrantFacade.getSealGrantDownloadUrl(input);
    }

    /**
     * 催办印章
     *
     * @param sealId
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.IMAGE_URGE_AUDIT_ERROR,
            isNotNull = false)
    public void imageUrgeForAudit(String sealId) {
        sealOrganizationFacade.imageUrgeForAudit(sealId);
    }

    /**
     * 授权数量查询
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.COUNT_RULE_GRANT_ERROR)
    public Integer countRuleGrantCount(RuleGrantCountV2Input input) {
        return ruleGrantFacade.countRuleGrantCount(input);
    }

    /**
     * 计算二级授权数量
     * @param input
     * @return
     */
    @ICResultCheck(
            useResultEnum = false,
            errorCode = SealExceptionConstants.COMMON_ERROR_CODE,
            errorMsg = SealExceptionConstants.COUNT_SECOND_SEAL_GRANT_ERROR)
    public Integer countSecondSealGrant(SecondSealGrantCountInput input) {
        return secondSealGrantFacade.countSecondSealGrant(input);
    }
}
