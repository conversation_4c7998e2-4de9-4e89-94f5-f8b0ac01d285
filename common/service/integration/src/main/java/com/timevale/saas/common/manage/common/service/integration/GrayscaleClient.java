package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.gray.config.manage.service.api.GrayscaleRpcService;
import com.timevale.gray.config.manage.service.model.base.BaseResult;
import com.timevale.gray.config.manage.service.model.request.CheckGrayscaleFunctionRequest;
import com.timevale.gray.config.manage.service.model.request.RemoveGrayscaleFunctionRequest;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by tianlei on 2022/3/15
 */
@Slf4j
@Component
public class GrayscaleClient {

    @Autowired
    private GrayscaleRpcService grayscaleRpcService;

    public boolean checkGid(String gid, String projectKey, String functionKey) {
        if (StringUtils.isEmpty(gid)) {
            return false;
        }
        CheckGrayscaleFunctionRequest request = new CheckGrayscaleFunctionRequest();
        request.setUserId(gid);
        request.setGid(true);
        request.setProjectKey(projectKey);
        request.setFunctionKey(functionKey);
        BaseResult<Boolean> result = grayscaleRpcService.checkGrayscaleFunctionV2(request);
        if (!result.ifSuccess()) {
            log.error("grayscaleRpcService checkGrayscaleFunctionV2 req: {} res: {}",
                    JsonUtils.obj2json(request), JsonUtils.obj2json(result));
            throw new BaseBizRuntimeException(String.valueOf(result.getCode()), result.getMessage());
        }
        return Boolean.TRUE.equals(result.getData());
    }

    /**
     * 删除gid的灰度
     * @param gid
     * @param projectKey
     * @param functionKey
     */
    public void removeGrayscaleFunction(String gid, String projectKey, String functionKey){
        RemoveGrayscaleFunctionRequest request = new RemoveGrayscaleFunctionRequest();
        request.setGid(true);
        request.setUserId(gid);
        request.setProjectKey(projectKey);
        request.setFunctionKey(functionKey);
        grayscaleRpcService.removeGrayscaleFunction(request);
    }




}
