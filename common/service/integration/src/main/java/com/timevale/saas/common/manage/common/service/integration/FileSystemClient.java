package com.timevale.saas.common.manage.common.service.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.timevale.filesystem.common.service.api.FileSystemService;
import com.timevale.filesystem.common.service.query.BaseInput;
import com.timevale.filesystem.common.service.query.GetDownloadUrlInput;
import com.timevale.filesystem.common.service.query.GetSignUrlInput;
import com.timevale.filesystem.common.service.result.GetDownloadUrlResult;
import com.timevale.filesystem.common.service.result.GetFileInfoResult;
import com.timevale.filesystem.common.service.result.GetSignUrlResult;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.utils.http.RemoteHttpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;

@Slf4j
@Service
public class FileSystemClient {

    public static final String STREAM_OCTET = "application/octet-stream";

    /** OSS 目录名称 */
    @Value("${oss.bucket_name}")
    private static String OSS_BUCKET_NAME;

    /** OSS是否在内网 */
    @Value("${oss.isinside}")
    private static boolean OSS_ISINSIDE;

    @Value("${saas.downloadurl.expire}")
    private long downloadUrlExpire = 60 * 60 * 1000;

    @Autowired private FileSystemService fileSystemService;

    private GetDownloadUrlResult getDownloadUrl(GetDownloadUrlInput input) {

        GetDownloadUrlResult output;
        try {
            output = fileSystemService.getDownloadUrl(input);
        } catch (BaseRuntimeException e) {
            log.warn(
                    "file system upload fail, input:{}, output:{}.",
                    JsonUtils.obj2json(input),
                    e.getMessage());
            throw e;
        }
        return output;
    }

    public String uploadBytes(String fileName, String projectId, byte[] data) {

        long startTime = System.currentTimeMillis();
        GetSignUrlResult signUrlResult = getUploadUrl(fileName, projectId, true);
        String fileKey = signUrlResult.getFileKey();
        String uploadUrl = signUrlResult.getUrl();

        log.info("文件流上传地址获取完成， 开始上传");
        this.uploadFileToUrl(data, null, uploadUrl);

        log.info(
                " 上传文件流完成， fileName: {}, fileKey：{}, 耗时：{}ms",
                fileName,
                fileKey,
                (System.currentTimeMillis() - startTime));

        return fileKey;
    }

    public String uploadFile(String fileName, String projectId, File file) {
        long startTime = System.currentTimeMillis();
        GetSignUrlResult signUrlResult = getUploadUrl(fileName, projectId, true);
        String fileKey = signUrlResult.getFileKey();
        String uploadUrl = signUrlResult.getUrl();

        log.info("文件流上传地址获取完成， 开始上传");
        this.uploadFileToUrl(file, null, uploadUrl);

        log.info(
                " 上传文件流完成， fileName: {}, fileKey：{}, 耗时：{}ms",
                fileName,
                fileKey,
                (System.currentTimeMillis() - startTime));

        return fileKey;
    }

    private void uploadFileToUrl(byte[] data, String fileMd5, String uploadUrl) {
        try {
            long startTime = System.currentTimeMillis();
            JsonNode jsonNode =
                    RemoteHttpUtil.sendBytesToUrl(uploadUrl, data, STREAM_OCTET, fileMd5);
            if (jsonNode.get("errCode").asInt() != 0) {
                String msg = jsonNode.get("msg").asText();
                log.warn("上传出错", msg);
                throw new BaseRuntimeException(msg);
            }
            long endTime = System.currentTimeMillis();
            log.info(" 上传文件流花费时间：{} ms", (endTime - startTime));
        } catch (Exception e) {
            log.warn("上传出错");
            throw new BaseRuntimeException("文件上传异常", e);
        }
    }

    private void uploadFileToUrl(File file, String fileMd5, String uploadUrl) {
        try {
            long startTime = System.currentTimeMillis();
            JsonNode jsonNode =
                    RemoteHttpUtil.sendFileToUrl(uploadUrl, file, STREAM_OCTET, fileMd5);
            if (jsonNode.get("errCode").asInt() != 0) {
                String msg = jsonNode.get("msg").asText();
                log.warn("上传出错", msg);
                throw new BaseRuntimeException(msg);
            }
            long endTime = System.currentTimeMillis();
            log.info(" 上传文件流花费时间：{} ms", (endTime - startTime));
        } catch (Exception e) {
            log.warn("上传出错");
            throw new BaseRuntimeException("文件上传异常", e);
        }
    }

    private GetSignUrlResult getUploadUrl(String fileName, String projectId, Boolean internal) {
        GetSignUrlInput signUrlInput = new GetSignUrlInput();
        signUrlInput.setProjectId(projectId);
        // 服务商0-阿里云，1-百度云
        signUrlInput.setAgentType(0);
        signUrlInput.setFileName(fileName);
        signUrlInput.setContentType(STREAM_OCTET);
        signUrlInput.setMd5(null);
        signUrlInput.setBucketName(OSS_BUCKET_NAME);
        signUrlInput.setInternal(internal == null ? OSS_ISINSIDE : internal);
        log.info("开始获取文件上传地址， fileName: {}", fileName);
        return fileSystemService.getSignUrl(signUrlInput);
    }

    /**
     * 转化url，默认过期时间
     * @param fileKey
     * @return
     */
    public String convertUrl(String fileKey) {
        return convertUrl(fileKey, null);
    }

    /**
     * 转换url，自定义过期时间
     * @param fileKey
     * @param expireTime
     * @return
     */
    public String convertUrl(String fileKey, Long expireTime) {
        if (StringUtils.isBlank(fileKey)) {
            return null;
        }

        GetDownloadUrlInput input = new GetDownloadUrlInput();
        input.setFileKey(fileKey);
        if (expireTime != null){
            input.setExpire(expireTime);
        }else{
            input.setExpire(downloadUrlExpire);
        }
        input.setInternal(false);

        // 获取文件下载地址
        GetDownloadUrlResult getDownloadUrlResult;
        try {
            getDownloadUrlResult = getDownloadUrl(input);
        } catch (BaseRuntimeException e) {
            log.warn("get download url from file system failed. fileKey: {} ", input.getFileKey());
            throw new BaseRuntimeException("获取文件下载地址失败", e);
        }
        return getDownloadUrlResult.getUrl();
    }

    /**
     * 获取文件信息
     *
     * @param fileKey
     * @return
     */
    public GetFileInfoResult getFileInfo(String fileKey) {
        BaseInput input = new BaseInput();
        input.setFileKey(fileKey);
        return fileSystemService.getFileInfo(input);
    }
}
