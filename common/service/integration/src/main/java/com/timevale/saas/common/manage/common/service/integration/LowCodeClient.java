package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.besp.lowcode.integration.request.ThirdConfigBatchQueryRequest;
import com.timevale.besp.lowcode.integration.request.ThirdConfigUpdateRequest;
import com.timevale.besp.lowcode.integration.response.TenantConfigResponse;
import com.timevale.besp.lowcode.integration.third.ThirdConfigBaseRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-06-07
 */
@Slf4j
@Component
public class LowCodeClient {
  @Autowired private ThirdConfigBaseRpcService thirdConfigBaseRpcService;


  /**
   * 租户配置更新
   * @param platform
   * @param oid
   * @param tenantKey
   * @param configs
   */
  public void  updateConfig(String platform, String oid,String tenantKey,List<ThirdConfigUpdateRequest.ThirdConfigUpdate> configs){
    ThirdConfigUpdateRequest request=new ThirdConfigUpdateRequest();
    request.setTenantKey(tenantKey);
    request.setPlatform(platform);
    request.setOrgOuId(oid);
    request.setConfigs(configs);
    thirdConfigBaseRpcService.updateConfig(request);
  }


}
