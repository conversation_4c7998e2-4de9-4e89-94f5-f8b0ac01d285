package com.timevale.saas.common.manage.common.service.integration;

import com.alibaba.fastjson.JSON;
import com.timevale.account.organization.service.api.RpcOrgGroupService;
import com.timevale.account.organization.service.model.service.biz.input.BizAddSubOrganInput;
import com.timevale.account.organization.service.model.service.biz.input.BizCreateOrganGroupInput;
import com.timevale.account.organization.service.model.service.biz.input.BizDeleteOrganGroupInput;
import com.timevale.account.organization.service.model.service.biz.input.BizDeleteSubOrganInput;
import com.timevale.account.organization.service.model.service.biz.input.BizGetAffiliatedOrganGroupInput;
import com.timevale.account.organization.service.model.service.biz.input.BizGetOrganGroupInput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizGetAffiliatedOrganGroupOutput;
import com.timevale.account.organization.service.model.service.newbiz.output.BizGetOrganGroupOutput;
import com.timevale.easun.service.api.RpcMultipleOrgService;
import com.timevale.easun.service.model.organization.input.MultiFullOrgsInput;
import com.timevale.easun.service.model.organization.input.MultipleOrganInput;
import com.timevale.easun.service.model.organization.input.MultipleRemoveAllInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcInput;
import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Created by tianlei on 2022/3/2
 */
@Slf4j
@Component
public class OrgGroupClient {

    @Autowired
    private RpcOrgGroupService rpcOrgGroupService;

    @Autowired
    private RpcMultipleOrgService rpcMultipleOrgService;

    // 添加为组企业
    public void createOrganGroup(String tenantOid) {
        BizCreateOrganGroupInput input = new BizCreateOrganGroupInput();
        input.setOrganOuid(tenantOid);
        RpcOutput<?> result =
                rpcOrgGroupService.createOrganGroup(new RpcInput<>(input));
        if (!result.isSuccess()) {
            log.error("rpcOrgGroupService createOrganGroup input: {} result: {}",
                    JSON.toJSONString(input), JSON.toJSONString(result));
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()), result.getMessage());
        }
    }

    // 取消为授权企业
    public void deleteOrganGroup(String tenantOid) {
        BizDeleteOrganGroupInput input = new BizDeleteOrganGroupInput();
        input.setOrganOuid(tenantOid);
        RpcOutput<?> result =
                rpcOrgGroupService.deleteOrganGroup(new RpcInput<>(input));
        if (!result.isSuccess()) {
            log.error("rpcOrgGroupService deleteOrganGroup input: {} result: {}",
                    JSON.toJSONString(input), JSON.toJSONString(result));
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()), result.getMessage());
        }
    }

    /**
     * 查询是不是主企业
     */
    public BizGetOrganGroupOutput getOrganGroup(String tenantOid) {

        BizGetOrganGroupInput input = new BizGetOrganGroupInput();
        input.setOrganOuid(tenantOid);
        RpcOutput<BizGetOrganGroupOutput> result =
                rpcOrgGroupService.getOrganGroup(new RpcInput<>(input));
        if (!result.isSuccess()) {
            log.error("rpcOrgGroupService getOrganGroup tenantOid: {} result: {}",
                    tenantOid, JSON.toJSONString(result));
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()), result.getMessage());
        }
        return result.getData();
    }


    /**
     * 新增子企业
     */
    public void addSubOrgan(String parentTenantOid, String childTenantOid) {
        BizAddSubOrganInput input = new BizAddSubOrganInput();
        input.setOrganOuid(parentTenantOid);
        input.setSubOrganOuid(childTenantOid);
        RpcOutput<?> result =
                rpcOrgGroupService.addSubOrgan(new RpcInput<>(input));
        if (!result.isSuccess()) {
            log.error("rpcOrgGroupService addSubOrgan input: {} result: {}",
                    JSON.toJSONString(input), JSON.toJSONString(result));
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()), result.getMessage());
        }
    }


    /**
     * 删除子企业
     */
    public void deleteSubOrgan(String parentTenantOid, String childTenantOid) {
        BizDeleteSubOrganInput input = new BizDeleteSubOrganInput();
        input.setOrganOuid(parentTenantOid);
        input.setSubOrganOuid(childTenantOid);
        RpcOutput<?> result =
                rpcOrgGroupService.deleteSubOrgan(new RpcInput<>(input));
        if (!result.isSuccess()) {
            log.error("rpcOrgGroupService deleteSubOrgan input: {} result: {}",
                    JSON.toJSONString(input), JSON.toJSONString(result));
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()), result.getMessage());
        }
    }


    // 查询关联企业的 主企业oid
    public String getAffiliatedOrganGroup(String childTenantOid) {
        BizGetAffiliatedOrganGroupInput input = new BizGetAffiliatedOrganGroupInput();
        input.setSubOrganOuid(childTenantOid);
        RpcOutput<BizGetAffiliatedOrganGroupOutput> result =
                rpcOrgGroupService.getAffiliatedOrganGroup(new RpcInput<>(input));
        if (!result.isSuccess()) {
            log.error("rpcOrgGroupService getAffiliatedOrganGroup childTenantOid: {} result: {}",
                    childTenantOid, JSON.toJSONString(result));
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()), result.getMessage());
        }
        return Optional.ofNullable(result.getData()).map(BizGetAffiliatedOrganGroupOutput::getOrganOuid)
                .orElse(null);
    }

    public Boolean addAuthSubOrg(String authTenantOid, String parentTenantOid, String childTenantOid) {
        RpcInput<MultipleOrganInput> input = new RpcInput<>();
        MultipleOrganInput organInput = new MultipleOrganInput();
        organInput.setRootOrganOid(authTenantOid);
        organInput.setParentOrganOid(parentTenantOid);
        organInput.setBranchOrganOid(childTenantOid);
        input.setInput(organInput);
        return rpcMultipleOrgService.addAuthSubOrg(input).getData();
    }

    public Boolean removeAuthSubOrg(String authTenantOid, String parentTenantOid, String childTenantOid) {
        RpcInput<MultipleOrganInput> input = new RpcInput<>();
        MultipleOrganInput organInput = new MultipleOrganInput();
        organInput.setRootOrganOid(authTenantOid);
        organInput.setParentOrganOid(parentTenantOid);
        organInput.setBranchOrganOid(childTenantOid);
        input.setInput(organInput);
        return rpcMultipleOrgService.removeAuthSubOrg(input).getData();
    }

    public Boolean pushFullAuthOrgs(RpcInput<MultiFullOrgsInput> input) {
        return rpcMultipleOrgService.pushFullAuthOrgs(input).getData();
    }

    public Boolean relieveAllRelation(String authTenantOid) {
        if (StringUtils.isEmpty(authTenantOid)) {
            return false;
        }
        RpcInput<MultipleRemoveAllInput> input = new RpcInput<>();
        MultipleRemoveAllInput organInput = new MultipleRemoveAllInput();
        organInput.setRootOrganOid(authTenantOid);
        input.setInput(organInput);
        return rpcMultipleOrgService.relieveAllRelation(input).getData();
    }

    public Boolean hasRelation(String authTenantOid) {
        RpcInput<MultipleRemoveAllInput> input = new RpcInput<>();
        MultipleRemoveAllInput removeAllInput = new MultipleRemoveAllInput();
        removeAllInput.setRootOrganOid(authTenantOid);
        input.setInput(removeAllInput);
        return rpcMultipleOrgService.hasRelation(input).getData();
    }

}
