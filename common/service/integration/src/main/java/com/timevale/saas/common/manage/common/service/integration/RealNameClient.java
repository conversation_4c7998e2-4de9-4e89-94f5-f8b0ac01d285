package com.timevale.saas.common.manage.common.service.integration;

import com.timevale.esign.platform.toolkit.utils.rpc.RpcOutput;
import com.timevale.footstone.identity.common.service.api.common.QueryService;
import com.timevale.footstone.identity.common.service.response.IdentityAuthDetailResponse;
import com.timevale.infoauth.service.api.InfoQueryService;
import com.timevale.infoauth.service.request.query.FuzzyQueryEnterpriseInformationRequest;
import com.timevale.infoauth.service.response.query.FuzzyQueryEnterpriseInformationDTO;
import com.timevale.infoauth.service.response.query.FuzzyQueryEnterpriseInformationResponse;
import com.timevale.footstone.base.model.response.BaseResult;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-01
 */
@Service
public class RealNameClient {

    @Autowired InfoQueryService infoQueryService;

    @Autowired QueryService queryService;

    /**
     * 根据企业名称查询企业列表， 每次最多10条， 循环多次查询所有。
     * @param orgName
     */
    public List<FuzzyQueryEnterpriseInformationDTO> queryOrgInfosByName(String appId, String orgName) {

        FuzzyQueryEnterpriseInformationRequest request = new FuzzyQueryEnterpriseInformationRequest();
        request.setKeyword(orgName);
        request.setAppId(appId);
        RpcOutput<FuzzyQueryEnterpriseInformationResponse> rpcOutput;
        request.setPageIndex(1);
        rpcOutput = infoQueryService.fuzzyQueryEnterpriseInformation(request);

        return rpcOutput.getData().getEnterpriseInformationResultList();
    }

    /**
     * 查询实名流程详情
     *
     * @param flowId
     * @return
     */
    public IdentityAuthDetailResponse queryDetail(String flowId) {
        BaseResult<IdentityAuthDetailResponse> result = queryService.queryDetail(flowId);
        if (result.ifSuccess() && result.getData() != null) {
            return result.getData();
        }
        return null;
    }
}
