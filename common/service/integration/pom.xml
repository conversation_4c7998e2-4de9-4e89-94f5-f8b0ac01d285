<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale.saas-common-manage</groupId>
		<artifactId>saas-common-manage-parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../../../pom.xml</relativePath>
	</parent>

	<artifactId>saas-common-manage-common-service-integration</artifactId>
	<name>saas-common-manage/integration</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>saas-common-manage-common-util</artifactId>
			<version>${project.version}</version>
		</dependency>

		<!-- 文件系统 -->
		<dependency>
			<groupId>com.timevale.filesystem</groupId>
			<artifactId>filesystem-facade</artifactId>
			<version>2.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.account</groupId>
			<artifactId>account-facade</artifactId>
		</dependency>

		<!-- 用户中心 -->
		<dependency>
			<groupId>com.timevale.easun</groupId>
			<artifactId>easun-facade</artifactId>
			<version>3.4.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.account</groupId>
			<artifactId>account-realname-facade</artifactId>
			<version>2.1.2-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.account</groupId>
			<artifactId>account-organization-facade</artifactId>
			<version>2.6.7-SNAPSHOT</version>
		</dependency>

		<!--用户联系人 -->
		<dependency>
			<groupId>com.timevale.user.resource</groupId>
			<artifactId>user-resource-facade</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<artifactId>account-facade</artifactId>
					<groupId>com.timevale.account</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>saas-common-manage-common-service-facade</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.billing.promotion</groupId>
			<artifactId>billing-promotion-common-service-facade</artifactId>
			<version>1.6.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.billing.manager</groupId>
			<artifactId>basicbs-billing-manager-sdk</artifactId>
			<version>1.7.43-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.billing.manager</groupId>
			<artifactId>basicbs-billing-manager-facade</artifactId>
			<version>1.4.8-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.infoauth</groupId>
			<artifactId>infoauth-facade</artifactId>
			<version>1.4.5-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.gray-config-manage</groupId>
			<artifactId>gray-config-manage-facade</artifactId>
			<version>${gray-config-manage-facade-version}</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.tpi</groupId>
			<artifactId>third-platform-integrate-facade</artifactId>
			<version>1.0.5-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.seal</groupId>
			<artifactId>seal-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.timevale.saas-auth-api</groupId>
			<artifactId>saas-auth-api-facade</artifactId>
			<version>2.2.18-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.cert</groupId>
			<artifactId>cert-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.timevale.authcode</groupId>
			<artifactId>authcode-facade</artifactId>
			<version>2.0.8-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.notificationmanager</groupId>
			<artifactId>notificationmanager-facade</artifactId>
			<version>3.1.17-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.footstone</groupId>
			<artifactId>footstone-identity-common-service-facade</artifactId>
			<version>2.3.29-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.shortlink</groupId>
			<artifactId>shortlink-common-service-facade</artifactId>
			<version>2.0.8-RELEASE</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.saas</groupId>
			<artifactId>common-util</artifactId>
			<version>${common-util.version}</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.saas-auth-api</groupId>
            <artifactId>ding-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.besp.lowcode.tripartite</groupId>
            <artifactId>tripartite-rpc-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>basicbs-product-inventory-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.dayu</groupId>
            <artifactId>config-facade</artifactId>
        </dependency>
    </dependencies>
</project>
