<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale</groupId>
		<artifactId>mandarin-bom</artifactId>
		<version>2.13.2</version>
	</parent>

	<groupId>com.timevale.saas-common-manage</groupId>
	<artifactId>saas-common-manage-parent</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>

	<name>saas-common-manage/Parent</name>
	<url>https://www.tsign.cn/</url>
	<description>saas-common-manage Application Parent</description>

	<modules>
		<module>common/dal</module>
		<module>common/util</module>
		<module>common/service/facade</module>
		<module>common/service/integration</module>
		<module>core/model</module>
		<module>core/service</module>
		<module>biz/shared</module>
		<module>biz/service-impl</module>
		<module>biz/task</module>
		<module>spi/invoker</module>
		<module>spi/extender</module>
		<module>deploy</module>
    </modules>

	<properties>
		<swagger2.version>2.8.0</swagger2.version>
		<tgtest.base.version>1.2-SNAPSHOT</tgtest.base.version>
		<launcher-support-spring.version>1.1.0</launcher-support-spring.version>
		<gray-config-manage-facade-version>1.0.2-SNAPSHOT</gray-config-manage-facade-version>
		<cert.facade.version>2.4.56</cert.facade.version>
		<seal.fcade.version>1.3.21-SNAPSHOT</seal.fcade.version>
		<dayu.fcade.version>1.2.1-SNAPSHOT</dayu.fcade.version>
		<dayu-sdk.verison>1.0.24-SNAPSHOT</dayu-sdk.verison>
		<common-util.version>1.0.13-SNAPSHOT</common-util.version>
		<sealmanager.common.service.facade>3.5.20-SNAPSHOT</sealmanager.common.service.facade>
		<multilingual-translate-version>0.0.5-SNAPSHOT</multilingual-translate-version>
		<ding.starter.version>1.2.0-SNAPSHOT</ding.starter.version>
		<lowcode.tripartite.version>0.0.1-SNAPSHOT</lowcode.tripartite.version>
		<account.facade.version>2.2.20-SNAPSHOT</account.facade.version>
		<basicbs.product.version>5.0.9-SNAPSHOT</basicbs.product.version>
	</properties>

	<dependencyManagement>
		<dependencies>

			<!-- 插件化 -->
			<dependency>
				<groupId>com.timevale.framework.pluginify</groupId>
				<artifactId>launcher-support-spring</artifactId>
				<version>${launcher-support-spring.version}</version>
			</dependency>

			<dependency>
				<groupId>com.timevale.spring.boot</groupId>
				<artifactId>elock-spring-boot-starter</artifactId>
				<version>1.2.8</version>
			</dependency>
			<dependency>
				<groupId>kms-sdk</groupId>
				<artifactId>kmssdk-spring-boot-starter</artifactId>
				<version>0.0.6</version>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger2</artifactId>
				<version>${swagger2.version}</version>

				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-context</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-context-support</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-aop</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-tx</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-orm</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-jdbc</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-web</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-webmvc</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-oxm</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>io.springfox</groupId>
				<artifactId>springfox-swagger-ui</artifactId>
				<version>${swagger2.version}</version>
				<exclusions>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-core</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-beans</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-context</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-context-support</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-aop</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-tx</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-orm</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-jdbc</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-web</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-webmvc</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.springframework</groupId>
						<artifactId>spring-oxm</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.github.xiaoymin</groupId>
				<artifactId>swagger-bootstrap-ui</artifactId>
				<version>1.9.1</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.seal</groupId>
				<artifactId>seal-facade</artifactId>
				<version>${seal.fcade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.sealmanager</groupId>
				<artifactId>sealmanager-common-service-facade</artifactId>
				<version>${sealmanager.common.service.facade}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.cert</groupId>
				<artifactId>cert-facade</artifactId>
				<version>${cert.facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.dayu</groupId>
				<artifactId>dayu-facade</artifactId>
				<version>${dayu.fcade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.dayu</groupId>
				<artifactId>sdk</artifactId>
				<version>${dayu-sdk.verison}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.dayu</groupId>
				<artifactId>config-facade</artifactId>
				<version>1.0.7-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.authcode</groupId>
				<artifactId>authcode-facade</artifactId>
				<version>2.0.8-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>timevale-platform.toolkit</groupId>
						<artifactId>timevale-platform.toolkit.facade-utils</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>com.timevale.saas</groupId>
				<artifactId>tracking-util</artifactId>
				<version>1.0.1-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.saas</groupId>
				<artifactId>common-util</artifactId>
				<version>${common-util.version}</version>
			</dependency>
			<!--多语言工具包-->
			<dependency>
				<groupId>com.timevale.saas-utils</groupId>
				<artifactId>multilingual-translate-util</artifactId>
				<version>${multilingual-translate-version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>2.2.10</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>4.1</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.saas-auth-api</groupId>
				<artifactId>ding-spring-boot-starter</artifactId>
				<version>${ding.starter.version}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.besp.lowcode.tripartite</groupId>
				<artifactId>tripartite-rpc-facade</artifactId>
				<version>${lowcode.tripartite.version}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale.account</groupId>
				<artifactId>account-facade</artifactId>
				<version>${account.facade.version}</version>
			</dependency>
			<dependency>
				<groupId>com.timevale</groupId>
				<artifactId>basicbs-product-inventory-facade</artifactId>
				<version>${basicbs.product.version}</version>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>
