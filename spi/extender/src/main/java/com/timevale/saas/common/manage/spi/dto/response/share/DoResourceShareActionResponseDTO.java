package com.timevale.saas.common.manage.spi.dto.response.share;

import com.timevale.mandarin.common.result.ToString;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020/11/27
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DoResourceShareActionResponseDTO extends ToString {

    /*资源发起人*/
    private String initiatorAccountId;

    /** 资源发起主体 */
    private String initiatorSubjectId;

    /** 分享标题 */
    private String shareTitle;

    /** 分享标题值 不携带前缀 */
    private String shareTitleValue;

    /** 资源名称 */
    private String resourceName;

    /** 跳过实名 */
    private Boolean skipRealName;

    /** 操作人名称 */
    private String resourceOperatorName;
}
