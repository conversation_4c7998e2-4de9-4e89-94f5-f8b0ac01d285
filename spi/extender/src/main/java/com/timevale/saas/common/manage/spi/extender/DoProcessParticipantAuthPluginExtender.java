package com.timevale.saas.common.manage.spi.extender;

import com.timevale.framework.pluginify.launcher.api.PluginifyLauncherRegister;
import com.timevale.saas.common.manage.spi.dto.request.share.DoProcessParticipantAuthRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoProcessParticipantAuthResponseDTO;

/**
 * 插件执行流程参与人鉴权扩展点
 * @Author:jianyang
 * @since 2021-07-08 15:15
 */
@PluginifyLauncherRegister
public interface DoProcessParticipantAuthPluginExtender {
	/**
	 *
	 * @param requestDTO
	 * @return
	 */
	DoProcessParticipantAuthResponseDTO doProcessParticipantAuth(
			DoProcessParticipantAuthRequestDTO requestDTO);
}
