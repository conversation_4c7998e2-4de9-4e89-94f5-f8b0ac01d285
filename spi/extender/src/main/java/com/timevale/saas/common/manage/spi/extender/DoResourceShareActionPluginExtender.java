package com.timevale.saas.common.manage.spi.extender;

import com.timevale.framework.pluginify.launcher.api.PluginifyLauncherRegister;
import com.timevale.saas.common.manage.spi.dto.request.share.DoResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoResourceShareActionResponseDTO;

/**
 * 执行资源分享行为插件化扩展点定义
 *
 * <AUTHOR>
 * @since 2020/11/27
 */
@PluginifyLauncherRegister
public interface DoResourceShareActionPluginExtender {

    /**
     * 执行各资源分享行为
     *
     * @param request 获取资源的参数
     * @return 返回信息, 只有正确的时候返回,其他情况抛异常
     */
    DoResourceShareActionResponseDTO doResourceShareAction(DoResourceShareActionRequestDTO request);
}
