package com.timevale.saas.common.manage.spi.extender;

import com.timevale.framework.pluginify.launcher.api.PluginifyLauncherRegister;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceShareActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceShareActionResponseDTO;

/**
 * 执行获取资源分享行为插件扩展点
 *
 * <AUTHOR>
 * @since 2020/12/9
 */
@PluginifyLauncherRegister
public interface DoGetResourceShareActionPluginExtender {

    /**
     * 执行获取资源分享行为
     *
     * @param request 传递的参数
     * @return 返回的执行结果
     */
    DoGetResourceShareActionResponseDTO doGetResourceShareAction(
            DoGetResourceShareActionRequestDTO request);
}
