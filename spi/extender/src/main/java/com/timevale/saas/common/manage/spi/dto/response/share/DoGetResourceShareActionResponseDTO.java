package com.timevale.saas.common.manage.spi.dto.response.share;

import com.timevale.mandarin.common.result.ToString;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 执行获取资源分享信息插件调用返回
 *
 * <AUTHOR>
 * @since 2020/12/9
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DoGetResourceShareActionResponseDTO extends ToString {

    /** 是否允许修改资源分享配置 同时标识这是否有权限进行分享 */
    private boolean canChangeShareConfig;
}
