package com.timevale.saas.common.manage.spi.dto.request.share;

import com.timevale.mandarin.common.result.ToString;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020/12/9
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BaseDoResourceShareActionRequestDTO extends ToString {

    /** 资源id */
    private String resourceId;

    /** 请参考{@link com.timevale.saas.common.manage.common.service.enums.share.ResourceTypeEnum } */
    private String resourceType;
    /**
     * 分享操作类型 请参考{@link
     * com.timevale.saas.common.manage.common.service.enums.share.ShareOperateTypeEnum}
     */
    private String shareOperateType;
    /** 当前操作人oid */
    private String accountId;
    /** 当前操作人gid */
    private String accountGid;
    /** 当前操作主体oid */
    private String subjectId;
    /** 当前操作主体gid */
    private String subjectGid;

    /**
     * 调用来源,1:经办合同,2:企业合同
     */
    private String callSource;

    /**
     * 分类id
     */
    private String menuId;

    /** 分享类型1:分享给合同参与人,2:分享给非合同参与人 **/
    private Integer shareType;

    /**
     * 操作人名称
     */
    private String resourceOperatorName;
}
