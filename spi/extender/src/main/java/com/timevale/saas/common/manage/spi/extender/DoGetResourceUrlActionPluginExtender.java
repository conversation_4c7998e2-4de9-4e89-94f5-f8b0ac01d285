package com.timevale.saas.common.manage.spi.extender;

import com.timevale.framework.pluginify.launcher.api.PluginifyLauncherRegister;
import com.timevale.saas.common.manage.spi.dto.request.share.DoGetResourceUrlActionRequestDTO;
import com.timevale.saas.common.manage.spi.dto.response.share.DoGetResourceUrlActionResponseDTO;

/**
 * 插件执行获取资源最终地址扩展点
 *
 * <AUTHOR>
 * @since 2020/12/10
 */
@PluginifyLauncherRegister
public interface DoGetResourceUrlActionPluginExtender {

    /**
     * 执行获取资源最终地址
     *
     * @param request 请求入参
     * @return 资源地址等信息
     */
    DoGetResourceUrlActionResponseDTO doGetResourceUrlAction(
            DoGetResourceUrlActionRequestDTO request);
}
