package com.timevale.saas.common.manage.spi.dto.response.share;

import com.timevale.mandarin.common.result.ToString;

import lombok.Getter;
import lombok.Setter;

/**
 * 插件执行获取资源最终地址返回结果
 *
 * <AUTHOR>
 * @since 2020/12/10
 */
@Getter
@Setter
public class DoGetResourceUrlActionResponseDTO extends ToString {
    /** 资源地址 */
    private String resourceUrl;

    private Ext ext;

    @Getter
    @Setter
    public static final class Ext {

        /** 资源状态 */
        private String resourceStatus;
        /** 子资源id */
        private String subResourceId;
    }
}
