<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>saas-common-manage-parent</artifactId>
        <groupId>com.timevale.saas-common-manage</groupId>
        <version>1.0.0</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>saas-common-manage-spi-extender</artifactId>
    <name>saas-common-manage-spi/extender</name>
    <packaging>jar</packaging>
    <version>1.0.0</version>

    <dependencies>
        <dependency>
            <groupId>com.timevale</groupId>
            <artifactId>mandarin-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <!--swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <!-- 插件化 -->
        <dependency>
            <groupId>com.timevale.framework.pluginify</groupId>
            <artifactId>launcher-support-spring</artifactId>
        </dependency>
    </dependencies>

</project>