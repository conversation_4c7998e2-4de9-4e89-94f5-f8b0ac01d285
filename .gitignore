.metadata/
*.class
classes/
Servers/
node_modules/
**/static/bundles/**
bin/
*.bak
*.vm
#svn
.svn/
# built application files
*.apk
*.ap_

# files for the dex VM
*.dex

# Java class files

# generated files
gen/
.settings/
target/
ide/


# Local configuration file (sdk path, etc)
local.properties

# Eclipse project files
*.classpath
*.project
*.prefs
*.log

# Proguard folder generated by Eclipse
proguard/

# Intellij project files
*.iml
*.ipr
*.iws
.idea/
*.war
*.mymetadata
META-INF/
*.checkstyle
code/mq_backup_db
code/mandarin

##Inteilij IDEA plugin
#jrebel( hot deployment )
rebel.xml
rebel-remote.xml

*.tern-project
config/
deploy/config
deploy/allure-results
deploy/mandarin
mandarin/
mq_backup_db/
**/metadata/*.reload
.DS_Store
logs/**