<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent>
		<groupId>com.timevale.saas-common-manage</groupId>
		<artifactId>saas-common-manage-parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>saas-common-manage-core-model</artifactId>
	<name>saas-common-manage/model</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>saas-common-manage-common-service-integration</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>saas-common-manage-common-service-facade</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.saas</groupId>
			<artifactId>common-util</artifactId>
		</dependency>
		<!--对象映射转换-->
		<dependency>
			<groupId>ma.glasnost.orika</groupId>
			<artifactId>orika-core</artifactId>
			<version>1.5.2</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--多语言工具包-->
		<dependency>
			<groupId>com.timevale.saas-utils</groupId>
			<artifactId>multilingual-translate-util</artifactId>
		</dependency>
	</dependencies>
</project>
