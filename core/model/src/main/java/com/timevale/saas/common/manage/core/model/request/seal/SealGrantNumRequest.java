package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 印章授权数量查询入参
 *
 * <AUTHOR>
 * @since 2022-09-20 15:17
 */
@Data
public class SealGrantNumRequest extends ToString {

    @ApiModelProperty(name = "sealId", value = "印章id")
    private String sealId;

    @ApiModelProperty(name = "orgId", value = "操作主体oid")
    private String orgId;

    @ApiModelProperty(name = "sealGrantBizId", value = "一级授权业务id")
    private String sealGrantBizId;

    @ApiModelProperty(name = "grantLevel", value = "授权等级：1.一级授权 2.二级授权", required = true)
    @NotNull(message = "授权等级：1.一级授权 2.二级授权")
    private Integer grantLevel;
    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

}
