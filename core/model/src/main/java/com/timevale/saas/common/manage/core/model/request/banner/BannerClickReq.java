package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2021-05-31 17:56
 **/
@ApiModel("banner点击请求信息")
public class BannerClickReq extends ToString {

    @ApiModelProperty(value = "产品端编码", required = true)
    @NotBlank(message = "产品端编码不能为空")
    private String  productCode;

    @ApiModelProperty(value = "位置编码", required = true)
    @NotBlank(message = "位置编码不能为空")
    private String  areaCode;

    @ApiModelProperty(value = "banner唯一标示", required = true)
    @NotNull(message = "banner唯一标示不能为空")
    private Long bannerId;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Long getBannerId() {
        return bannerId;
    }

    public void setBannerId(Long bannerId) {
        this.bannerId = bannerId;
    }
}
