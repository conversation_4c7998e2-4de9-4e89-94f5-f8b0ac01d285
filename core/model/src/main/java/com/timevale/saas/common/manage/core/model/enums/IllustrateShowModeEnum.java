package com.timevale.saas.common.manage.core.model.enums;

import lombok.Getter;

/**
 * TODO 介绍展示模式
 *
 * <AUTHOR>
 * @since 2022/11/22
 */
@Getter
public enum IllustrateShowModeEnum {
    IMG(1, "图片"),
    VIDEO(2, "视频");
    private final int code;
    private final String desc;

    IllustrateShowModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static IllustrateShowModeEnum convert(Integer code) {
        if (code == null) {
            return null;
        }
        for (IllustrateShowModeEnum illustrateShowModeEnum : values()) {
            if (illustrateShowModeEnum.getCode() == code) {
                return illustrateShowModeEnum;
            }
        }
        return null;
    }
}
