package com.timevale.saas.common.manage.core.model.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@ApiModel("获取失败子任务列表请求参数")
public class QueryFailedTaskRequest extends ToString {

    @NotBlank(message = "用户账号oid不能为空")
    @ApiModelProperty("用户账号oid")
    private String accountId;

    @NotBlank(message = "主任务id不能为空")
    @ApiModelProperty("主任务id")
    private String taskId;

    @ApiModelProperty("页码, 默认1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    @ApiModelProperty("每页数量, 默认20")
    @Max(value = 100, message = "每页最多获取100条数据")
    private Integer pageSize;
}
