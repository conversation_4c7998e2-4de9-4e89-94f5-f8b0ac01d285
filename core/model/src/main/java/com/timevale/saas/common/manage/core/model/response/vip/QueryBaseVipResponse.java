package com.timevale.saas.common.manage.core.model.response.vip;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * QueryBaseVipResponse
 *
 * <AUTHOR>
 * @since 2021/5/27 4:14 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBaseVipResponse extends ToString {

    /**
     * 租户名称
     */
    @ApiModelProperty("租户名称")
    private String tenantName;

    /**
     * 租户id
     */
    @ApiModelProperty("租户id")
    private String gid;

    /**
     * 套餐信息
     */
    @ApiModelProperty("套餐信息")
    private List<VipInfo> vipInfoList;

    @JsonIgnore
    private Integer sortIndex;
}
