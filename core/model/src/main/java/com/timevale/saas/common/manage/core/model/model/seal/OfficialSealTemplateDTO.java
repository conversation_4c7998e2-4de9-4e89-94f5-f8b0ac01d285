package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("企业章模版详细信息")
public class OfficialSealTemplateDTO {

    @ApiModelProperty(
            value =
                    "印章模板样式 （TEMPLATE_ROUND、TEMPLATE_OVAL、TEMPLATE_DYNAMIC_OVAL已经被以前的老模板占用了，且这个接口，也不允许创建老模板印章）<br>"
                            + "其他-圆形章-五角星(COMMON_ROUND_STAR)<br>"
                            + "其他-椭圆章(COMMON_OVAL)<br>"
                            + "其他-双椭圆(COMMON_TWO_OVAL)<br>"
                            + "公章-圆形章-五角星(PUBLIC_ROUND_STAR) <br>"
                            + "公章-椭圆章(PUBLIC_OVAL)<br>"
                            + "公章-双椭圆(PUBLIC_TWO_OVAL)<br>"
                            + "财务-圆形章-不带五角星(FINANCE_ROUND_NO_STAR)<br>"
                            + "财务-圆形章-带五角星(FINANCE_ROUND_STAR)<br>"
                            + "财务-方形横排(FINANCE_SQUARE_HORIZONTAL)<br>"
                            + "财务-方形竖排(FINANCE_SQUARE_VERTICAL)<br>"
                            + "合同-圆形章-不带五角星(CONTRACT_ROUND_NO_STAR)<br>"
                            + "合同-圆形章-带五角星(CONTRACT_ROUND_STAR)<br>"
                            + "人事-圆形章-不带五角星(PERSONNEL_ROUND_NO_STAR)"
                            + "人事-圆形章-带五角星(PERSONNEL_ROUND_STAR)")
    private String templateType;

    @ApiModelProperty(value = "环绕文")
    private String surroundText;

    @ApiModelProperty(
            value =
                    "印章尺寸：42_42、40_40等枚举值，见印章尺寸枚举<br>"
                            + "公章-圆形章-五角星：42_42、40_40<br>"
                            + "公章-椭圆章： 45_30<br>"
                            + "公章-双椭圆: 45_30<br>"
                            + "财务-圆形章-不带五角星: 38_38<br>"
                            + "财务-圆形章-带五角星: 38_38<br>"
                            + "财务-方形横排: 22_22<br>"
                            + "财务-方形竖排：22_22<br>"
                            + "合同-圆形章-不带五角星: 38_38<br>"
                            + "合同-圆形章-带五角星: 38_38<br>"
                            + "人事-圆形章-不带五角星: 38_38"
                            + "人事-圆形章-带五角星: 38_38")
    private String widthHeight;

    @ApiModelProperty(value = "横向文")
    private String horizontalText;

    @ApiModelProperty(value = "下弦文")
    private String bottomText;

    @ApiModelProperty(value = "印章颜色，RED-红色，BLUE-蓝色，BLACK-黑色，PURPLE-紫色")
    private String color;

    @ApiModelProperty(value = "不透明度 0~100。 100表示不透明")
    private Short opacity;

    @ApiModelProperty(
            value = "印章做旧样式，NONE-无，OLD-印章做旧(随机样式，如果需指定做旧样式，指定1-12样式id，如OLD_1、OLD_2)，默认NONE")
    private String style;

    @ApiModelProperty(value = "环绕文英文，只有当印章样式为“公章-椭圆章-中英文”时可选，其他样式忽略")
    private String surroundTextInner;

    @ApiModelProperty(value = "税号(发票专用章会用到)")
    private String taxNumber;

    @ApiModelProperty(value = "防伪编号(发票专用章会用到)")
    private String antiFakeNumber;
}
