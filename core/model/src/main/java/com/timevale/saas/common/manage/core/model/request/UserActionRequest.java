package com.timevale.saas.common.manage.core.model.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 用户行为记录request
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/25 20:38
 */
@Data
public class UserActionRequest extends ToString {
    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String accountId;
    /**
     * 用户id 类型 0 oid, 1 gid
     */
    @ApiModelProperty("用户id 类型 0 oid, 1 gid")
    private Integer accountType;
    /**
     * 主体id
     */
    @ApiModelProperty("主体id")
    private String subjectId;
    /**
     * 主体id 类型  0 oid, 1 gid
     */
    @ApiModelProperty("主体id 类型  0 oid, 1 gid")
    private Integer subjectType;
    /**
     * 用户行为
     */
    @NotBlank(message = "用户行为不能为空")
    @ApiModelProperty("用户行为")
    @Length(min = 5, max = 128, message = "用户行为长度为5-128")
    private String action;
    /**
     * 过期时间
     */
    private Long expireTime;
}
