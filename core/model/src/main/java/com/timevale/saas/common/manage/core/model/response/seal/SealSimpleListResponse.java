package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.SealInfoSimpleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/12/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取印章列表响应")
public class SealSimpleListResponse extends ToString {

    @ApiModelProperty(value = "查询总数")
    private Long total;

    @ApiModelProperty(value = "印章列表")
    private List<SealInfoSimpleDTO> seals;
}
