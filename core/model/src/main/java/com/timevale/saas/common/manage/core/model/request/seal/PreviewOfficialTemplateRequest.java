package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("企业章预览请求")
public class PreviewOfficialTemplateRequest extends ToString {
    @NotBlank(message = "印章模板样式不能为空")
    @ApiModelProperty(
            value =
                    "印章模板样式 （TEMPLATE_ROUND、TEMPLATE_OVAL、TEMPLATE_DYNAMIC_OVAL已经被以前的老模板占用了，且这个接口，也不允许创建老模板印章）<br>"
                            + "公章-圆形章-五角星(PUBLIC_ROUND_STAR)<br>"
                            + "公章-椭圆章(PUBLIC_OVAL)<br>"
                            + "公章-双椭圆(PUBLIC_TWO_OVAL)<br>"
                            + "财务-圆形章-不带五角星(FINANCE_ROUND_NO_STAR)<br>"
                            + "财务-圆形章-带五角星(FINANCE_ROUND_STAR)<br>"
                            + "财务-方形横排(FINANCE_SQUARE_HORIZONTAL)<br>"

                            + "财务-方形竖排(FINANCE_SQUARE_VERTICAL)<br>"

                            + "合同-圆形章-不带五角星(CONTRACT_ROUND_NO_STAR)<br>"
                            + "合同-圆形章-带五角星(CONTRACT_ROUND_STAR)<br>"

                            + "人事-圆形章-不带五角星(PERSONNEL_ROUND_NO_STAR)<br>"
                            + "人事-圆形章-带五角星(PERSONNEL_ROUND_STAR)<br>" +
                            "其他-圆形章-五角星(COMMON_ROUND_STAR)<br>" +
                            "其他-椭圆章(COMMON_OVAL)<br>" +
                            "其他-双椭圆(COMMON_TWO_OVAL)")
    @Pattern(
            regexp =
                    "COMMON_TWO_OVAL|COMMON_OVAL|COMMON_ROUND_STAR|PUBLIC_ROUND_STAR|PUBLIC_OVAL|PUBLIC_TWO_OVAL|FINANCE_ROUND_NO_STAR|FINANCE_ROUND_STAR|FINANCE_SQUARE_HORIZONTAL|FINANCE_SQUARE_VERTICAL|CONTRACT_ROUND_NO_STAR|CONTRACT_ROUND_STAR|PERSONNEL_ROUND_NO_STAR|PERSONNEL_ROUND_STAR",
            message = "印章模板样式不正确")
    private String templateType;

    @NotBlank(message = "印章尺寸不能为空")
    @ApiModelProperty(
            value =
                    "印章尺寸：42_42、40_40等枚举值，见印章尺寸枚举<br>"

                            + "公章-圆形章-五角星：42_42、40_40<br>"

                            + "公章-椭圆章： 45_30<br>"

                            + "公章-双椭圆: 45_30<br>"
                            + "其他-圆形章-五角星：42_42、40_40<br>"

                            + "其他-椭圆章： 45_30<br>"

                            + "其他-双椭圆: 45_30<br>"

                            + "财务-圆形章-不带五角星: 38_38<br>"
                            + "财务-圆形章-带五角星: 38_38<br>"

                            + "财务-方形横排: 22_22<br>"

                            + "财务-方形竖排：22_22<br>"

                            + "合同-圆形章-不带五角星: 38_38<br>"
                            + "合同-圆形章-带五角星: 38_38<br>"

                            + "人事-圆形章-不带五角星: 38_38"
                            + "人事-圆形章-带五角星: 38_38"
    )
    private String widthHeight;

    @Length(max = 30, message = "横向文最长只支持30个字符")
    @ApiModelProperty(value = "横向文")
    private String horizontalText;

    @Pattern(regexp = "[\\x00-\\xFF]{0,30}", message = "下弦文：只支持英文或英文状态下的符号，最多30个字符")
    @ApiModelProperty(value = "下弦文")
    private String bottomText;

    @NotBlank(message = "印章颜色不能为空")
    @ApiModelProperty(value = "印章颜色，RED-红色，BLUE-蓝色，BLACK-黑色，PURPLE-紫色")
    @Pattern(regexp = "RED|BLUE|BLACK|PURPLE", message = "印章颜色不正确")
    private String color;

    @NotNull(message = "不透明度不能为空")
    @Min(value = 1, message = "不透明度必须在1~100以内")
    @Max(value = 100, message = "不透明度必须在1~100以内")
    @ApiModelProperty(value = "不透明度 0~100。 100表示不透明")
    private Short opacity;

    @ApiModelProperty(
            value = "印章做旧样式，NONE-无，OLD-印章做旧(随机样式，如果需指定做旧样式，指定1-12样式id，如OLD_1、OLD_2)，默认NONE")
    @Pattern(
            regexp =
                    "NONE|OLD_1|OLD_2|OLD_3|OLD_4|OLD_5|OLD_6|OLD_7|OLD_8|OLD_9|OLD_10|OLD_11|OLD_12",
            message = "做旧样式不正确")
    private String style;

    @ApiModelProperty(value = "环绕文英文，只有当印章样式为“公章-椭圆章-中英文”时可选，其他样式忽略")
    @Pattern(regexp = "[\\x00-\\xFF]{0,50}", message = "环绕文英文：只支持英文或英文状态下的符号，最多50个字符")
    private String surroundTextInner;

    @Pattern(regexp = "[A-Z0-9]{0,18}", message = "税号：只支持大写字母或数字，最多18个字符")
    @ApiModelProperty(value = "税号(发票专用章会用到)")
    private String taxNumber;

    @Length(max = 15, message = "防伪编号最长只支持15个字符")
    @ApiModelProperty(value = "防伪编号(发票专用章会用到)")
    private String antiFakeNumber;

    @ApiModelProperty(value = "印章归属OID")
    private String sealOwnerOid;

}
