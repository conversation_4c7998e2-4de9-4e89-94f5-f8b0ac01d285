package com.timevale.saas.common.manage.core.model.response.vip;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.vo.vip.VipFunctionIllustrateVO;
import com.timevale.saas.common.manage.core.model.request.vip.bean.VipFunctionLimit;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipLevelFunctionInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-14 17:02
 */
@Data
@ApiModel(value = "获取会员功能详情响应数据")
public class QueryVipFunctionInfoResponse extends ToString {

    @ApiModelProperty("会员功能基本信息")
    private VipLevelFunctionInfo function;

    @ApiModelProperty("会员功能限制情况")
    private List<VipFunctionLimit> limits;

    @ApiModelProperty("版本展示信息")
    private VipFunctionIllustrateVO illustrate;
}
