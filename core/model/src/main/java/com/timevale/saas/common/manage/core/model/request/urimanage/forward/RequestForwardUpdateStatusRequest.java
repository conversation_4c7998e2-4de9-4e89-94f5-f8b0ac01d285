package com.timevale.saas.common.manage.core.model.request.urimanage.forward;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020-10-21
 */
@Data
@ApiModel("更新saas接口转发配置状态请求参数")
public class RequestForwardUpdateStatusRequest extends ToString {

    @NotNull(message = "规则配置状态不能为空")
    @ApiModelProperty("规则配置状态， 0-停用，1-启用，2-删除")
    @Min(value =0 , message = "规则配置状态无效， 仅支持: 0-停用，1-启用，2-删除")
    @Max(value = 2, message = "规则配置状态无效， 仅支持: 0-停用，1-启用，2-删除")
    private Integer status;
}
