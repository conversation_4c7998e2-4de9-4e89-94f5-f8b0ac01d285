package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("文档信息")
public class FileDocDTO {
    @ApiModelProperty(value = "文件fileKey")
    private String fileKey;

    @ApiModelProperty(value = "文件url")
    private String url;
}
