package com.timevale.saas.common.manage.core.model.response.activities;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.activity.LandingPageConfigDetail;
import com.timevale.saas.common.manage.core.model.model.activity.NoticeConfig;
import com.timevale.saas.common.manage.core.model.model.activity.PosterConfig;
import com.timevale.saas.common.manage.core.model.model.activity.ResultPageConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
public class QueryActivityDetailResponse extends ToString {

    @ApiModelProperty("活动标识")
    private String activityCode;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("赠送活动标识")
    private String presentCode;

    @ApiModelProperty("赠送活动套餐id")
    private String presentMenuId;

    @ApiModelProperty("活动参与上限")
    private Integer maxLimit;

    @ApiModelProperty("活动起始时间")
    private Long startTime;

    @ApiModelProperty("活动截止时间")
    private Long endTime;

    @ApiModelProperty("活动参与主体类型, PERSON-个人，ORGANIZE-企业")
    private List<String> subjectTypes;

    @ApiModelProperty("通知配置")
    private NoticeConfig noticeConfig;

    @ApiModelProperty("活动落地页配置")
    private LandingPageConfigDetail landingPageConfig;

    @ApiModelProperty("活动结果页配置")
    private ResultPageConfig resultPageConfig;

    @ApiModelProperty("海报配置")
    private PosterConfig posterConfig;

    @ApiModelProperty("额外配置")
    private Map<String, Object> extraConfig;

    @ApiModelProperty("渠道gid：用于计费隔离")
    private String channelGid;

}
