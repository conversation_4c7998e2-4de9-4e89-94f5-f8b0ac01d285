package com.timevale.saas.common.manage.core.model.request.urimanage.forward;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.model.output.bean.DefenderBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020-10-21
 */
@Data
@ApiModel("编辑接口转发及参数替换规则请求参数")
public class RequestForwardUpdateRequest extends ToString {

    @Length(max = 100, message = "接口地址最长不可超过100字符")
    @ApiModelProperty("接口地址")
    private String uri;

    @NotBlank(message = "接口说明不能为空")
    @Length(max = 40, message = "接口地址最长不可超过40字符")
    @ApiModelProperty("接口说明")
    private String desc;

    @ApiModelProperty("接口对应的method类型，GET/POST/PUT/DELETE，若为空，表示不限制")
    private String method;

    /** @see com.timevale.saas.common.manage.common.service.enums.ForwardTypeEnum */
    @NotNull(message = "转发类型不能为空")
    @Min(value = 0, message = "转发类型仅支持0/1/2，0-不转发，1-接口转发，2-转发到account-webserver")
    @Max(value = 2, message = "转发类型仅支持0/1/2，0-不转发，1-接口转发，2-转发到account-webserver")
    @ApiModelProperty("转发类型，0-不转发，1-接口转发，2-转发到account-webserver")
    private Integer forwardType;

    @ApiModelProperty("接口转发地址")
    private String forwardUri;

    @ApiModelProperty("接口转发地址对应的method类型，GET/POST/PUT/DELETE")
    private String forwardMethod;

    @ApiModelProperty("接口标签， 多个以逗号分隔")
    private String tags;

    @ApiModelProperty("参数替换规则")
    private DefenderBean defender;
}
