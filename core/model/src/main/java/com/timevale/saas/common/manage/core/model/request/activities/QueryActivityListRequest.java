package com.timevale.saas.common.manage.core.model.request.activities;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@ApiModel("查询活动列表请求参数")
public class QueryActivityListRequest extends ToString {

    @ApiModelProperty("页码")
    private Integer pageNum;

    @ApiModelProperty("单页数量")
    private Integer pageSize;

    @ApiModelProperty("活动标识")
    private String activityCode;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动创建人")
    private String creator;

    @ApiModelProperty("活动状态，1-进行中，2-已过期，3-未开始")
    private Integer status;
}
