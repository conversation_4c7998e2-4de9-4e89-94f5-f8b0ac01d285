package com.timevale.saas.common.manage.core.model.vo.vip;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;

/**
 * TODO 版本变更的展示信息
 *
 * <AUTHOR>
 * @since 2022/11/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("功能变更的展示信息")
public class VipFunctionIllustrateVO {
    @ApiModelProperty("功能介绍展示模式，1 图片 2 视频")
    private Integer illustrateShowMode;

    @ApiModelProperty("功能介绍图片或者视频的文件fileKey")
    private String illustrateShowFile;

    @ApiModelProperty("功能介绍图片或者视频的文件fileName")
    private String illustrateShowFileName;

    @ApiModelProperty("功能介绍标题")
    @Max(value = 100, message = "功能介绍标题最多支持100个字符")
    private String illustrateDocTitle;

    @ApiModelProperty("功能介绍文档")
    @Max(value = 500, message = "功能介绍文档最多支持500个字符")
    private String illustrateDocContent;

    @ApiModelProperty("对内介绍文档地址")
    @Max(value = 250, message = "对内介绍文档地址最多支持250个字符")
    private String illustrateInnerDocUrl;

    @ApiModelProperty("对外介绍文档地址")
    @Max(value = 250, message = "对外介绍文档地址最多支持250个字符")
    private String illustrateOutDocUrl;

    @ApiModelProperty("对外申请文案内容")
    private String illustrateApplyDoc;

    @ApiModelProperty("对外展示模式，1 平铺 2 弹窗")
    private Integer illustratePopMode;
    
    @ApiModelProperty("是否支持试用")
    private boolean supportTrial;
}
