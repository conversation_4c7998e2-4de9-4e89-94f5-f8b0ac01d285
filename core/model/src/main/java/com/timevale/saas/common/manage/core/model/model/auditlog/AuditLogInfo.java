package com.timevale.saas.common.manage.core.model.model.auditlog;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * TODO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/14 14:34
 */
@Data
public class AuditLogInfo extends ToString {

    @ApiModelProperty("企业空间唯一标识1")
    private String enterpriseSpaceUnique1;

    @ApiModelProperty("应用id")
    private String appId;

    @ApiModelProperty("操作人唯一标识1")
    private String userUnique1;

    @ApiModelProperty("操作时间（ms）")
    private Long time;

    @ApiModelProperty("时间戳内唯一id")
    private Long uniqueId;

    @ApiModelProperty("二级模块")
    private String secondaryModule;

    @ApiModelProperty("ip地址")
    private String ip;

    @ApiModelProperty("企业空间名称")
    private String enterpriseSpaceName;

    @ApiModelProperty("企业空间唯一标识2")
    private String enterpriseSpaceUnique2;

    @ApiModelProperty("操作人唯一标识2")
    private String userUnique2;

    @ApiModelProperty("操作人姓名")
    private String name;

    @ApiModelProperty("操作人组织架构")
    private String organization;

    @ApiModelProperty("角色")
    private String role;

    @ApiModelProperty("操作资源id")
    private String resourceId;

    @ApiModelProperty("操作资源名称")
    private String resourceName;

    @ApiModelProperty("操作端")
    private String terminal;

    @ApiModelProperty("操作类型")
    @NeedTranslateField
    private String type;

    @ApiModelProperty("操作结果")
    @NeedTranslateField
    private String result;

    @ApiModelProperty("操作详情")
    private String details;

    @ApiModelProperty("日期时间")
    private String dateTime;

    @ApiModelProperty("事件")
    private String event;

    @ApiModelProperty("登录凭证")
    private String loginCredentials;

}
