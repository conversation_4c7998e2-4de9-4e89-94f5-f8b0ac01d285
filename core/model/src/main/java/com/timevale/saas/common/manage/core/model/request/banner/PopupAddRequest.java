package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.banner.PictureDTO;
import com.timevale.saas.common.manage.core.model.model.banner.PushUserConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/1
 */
@ApiModel("弹窗添加/修改入参")
@Data
public class PopupAddRequest extends ToString {

    @ApiModelProperty("产品code")
    @NotBlank(message = "请选择产品编码")
    @Length(max = 32, message = "产品编码不能超过32个字符")
    private String productCode;

    @ApiModelProperty("位置code")
    @NotBlank(message = "请选择位置编码")
    @Length(max = 32, message = "位置编码不能超过32个字符")
    private String areaCode;

    @ApiModelProperty("弹出名称")
    @NotBlank(message = "名称不可为空")
    @Length(max = 32, message = "名称不能超过32个字符")
    private String name;

    @ApiModelProperty("图片key")
    private String fileKey;

    @ApiModelProperty("跳转链接")
    private String linkUrl;

    @ApiModelProperty("有效用户配置")
    private List<PushUserConfigDTO> validityUserConfig;

    @ApiModelProperty("有效开始时间")
    private String validityStartTime;

    @ApiModelProperty("有效结束时间")
    private String validityEndTime;

    @ApiModelProperty("弹出配置类型 0首次")
    private Integer configType;

    @ApiModelProperty("权重")
    private Integer weight;

    @ApiModelProperty("标题")
    @Length(max = 40, message = "弹窗标题不能超过40个字")
    @Valid
    private String title;

    @ApiModelProperty("公告类型:1.产品迭代更新、2.新功能上线、3.交互体验升级、4.系统问题通知、5.其他")
    private Integer noticeType;

    @ApiModelProperty("弹窗类型，1. 单一图片 2.轮播图片")
    private Integer popupType;

    @ApiModelProperty("轮播图片列表")
    private List<PictureDTO> scrollPictures;

    @ApiModelProperty("支持的vip版本，为空表示所有版本都展示")
    private List<String> notifyVersions;

    @ApiModelProperty("支持的角色，为空表示所有角色都展示")
    private List<String> notifyRoles;
}
