package com.timevale.saas.common.manage.core.model.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 查询用户行为记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/25 20:47
 */
@Data
public class UserActionQueryRequest extends ToString {
    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private String accountId;
    /**
     * 主体id
     */
    @ApiModelProperty("主体id")
    private String subjectId;
    /**
     * 用户行为
     */
    @NotBlank(message = "用户行为不能为空")
    @ApiModelProperty("用户行为")
    @Length(min = 5, max = 64)
    private String action;
}
