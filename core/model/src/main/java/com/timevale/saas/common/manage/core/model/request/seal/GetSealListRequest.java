package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取印章列表请求")
public class GetSealListRequest extends ToString {

    @ApiModelProperty(value = "是否返回下载地址, 默认是true")
    private Boolean downloadFlag = true;

    /** 是否加水印, 此字段不暴露文档中 */
    @ApiModelProperty(hidden = true)
    private Boolean watermarkFlag = false;

    @ApiModelProperty(value = "是否返回待审核印章, 默认是false")
    private Boolean statusFlag = false;

    @ApiModelProperty(value = "是否返回授权统计信息, 默认是false")
    private Boolean sealGrantCountFlag = false;

    @ApiModelProperty(value = "是否返回平台信息, 默认是false")
    private Boolean platformFlag = false;

    @ApiModelProperty(value = "是否返回图片章驳回信息, 默认是false")
    private boolean imageRefuseFlag = false;

    /** 印章业务类型 参考 footstone-seal服务的 ealBizType */
    @ApiModelProperty(value = "印章业务类型")
    private String sealBizType;

    @ApiModelProperty(value = "页码", required = true)
    @NotNull(message = "pageNo不能为空")
    @Min(value = 1, message = "pageNo最小值为1")
    private Integer pageNo;

    @ApiModelProperty(value = "每页显示的数量", required = true)
    @NotNull(message = "pageSize不能为空")
    @Min(value = 1, message = "pageSize最小值为1")
    @Max(value = 20, message = "pageSize最大值为20")
    private Integer pageSize;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;
}
