package com.timevale.saas.common.manage.core.model.bo.share;

import com.timevale.mandarin.base.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-07-10 17:27
 */
@Data
public class GetResourceRecordsBO {

	/** 分页参数 */
	private Integer offset;

	/** 每页大小 */
	private Integer pageSize;

	/*分享状态,1:生效中，0:失效*/
	private Integer status;

	/*操作开始时间*/
	private Date operateStartTime;

	/*操作结束时间*/
	private Date operateEndTime;


	/*资源名称*/
	private String resourceName;

	/*分享对象(姓名/手机号/邮箱)*/
	private String shareTargetSearch;

	@ApiModelProperty(value = "操作人(姓名/手机号/邮箱)")
	private String operatorSearch;
	/** 企业空间gid->索引键 */
	private String gid;
	/*资源id*/
	private String resourceId;

	public GetResourceRecordsBO(
			Integer offset,
			Integer pageSize,
			Integer status,
			Date operateStartTime,
			Date operateEndTime,
			String resourceName,
			String shareTargetSearch,
			String operatorSearch,
			String gid,
			String resourceId) {
		this.offset = offset;
		this.pageSize = pageSize;
		this.status = status;
		this.operateStartTime = operateStartTime;
		this.operateEndTime = operateEndTime;
		if(StringUtils.isNotBlank(resourceName)){
			this.resourceName = resourceName;
		}
		if(StringUtils.isNotBlank(shareTargetSearch)){
			this.shareTargetSearch = shareTargetSearch;
		}
		if(StringUtils.isNotBlank(operatorSearch)){
			this.operatorSearch = operatorSearch;
		}
		if(StringUtils.isNotBlank(resourceId)){
			this.resourceId = resourceId;
		}
		this.gid = gid;
	}
}
