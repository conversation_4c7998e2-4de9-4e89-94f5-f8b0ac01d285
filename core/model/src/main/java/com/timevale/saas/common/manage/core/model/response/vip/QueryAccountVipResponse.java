package com.timevale.saas.common.manage.core.model.response.vip;

import com.google.common.collect.Lists;
import com.timevale.saas.common.manage.core.model.response.vip.bean.AccountVip;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipFunctionBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-08-21
 */
@Data
@ApiModel("获取用户会员信息响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class QueryAccountVipResponse extends AccountVip {

    @ApiModelProperty("会员产品id")
    private String productId;

    @ApiModelProperty("是否即将过期")
    private boolean expiring;

    @ApiModelProperty("会员功能列表")
    private List<VipFunctionBean> functions = Lists.newArrayList();

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("是否为多会员版本共享 true 是  false or null 不是共享")
    private Boolean share = false;

    /**
     * 共享企业版本
     */
    private String shareVipCode;

    /**
     * 多订单标识
     */
    private Boolean multiOrderFlag;

    // 对前端不透出，当前主题名称
    private transient String tenantName;

    @ApiModelProperty("如果为共享版本 主企业主体名称")
    private String parentTenantName;

    public QueryAccountVipResponse(boolean useVipCode) {
        super(useVipCode);
    }
}
