package com.timevale.saas.common.manage.core.model.request.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("设置接受审批通知请求")
public class SettingRuleGrantNotifyRequest {

    @ApiModelProperty(name = "orgId", value = "企业id", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiModelProperty(name = "ruleGrantedId", value = "规则授权id", required = true)
    @NotBlank(message = "规则授权id不能为空")
    private String ruleGrantedId;

    @ApiModelProperty(value = "是否通知", required = true)
    private Boolean shouldNotify = false;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;
}
