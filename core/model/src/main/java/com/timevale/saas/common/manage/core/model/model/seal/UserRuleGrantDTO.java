package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户授权信息")
public class UserRuleGrantDTO {
    @ApiModelProperty(value = "被授权人Oid")
    private String authorizedPsnId;

    @ApiModelProperty(value = "授权业务id")
    private String sealAuthBizId;
}
