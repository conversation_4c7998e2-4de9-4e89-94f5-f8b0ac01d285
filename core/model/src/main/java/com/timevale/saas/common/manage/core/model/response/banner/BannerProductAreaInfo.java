package com.timevale.saas.common.manage.core.model.response.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2021-05-31 16:34
 **/
@ApiModel("Banner位置详情列表")
public class BannerProductAreaInfo extends ProductAreaSimpleInfo {

    @ApiModelProperty("该位置允许的最大容量")
    private Integer maxCapacity;


    @ApiModelProperty("该位置已上架的容量")
    private Integer capacity;

    /**
     * @see com.timevale.saas.common.manage.common.service.constant.banner.BannerAreaDisplayTypeConstant
     */
    @ApiModelProperty("展示形式")
    private Integer displayType;

    public Integer getMaxCapacity() {
        return maxCapacity;
    }

    public void setMaxCapacity(Integer maxCapacity) {
        this.maxCapacity = maxCapacity;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }

    public Integer getDisplayType() {
        return displayType;
    }

    public void setDisplayType(Integer displayType) {
        this.displayType = displayType;
    }
}
