package com.timevale.saas.common.manage.core.model.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@ApiModel("获取任务列表请求参数")
public class QuerySaasTaskRequest extends ToString {

    @NotBlank(message = "用户账号oid不能为空")
    @ApiModelProperty("用户账号oid")
    private String accountId;

    @ApiModelProperty("任务状态, 可多选, 多个逗号分隔, 1-进行中， 2-已完成，3-部分失败， 4-全部失败")
    private String taskStatus;

    @ApiModelProperty("任务类型, 可多选, 多个逗号分隔, 0-批量发起合同，3-合同下载，4-导出合同任务明细，5-批量签署,6-批量撤回,7-批量催办")
    private String taskType;

    @ApiModelProperty("页码, 默认1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    @ApiModelProperty("每页数量, 默认20")
    @Max(value = 100, message = "每页最多获取100条数据")
    private Integer pageSize;
}
