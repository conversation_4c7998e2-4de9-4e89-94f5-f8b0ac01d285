package com.timevale.saas.common.manage.core.model.request.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.vo.vip.VipFunctionIllustrateVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-08-20
 */
@Data
public class VipFunctionInput extends ToString {

    @ApiModelProperty("功能标识")
    private String code;

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("功能说明")
    private String desc;

    @ApiModelProperty("功能限制信息列表")
    private List<VipFunctionLimit> limits;

    @ApiModelProperty("版本展示信息")
    private VipFunctionIllustrateVO illustrate;
}
