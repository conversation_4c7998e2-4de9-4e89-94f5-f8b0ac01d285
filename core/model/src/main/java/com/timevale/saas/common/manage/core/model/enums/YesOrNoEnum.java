package com.timevale.saas.common.manage.core.model.enums;

import lombok.Getter;

/**
 * Created by tianlei on 2022/2/19
 */
public enum YesOrNoEnum {


    YES(1),
    NO(0),
    ;

    @Getter
    private Integer code;


    YesOrNoEnum(Integer code) {
        this.code = code;
    }

    public static YesOrNoEnum from(Integer code) {
        if (null == code) {
            return null;
        }

        for (YesOrNoEnum statusEnum : YesOrNoEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }
}
