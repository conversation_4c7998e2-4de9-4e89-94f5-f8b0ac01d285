package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-04-02 10:27
 */
@Data
@ApiModel("套餐告警信息")
@NoArgsConstructor
public class PackageWarnResponse {

    @ApiModelProperty("主体名称")
    private String subjectName;

    @ApiModelProperty("提醒类型：1. 余量提醒 2. 到期提醒 3. 余量及到期提醒 4. 超期续购提醒")
    private Integer warnType;

    @ApiModelProperty("套餐订购时间（创建订单的时间）")
    private Date createTime;

    @ApiModelProperty("套餐最后操作时间")
    private Date updateTime;

    @ApiModelProperty("套餐到期时间")
    private Date endTime;

    @ApiModelProperty("剩余量")
    private BigDecimal margin;

    @ApiModelProperty("购买量")
    private BigDecimal totalNum;

    @ApiModelProperty("计量单位")
    private String units;

    @ApiModelProperty("提醒不足比例，eg：30、15、5，不提醒为null")
    private BigDecimal warnPct;

    /**
     * 套餐是否提前过期（提前用完）
     *
     * @return
     */
    public boolean beforeExpired() {
        if (margin == null || updateTime == null || endTime == null) {
            return false;
        }
        // 在endDate前剩余量为0，说明提前完成（不包括endDate当天用完）
        if (BigDecimal.ZERO.equals(margin)
                && (updateTime.before(endTime) && !DateUtils.isSameDay(updateTime, endTime))) {
            return true;
        }

        return false;
    }

    /**
     * 套餐是否正常过期
     *
     * @return
     */
    public boolean arriveEndExpired() {
        if (margin == null || endTime == null) {
            return false;
        }

        return new Date().after(endTime)
                && !DateUtils.isSameDay(new Date(), endTime)
                && !beforeExpired();
    }
}
