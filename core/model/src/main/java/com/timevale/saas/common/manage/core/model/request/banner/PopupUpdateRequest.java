package com.timevale.saas.common.manage.core.model.request.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/11/1
 **/
@ApiModel("弹窗修改入参")
@Data
public class PopupUpdateRequest extends PopupAddRequest {

    @ApiModelProperty("修改弹窗id")
    @NotNull(message = "弹窗唯一标识不可为空")
    private Long id;
}
