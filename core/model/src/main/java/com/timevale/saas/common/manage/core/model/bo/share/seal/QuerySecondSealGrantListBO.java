package com.timevale.saas.common.manage.core.model.bo.share.seal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/17 查询二次授权
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QuerySecondSealGrantListBO {
    /** 企业的oid */
    private String orgOid;
    /** 操作人的oid */
    private String operatorOid;
    /** 一级授权业务id */
    private String sealGrantBizId;
    /** 页码 */
    private Integer pageNum;
    /** 每页数量 */
    private Integer pageSize;
    /** 查询的状态 */
    private List<Integer> statusList;

    private String appId;
}
