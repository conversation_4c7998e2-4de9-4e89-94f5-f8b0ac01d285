package com.timevale.saas.common.manage.core.model.request.share;

import com.timevale.mandarin.common.result.ToString;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取资源访问地址
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@ApiModel(value = "获取资源访问地址")
@Getter
@Setter
public class GetResourceUrlRequest extends ToString {

    @ApiModelProperty(value = "资源分享id", required = true)
    @NotBlank(message = "resourceShareId不能为空")
    private String resourceShareId;

    @ApiModelProperty(value = "操作人oid", required = true)
    @NotBlank(message = "accountId不能为空")
    private String accountId;

    @ApiModelProperty(value = "操作人主体oid", required = true)
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;

    @ApiModelProperty(value = "重定向地址")
    private String redirectUrl;

    @ApiModelProperty(value = "三方平台登录token")
    private String token;

    @ApiModelProperty(
            value =
                    "获取哪个端的地址,1-开放服务h5 2-支付宝小程序 3-微信小程序 4-标准签H5 5-标准签WEB 6-开放服务WEB 7-IOS标准签APP 8-Android标准签APP")
    private Integer platform = 4;

    private String appId;

    private String clientId;
}
