package com.timevale.saas.common.manage.core.model.model.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/24
 **/
@Data
@ApiModel("弹窗指定用户配置")
public class PopupUserConfigDTO {

    @ApiModelProperty("指定功能灰度应用")
    private String projectKey;
    @ApiModelProperty("指定功能灰度功能")
    private String functionKey;
    @ApiModelProperty("是否满足 true 满足添加推送 false 不满足添加推送")
    private Boolean hasSatisfy = true;
}
