package com.timevale.saas.common.manage.core.model.response.domain;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/3/8
 */
@Setter
@Getter
public class DomainWhiteListResponse extends ToString {

    @ApiModelProperty("域名信息")
    private List<DomainWhiteInfoVO> domainInfoList;

    @ApiModelProperty("数据总数")
    private Integer total;

}
