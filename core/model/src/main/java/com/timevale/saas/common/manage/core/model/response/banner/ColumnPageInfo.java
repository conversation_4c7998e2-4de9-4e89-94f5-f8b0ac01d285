package com.timevale.saas.common.manage.core.model.response.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 *
 * @date 2021/11/19
 */
@Data
@ApiModel("栏目内容管理端分页展示内容")
public class ColumnPageInfo {

    @ApiModelProperty("栏目内容唯一标识")
    private Long id;

    @ApiModelProperty("栏目内容名称（后端展示）")
    private String name;

    @ApiModelProperty("跳转链接")
    private String linkUrl;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("位置名称")
    private String areaName;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("点击量")
    private Integer clickNum;

    @ApiModelProperty("上架状态")
    private Integer status;

    @ApiModelProperty("权重")
    private Integer weight;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("修改人")
    private String modifier;
}
