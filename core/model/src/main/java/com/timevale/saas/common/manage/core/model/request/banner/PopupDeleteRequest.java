package com.timevale.saas.common.manage.core.model.request.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/11/3
 **/
@Data
@ApiModel("弹窗删除入参")
public class PopupDeleteRequest {
    @ApiModelProperty("弹窗唯一标识")
    @NotNull(message = "请选择弹窗")
    private Long popId;
}
