package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/15
 */
@Data
@ApiModel("套餐告警信息")
@NoArgsConstructor
@AllArgsConstructor
public class PageResponse<T> {

    @ApiModelProperty("分页数据")
    private List<T> result;

    @ApiModelProperty("总数")
    private Long total;
}
