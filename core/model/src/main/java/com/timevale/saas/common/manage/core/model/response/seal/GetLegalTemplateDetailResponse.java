package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.LegalSealTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("查询法人模板章详情")
public class GetLegalTemplateDetailResponse extends ToString {
    @ApiModelProperty(value = "印章别名")
    private String alias;

    @ApiModelProperty(value = "印章下载地址")
    private String url;

    @ApiModelProperty(value = "印章状态 -1-待领取，1-已启用，2-待审核，3-审核不通过 4-已挂起 5-待提交")
    private Integer status;

    @ApiModelProperty(value = "印章模版信息")
    private LegalSealTemplateDTO sealTemplate;
}
