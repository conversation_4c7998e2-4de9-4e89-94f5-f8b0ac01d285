package com.timevale.saas.common.manage.core.model.model;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 * @date 2022/11/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InviteSubjectAuthDTO extends ToString {
    /** 实名账号 */
    private UserAccount userAccount;
    /** 实名流程id */
    private String serviceId;
}
