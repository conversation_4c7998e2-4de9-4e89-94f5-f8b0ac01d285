package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 * @date 2021/11/2
 */
@Data
@ApiModel("产品位置信息")
public class ProductAreaInfo extends ToString {
    @ApiModelProperty("位置code")
    private String areaCode;
    @ApiModelProperty("位置名称")
    private String areaName;
    @ApiModelProperty("展示形式")
    private Integer displayType;
    @ApiModelProperty("链接地址")
    private String linkUrl;
    @ApiModelProperty("图片文件key")
    private String icon;
    @ApiModelProperty("图片链接")
    private String iconUrl;
}
