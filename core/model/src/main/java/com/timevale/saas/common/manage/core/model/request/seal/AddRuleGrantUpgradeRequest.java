package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("新增印章授权(兼容二次授权)请求")
public class AddRuleGrantUpgradeRequest extends ToString {

    @ApiModelProperty(name = "orgId", value = "企业id", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiModelProperty(value = "资源id", required = true)
    @NotBlank(message = "印章id不能为空")
    private String resourceId;

    @ApiModelProperty(value = "角色类型，SEAL_EXAMINER-印章审批员, SEAL_USER-印章使用员", required = true)
    @NotBlank(message = "角色类型不能为空")
    private String roleKey;

    @ApiModelProperty(value = "自动落章")
    private Boolean autoFall;

    @ApiModelProperty(value = "落章方式，0-手动落章，1-免意愿且自动落章，2-免意愿且手动落章， 默认手动落章")
    private Integer fallType;

    @ApiModelProperty(value = "授权范围列表(模板id/ALL/HR/FINANCE)", required = true)
    @NotNull(message = "授权范围列表不能为空")
    private Set<String> scopeList;

    @ApiModelProperty(value = "通知配值(默认开启)")
    private Boolean notifySetting = true;


    @ApiModelProperty(value = "被授权对象", required = true)
    private List<String> grantedAccountIds;

    @ApiModelProperty(value = "被授权对象证件号")
    private String grantedUserCode;

    @ApiModelProperty(value = "被授权对象")
    private String grantedUserName;

    @ApiModelProperty(value = "生效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "生效时间不能为空")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "失效时间不能为空")
    private Long expireTime;

    @ApiModelProperty(value = "授权签署重定向地址")
    private String grantRedirectUrl;
    /** @link com.timevale.footstone.user.service.enums.GrantTypeEnum#getType() */
    @ApiModelProperty(value = "授权类型，1-企业内，2-企业间，默认1")
    private Integer grantType;

    @ApiModelProperty(value = "跨企业授权时，授权给对方企业的哪个角色 目前有'ADMIN'或空字符串两种情况,默认空字符串")
    private String grantedUserRole;

    @ApiModelProperty(value = "印章授权状态变更时，回调地址")
    private String notifyUrl;

    @ApiModelProperty(value = "是否获取h5端地址", allowableValues = "false,true")
    private boolean h5;

    @ApiModelProperty("登录token")
    private String token;

    @ApiModelProperty(value = "app协议")
    private String appScheme;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

    @ApiModelProperty(name = "sealOwnerAdminOid", value = "印章归属企业的管理员OID")
    private String sealOwnerAdminOid;
}
