package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 创建资源分享响应结果
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@ApiModel(value = "获取资源访问地址响应结果")
@Getter
@Setter
public class GetResourceUrlResponse extends ToString {

    @ApiModelProperty(value = "资源访问地址")
    private String resourceUrl;

    @ApiModelProperty(value = "是否有权限")
    private Boolean hasAuth;

    @ApiModelProperty(value = "资源扩展字段")
    private Ext ext;

    @ApiModel(value = "资源扩展信息")
    @Getter
    @Setter
    public static final class Ext {

        @ApiModelProperty(value = "资源状态")
        private String resourceStatus;

        @ApiModelProperty(value = "子资源id")
        private String subResourceId;
    }
}
