package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("发起线下法人授权申请响应")
public class OfflineApplyLegalAuthResponse extends ToString {
    @ApiModelProperty(value = "法人授权id")
    private String legalAuthId;
}
