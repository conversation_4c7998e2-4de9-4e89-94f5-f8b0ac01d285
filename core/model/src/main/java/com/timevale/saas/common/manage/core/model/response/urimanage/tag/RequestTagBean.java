package com.timevale.saas.common.manage.core.model.response.urimanage.tag;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
public class RequestTagBean extends ToString {

    @ApiModelProperty("接口标签，以小写字母和下划线组成， 最长不超过40个字符")
    private String tag;

    @ApiModelProperty("接口标签名称， 最长不超过40个字符")
    private String name;

    @ApiModelProperty("接口标签说明， 最长不超过100个字符")
    private String desc;
}
