package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2021-05-31 16:31
 **/
@ApiModel("产品端位置信息")
public class ProductAreaSimpleInfo extends ToString {

    @ApiModelProperty("产品端编码")
    private String productCode;

    @ApiModelProperty("位置编码")
    private String areaCode;

    @ApiModelProperty("位置名称")
    private String  areaName;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }
}
