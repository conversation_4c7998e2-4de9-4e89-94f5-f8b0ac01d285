package com.timevale.saas.common.manage.core.model.model.activity.bean;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.activity.LandingPageConfig;
import com.timevale.saas.common.manage.core.model.model.activity.NoticeConfig;
import com.timevale.saas.common.manage.core.model.model.activity.PosterConfig;
import com.timevale.saas.common.manage.core.model.model.activity.ResultPageConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ActivityConfigInfo extends ToString {

    @ApiModelProperty("活动参与主体类型, PERSON-个人，ORGANIZE-企业")
    private List<String> subjectTypes;

    @ApiModelProperty("通知配置")
    private NoticeConfig noticeConfig;

    @ApiModelProperty("活动落地页配置")
    private LandingPageConfig landingPageConfig;

    @ApiModelProperty("活动结果页配置")
    private ResultPageConfig resultPageConfig;

    @ApiModelProperty("海报配置")
    private PosterConfig posterConfig;

    @ApiModelProperty("额外配置")
    private Map<String, Object> extraConfig;
}
