package com.timevale.saas.common.manage.core.model.enums;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023-07-12 17:48
 */
@Getter
public enum VipFunctionSelectTypeEnum {
    FUNCTION_CODE("FUNCTION_CODE", "功能标识"),
    FUNCTION_NAME("FUNCTION_NAME", "功能名称");

    private String type;
    private String desc;

    VipFunctionSelectTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    /**
     * 根据字符串获取枚举对象
     *
     * @param type
     * @return
     */
    public static VipFunctionSelectTypeEnum convert(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (VipFunctionSelectTypeEnum vipFunctionSelectTypeEnum : values()) {
            if (vipFunctionSelectTypeEnum.getType().equals(type)) {
                return vipFunctionSelectTypeEnum;
            }
        }
        return null;
    }
}
