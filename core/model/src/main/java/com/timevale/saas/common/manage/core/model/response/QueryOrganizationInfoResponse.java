package com.timevale.saas.common.manage.core.model.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 主体详情返回结构 <AUTHOR>
 *
 * @date 2021/11/17
 */
@Data
public class QueryOrganizationInfoResponse extends ToString {

    @ApiModelProperty("主体名称")
    private String subjectName;

    @ApiModelProperty("主体图片")
    private String subjectPic;

    @ApiModelProperty("管理员名称")
    private String subjectAdmin;

    @ApiModelProperty("会员等级")
    private Integer vipLevel;

    @ApiModelProperty("会员等级标识")
    private String vipCode;

    @ApiModelProperty("是否使用会员等级标识")
    private boolean useVipCode;

    @ApiModelProperty("是否即将过期")
    private Boolean expiring;

    @ApiModelProperty("截止时间")
    private Date effectiveTo;

    @ApiModelProperty("起始时间")
    private Date effectiveFrom;

    @ApiModelProperty("是否无限")
    private Boolean unlimitNum = false;

    @ApiModelProperty("剩余量")
    private BigDecimal margin = new BigDecimal(0);

    @ApiModelProperty("计量单位")
    @NeedTranslateField
    private String units;

    @ApiModelProperty("购买量")
    private BigDecimal totalNum = new BigDecimal(0);

    @ApiModelProperty("是否有控制台进入权限")
    private Boolean consolePermissions = false;

    @ApiModelProperty("是否是管理员")
    private Boolean hasAdmin = false;

    @ApiModelProperty("是否是虚拟管理员")
    private Boolean virtualAdmin = false;

    @ApiModelProperty("是否有管理员")
    private Boolean existAdmin = false;

    @ApiModelProperty("是否为多会员版本共享 true 是  false or null 不是共享")
    private Boolean share = false;

    @ApiModelProperty("如果为共享版本 主企业主体名称")
    private String parentTenantName;

    @ApiModelProperty("是否共享企业签署流量")
    private boolean shareSign;

    @ApiModelProperty("共享签署流量的企业名称")
    private String shareSignTenantName;


    @ApiModelProperty("套餐是否即将过期：0.未到期 1. 将到期 2. 已到期")
    private Integer packageExpiring;

    @ApiModelProperty("套餐到期日期")
    private Date packageExpireDate;

    @ApiModelProperty("套餐不足比例，eg：30、15、5，不提醒为null")
    private BigDecimal packageWarnPct;

    @ApiModelProperty("是否有购买套餐权限")
    private Boolean canBuy = false;
    /**
     * 计费产品
     */
    private List<Margin> signMargin = new ArrayList<>();


    @Data
    public static class Margin {

        /**
         * 签署模式
         */
        private String signMode;

        @ApiModelProperty("剩余量")
        private BigDecimal margin = new BigDecimal(0);

        @ApiModelProperty("是否无限")
        private Boolean unlimitNum = false;

        @ApiModelProperty("购买量")
        private BigDecimal totalNum = new BigDecimal(0);

        @ApiModelProperty("计量单位")
        @NeedTranslateField
        private String units;

        @ApiModelProperty("套餐到期日期")
        private Date packageExpireDate;

        @ApiModelProperty("套餐是否即将过期：0.未到期 1. 将到期 2. 已到期")
        private Integer packageExpiring;

        @ApiModelProperty("套餐不足比例，eg：30、15、5，不提醒为null")
        private BigDecimal packageWarnPct;
    }

}
