package com.timevale.saas.common.manage.core.model.request.activities;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2021-01-30
 */
@Data
public class ParticipateActivityRequest extends QueryActivityInfoRequest {

    @ApiModelProperty("参与人账号信息，手机号或邮箱")
    private String account;

    @NotBlank(message = "参与企业名称不能为空")
    @ApiModelProperty("参与企业名称")
    private String orgName;

    @NotBlank(message = "参与企业证件类型不能为空")
    @ApiModelProperty("参与企业证件类型")
    private String orgCertType;

    @NotBlank(message = "参与企业证件号不能为空")
    @ApiModelProperty("参与企业证件号")
    private String orgCertNo;
}
