package com.timevale.saas.common.manage.core.model.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-31
 */

@Data
public class QueryOrgInfoListResponse extends ToString {

    private List<OrgInfo> orgInfos = Lists.newArrayList();

    public void addOrgInfo(String name, String certType, String certNo) {
        OrgInfo orgInfo = new OrgInfo();
        orgInfo.setName(name);
        orgInfo.setCertType(certType);
        orgInfo.setCertNo(certNo);
        orgInfos.add(orgInfo);
    }


    @Data
    public static class OrgInfo extends ToString {
        private String name;

        private String certType;

        private String certNo;
    }
}
