package com.timevale.saas.common.manage.core.model.request.activities;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022-05-24
 */
@Data
public class ParticipateInActivityRequest extends QueryActivityInfoRequest {

    @ApiModelProperty("参与人账号信息，手机号或邮箱")
    private String account;

    @ApiModelProperty("参与人账号id，为空默认取当前登录用户账号id")
    private String accountId;

    @NotBlank(message = "参与主体姓名/名称不能为空")
    @ApiModelProperty("参与主体姓名/名称")
    private String name;

    @NotBlank(message = "参与主体证件类型不能为空")
    @ApiModelProperty("参与主体证件类型")
    private String certType;

    @NotBlank(message = "参与主体证件号不能为空")
    @ApiModelProperty("参与主体证件号")
    private String certNo;

    @ApiModelProperty(value = "clientId前端不用传递",hidden = true)
    private String clientId;
}
