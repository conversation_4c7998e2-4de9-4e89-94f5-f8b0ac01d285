package com.timevale.saas.common.manage.core.model.request.authrelation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2022-04-29 15:14
 */
@Data
public class AuthRelationOperatorRequest {
	@ApiModelProperty(value = "fileKey")
	private String fileKey;
	@ApiModelProperty(value = "开通的gid列表")
	private List<String> gids;
	@ApiModelProperty(value = "授权时长单位，day-天，month-月，year-年")
	private String authUnit;
	@ApiModelProperty(value = "授权时长")
	private Integer authTime;
	@ApiModelProperty(value = "主企业oid")
	private String parentOid;
}
