package com.timevale.saas.common.manage.core.model.request.share;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.share.SharePlatformEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * 资源分享父类
 *
 * <AUTHOR>
 * @since 2020/12/15
 */
@Getter
@Setter
public class BaseResourceShareRequest extends ToString {

    @ApiModelProperty(value = "创建分享的操作人oid", hidden = true)
    private String accountId;

    @ApiModelProperty(value = "创建分享的操作人主体oid", required = true)
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;

    @ApiModelProperty(value = "资源id", required = true)
    @NotBlank(message = "resourceId不能为空")
    private String resourceId;

    /** 请参考{@link com.timevale.saas.common.manage.common.service.enums.share.ResourceTypeEnum} */
    @ApiModelProperty(
            value = "资源类型, FLOW_TEMPLATE-流程模板,PROCESS-单个流程,PROCESS_GROUP-流程组批量",
            required = true)
    @NotBlank(message = "resourceType不能为空")
    private String resourceType;

    /**
     * 请参考{@link com.timevale.saas.common.manage.common.service.enums.share.ShareOperateTypeEnum}
     */
    @ApiModelProperty(value = "分享操作类型 NORMAL_SHARE CHECK BORROW_READ", required = true)
    @NotBlank(message = "shareOperateType不能为空")
    private String shareOperateType;

    @ApiModelProperty(value = "资源分享截至时间 为空表示不限制时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shareEndTime;

    @ApiModelProperty(value = "分享页面路由地址", required = true)
    @NotBlank(message = "router不能为空")
    private String router;

    @ApiModelProperty(value = "分享访问的限制平台, 默认")
    private String platform = SharePlatformEnum.defaultPlatform();

    public String getPlatform() {
        return StringUtils.isBlank(platform) ? SharePlatformEnum.defaultPlatform() : platform;
    }
}
