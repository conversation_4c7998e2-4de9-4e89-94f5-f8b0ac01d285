package com.timevale.saas.common.manage.core.model.response.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2021-05-31 17:26
 **/
@ApiModel("banner上下架响应信息")
public class BannerActiveRes extends DialogInfo{

    @ApiModelProperty(value = "确认后的唯一标示")
    private String ticket;

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }
}
