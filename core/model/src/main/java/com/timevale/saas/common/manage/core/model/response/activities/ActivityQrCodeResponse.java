package com.timevale.saas.common.manage.core.model.response.activities;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021-01-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityQrCodeResponse extends ToString {

    @ApiModelProperty("活动分享id")
    private String shareId;

    @ApiModelProperty("活动分享二维码落地页地址")
    private String shareUrl;

    @ApiModelProperty("活动海报地址")
    private String posterUrl;
}
