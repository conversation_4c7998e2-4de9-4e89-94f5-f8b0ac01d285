package com.timevale.saas.common.manage.core.model.response;

import com.google.common.collect.Lists;
import com.timevale.saas.common.manage.core.model.model.TypeBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */

@Data
@ApiModel("获取任务类型列表响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class TaskTypeListResponse {
    @ApiModelProperty("任务类型列表")
    private List<TypeBean> types = Lists.newArrayList();
}
