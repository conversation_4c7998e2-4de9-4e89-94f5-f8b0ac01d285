package com.timevale.saas.common.manage.core.model.response.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2021-05-31 17:33
 **/
@ApiModel("banner删除响应信息")
public class BannerDeleteRes extends DialogInfo{

    @ApiModelProperty(value = "确认后的唯一标示")
    private String ticket;

    public String getTicket() {
        return ticket;
    }

    public void setTicket(String ticket) {
        this.ticket = ticket;
    }
}
