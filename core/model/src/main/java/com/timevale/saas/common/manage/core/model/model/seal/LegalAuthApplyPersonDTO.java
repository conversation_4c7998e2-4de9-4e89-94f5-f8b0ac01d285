package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("法人章授权申请人信息")
public class LegalAuthApplyPersonDTO {
    @ApiModelProperty(value = "法人章授权申请人姓名")
    private String accountName;

    @ApiModelProperty(value = "法人章授权申请人登录标识")
    private String idCard;

    @ApiModelProperty(value = "法人章授权申请人账号id")
    private String accountId;

    @ApiModelProperty(value = "法人授权申请人证件号")
    private String idNo;

    @ApiModelProperty("申请人是否是法人")
    private boolean legal;
}
