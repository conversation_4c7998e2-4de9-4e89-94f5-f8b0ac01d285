package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.request.vip.bean.VipFunctionInput;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
public class AddVipFunctionListRequest extends ToString {
    
    private List<VipFunctionInput> functionList;
    
}
