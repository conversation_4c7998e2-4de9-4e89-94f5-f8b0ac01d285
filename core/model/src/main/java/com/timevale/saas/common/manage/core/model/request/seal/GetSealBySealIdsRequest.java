package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("根据印章id查询印章详情请求")
public class GetSealBySealIdsRequest extends ToString {

    @ApiModelProperty(name = "orgId", value = "企业id", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiModelProperty(name = "sealIds", value = "印章id，多个用','分隔", required = true)
    @NotBlank(message = "印章id不能为空")
    private String sealIds;

    @ApiModelProperty(name = "downloadFlag", value = "是否返回下载地址，默认false")
    boolean downloadFlag = false;

    @ApiModelProperty(name = "operator", value = "操作人账号，校验用印权限，不传不校验")
    String operator;

    @ApiModelProperty(value = "印章归属OID")
    private String sealOwnerOid;
}
