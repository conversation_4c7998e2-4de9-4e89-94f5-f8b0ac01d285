package com.timevale.saas.common.manage.core.model.model.illustrate;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2022/11/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("功能介绍信息")
public class FunctionIllustrateDTO extends BaseFunctionIllustrateDTO {

    /**功能介绍图片或者视频的文件fileKey*/
    private String showFile;

    /**对内介绍文档地址*/
    private String innerWikiUrl;

    /**扩展信息*/
    private FunctionIllustrateExtDTO ext;
}
