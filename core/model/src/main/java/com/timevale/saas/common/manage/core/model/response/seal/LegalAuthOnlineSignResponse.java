package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("发起线上法人授权签署流程响应")
public class LegalAuthOnlineSignResponse extends ToString {
    @ApiModelProperty("授权记录ID")
    private String legalAuthId;

    @ApiModelProperty("流程ID")
    private String flowId;
}
