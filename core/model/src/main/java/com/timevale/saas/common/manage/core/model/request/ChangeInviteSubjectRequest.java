package com.timevale.saas.common.manage.core.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 * @date 2022/11/14
 */
@Data
@ApiModel("修改邀请主体请求")
public class ChangeInviteSubjectRequest {

    @ApiModelProperty("主体名称")
    private String subjectName;

    @ApiModelProperty("主体oid")
    private String subjectOid;
}
