package com.timevale.saas.common.manage.core.model.response.activities;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.activity.bean.ActivityListInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
public class QueryActivityListResponse extends ToString {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("活动列表")
    private List<ActivityListInfo> activities;
}
