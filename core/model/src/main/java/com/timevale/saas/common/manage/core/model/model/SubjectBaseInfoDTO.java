package com.timevale.saas.common.manage.core.model.model;

import com.timevale.saas.common.manage.common.service.enums.SubjectTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/2/13 主体基本信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubjectBaseInfoDTO {
    /** 所属appid */
    private String appId;
    /** 主体的oid */
    private String oid;
    /** 主体的gid */
    private String gid;
    /** 主体名称 */
    private String name;
    /** 实名状态 */
    private Boolean realName;
    /** 主体类型 */
    private SubjectTypeEnum type;
}
