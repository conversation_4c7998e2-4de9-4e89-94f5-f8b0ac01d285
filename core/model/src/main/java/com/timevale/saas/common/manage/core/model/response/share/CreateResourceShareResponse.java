package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 创建资源分享响应结果
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
public class CreateResourceShareResponse extends ToString {

    @ApiModelProperty(value = "资源分享二维码图片地址")
    private String qrCode;

    @ApiModelProperty(value = "资源分享id")
    private String resourceShareId;

    @ApiModelProperty(value = "资源分享码")
    private String shareCode;

    @ApiModelProperty(value = "资源分享地址,一般为二维码对应的内容")
    private String shareUrl;

    @ApiModelProperty(value = "资源分享短链地址")
    private String shareShortUrl;

    @ApiModelProperty(value = "分享截止时间")
    private Date shareEndTime;

    @ApiModelProperty(value = "资源分享简单版二维码图片地址(logo+二维码)")
    private String simpleQrCode;

    @ApiModelProperty(value = "参与方信息")
    private String shareTitle;

    @ApiModelProperty(value = "参与方信息，不带前缀")
    private String shareTitleValue;
}
