package com.timevale.saas.common.manage.core.model.request.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *
 * @date 2021/11/2
 */
@Data
@ApiModel("位置添加入参")
public class AreaSaveRequest {
    @ApiModelProperty("产品code")
    @NotBlank(message = "请选择位置所属产品")
    @Length(max = 32, message = "产品编码不能超过32个字符")
    private String productCode;

    @ApiModelProperty("位置code")
    @NotBlank(message = "位置编码不能为空")
    @Length(max = 32, message = "位置编码不能超过32个字符")
    private String areaCode;

    @ApiModelProperty("位置名称")
    @NotBlank(message = "位置名称不能为空")
    @Length(max = 32, message = "位置名称不能超过32个字符")
    private String areaName;

    /** {@see AreaTypeConstant}*/
    @ApiModelProperty("位置类型 1banner 2弹窗 3栏目,4问卷")
    @NotNull(message = "请选择位置类型")
    @Min(value = 1, message = "未知位置类型")
    @Max(value = 4, message = "未知位置类型")
    private Integer type;

    @ApiModelProperty("展示形式 1轮播 2独占")
    private Integer displayType = 2;

    @ApiModelProperty("容量")
    private Integer capacity = 1;

    @ApiModelProperty("链接url")
    private String linkUrl;

    @ApiModelProperty("图片")
    private String icon;
}
