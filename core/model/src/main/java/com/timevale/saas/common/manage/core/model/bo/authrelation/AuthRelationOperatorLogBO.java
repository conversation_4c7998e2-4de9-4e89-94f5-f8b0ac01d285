package com.timevale.saas.common.manage.core.model.bo.authrelation;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationOperatorLogDO;
import lombok.Data;
import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2022-05-05 11:55
 */
@Data
public class AuthRelationOperatorLogBO extends ToString {
	private long total;
	private List<AuthRelationOperatorLogDO>  authRelationOperatorLogDOS;
}
