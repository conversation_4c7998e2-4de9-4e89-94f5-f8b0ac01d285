package com.timevale.saas.common.manage.core.model.request.activities;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.activity.LandingPageConfig;
import com.timevale.saas.common.manage.core.model.model.activity.NoticeConfig;
import com.timevale.saas.common.manage.core.model.model.activity.PosterConfig;
import com.timevale.saas.common.manage.core.model.model.activity.ResultPageConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-04-02
 */
@Data
@ApiModel("更新活动请求参数")
public class UpdateActivityRequest extends ToString {

    @ApiModelProperty("活动名称")
    @NotBlank(message = "活动名称不能为空")
    private String activityName;

    @ApiModelProperty("活动参与上限")
    private Integer maxLimit;

    @ApiModelProperty("活动起始时间")
    @NotNull(message = "活动起始时间不能为空")
    private Long startTime;

    @ApiModelProperty("活动截止时间")
    @NotNull(message = "活动截止时间不能为空")
    private Long endTime;

    @ApiModelProperty("活动参与主体类型, PERSON-个人，ORGANIZE-企业")
    private List<String> subjectTypes;

    @ApiModelProperty("通知配置")
    private NoticeConfig noticeConfig;

    @Valid
    @ApiModelProperty("活动落地页配置")
    @NotNull(message = "活动落地页配置不能为空")
    private LandingPageConfig landingPageConfig;

    @ApiModelProperty("活动结果页配置")
    private ResultPageConfig resultPageConfig;

    @ApiModelProperty("海报配置")
    private PosterConfig posterConfig;

    @ApiModelProperty("额外配置")
    private Map<String, Object> extraConfig;
}
