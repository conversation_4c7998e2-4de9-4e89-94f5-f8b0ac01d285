package com.timevale.saas.common.manage.core.model.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/12
 * 印章状态，参照 seal-manage
 */
@Getter
public enum SealStatusEnum {
    TO_BE_VALID((short) -1, "待领取"),
    DROPPED((short) 0, "已删除"),
    VALID((short) 1, "已启用"),
    TO_BE_AUDITED((short) 2, "待审核"),
    REJECTED((short) 3, "已驳回"),
    HANG_UP((short) 4, "已挂起"),
    WAIT_SUBMIT((short) 5, "待提交");

    private final Short value;
    private final String msg;

    SealStatusEnum(Short value, String msg) {
        this.value = value;
        this.msg = msg;
    }

    public Short value() {
        return this.value;
    }

    public static SealStatusEnum of(Short value) {
        SealStatusEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            SealStatusEnum status = var1[var3];
            if (status.value().equals(value)) {
                return status;
            }
        }

        return null;
    }

    public static boolean checkStatus(Short status) {
        return null != of(status);
    }

    public static List<Short> getExpectDelete() {
        return Arrays.asList(
                TO_BE_VALID.value,
                VALID.value(),
                TO_BE_AUDITED.value,
                REJECTED.value,
                HANG_UP.value(),
                WAIT_SUBMIT.value);
    }
}
