package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * QueryVipRequest
 *
 * <AUTHOR>
 * @since 2021/5/27 3:51 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryVipRequest extends ToString {

    /**
     * gid列表
     */
    @NotNull(message = "GID列表不能为空")
    @NotEmpty(message = "GID列表不能为空")
    @Size(max = 500, message = "GID不能超过500条")
    @ApiModelProperty(value = "租户id列表", required = true)
    private List<String> gidList;
}
