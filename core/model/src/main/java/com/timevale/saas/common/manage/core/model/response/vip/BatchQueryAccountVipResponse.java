package com.timevale.saas.common.manage.core.model.response.vip;

import com.google.common.collect.Lists;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipFunctionBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-08-21
 */
@Data
@ApiModel("获取用户会员信息响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class BatchQueryAccountVipResponse extends ToString {

    @ApiModelProperty("会员功能列表")
    private List<VipFunctionBean> functions = Lists.newArrayList();

}
