package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("发起线下法人授权申请请求")
public class OfflineApplyLegalAuthRequest extends ToString {

    @ApiModelProperty("企业id")
    @NotBlank(message = "组织oid不能为空")
    private String orgId;

    @ApiModelProperty(value = "授权文档fileKey")
    private String authDocFileKey;

    @ApiModelProperty(value = "身份证正面照fileKey")
    private String authIDFrontFileKey;

    @ApiModelProperty(value = "身份证发面照fileKey")
    private String authIDReverseFileKey;

    @ApiModelProperty(value = "法人证件号")
    private String legalNumber;

    @ApiModelProperty(value = "法人证件类型")
    private String legalNoType;
}
