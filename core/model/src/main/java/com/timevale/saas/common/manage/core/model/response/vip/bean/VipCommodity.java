package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class VipCommodity extends ToString {

    @ApiModelProperty("会员等级")
    private Integer level;

    @ApiModelProperty("会员等级标识")
    private String vipCode;

    @ApiModelProperty("是否使用会员等级标识")
    private boolean useVipCode;

    @ApiModelProperty("会员等级名称")
    private String levelName;

    @ApiModelProperty("会员版本对应的商品id列表")
    private List<String> commodities;

    @Deprecated
    @ApiModelProperty("图片信息")
    private VipImage vipImage;
}
