package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *
 * @date 2021/11/1
 */
@Data
@ApiModel("弹窗点击入参")
public class PopupClickRequest extends PopupCallbackRequest {

    @ApiModelProperty("点击类型 1点击链接 2关闭")
    @NotNull(message = "操作类型不可为空")
    @Min(value = 1, message = "无效操作类型")
    @Max(value = 2, message = "无效操作类型")
    private Integer type;
}
