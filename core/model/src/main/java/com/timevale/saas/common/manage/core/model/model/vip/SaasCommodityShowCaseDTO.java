package com.timevale.saas.common.manage.core.model.model.vip;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SaasCommodityShowCaseDTO {

    public List<ClientShowCase> person;
    public List<ClientShowCase> org;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ClientShowCase {
        private String clientId;
        private String showCaseNo;
    }
}
