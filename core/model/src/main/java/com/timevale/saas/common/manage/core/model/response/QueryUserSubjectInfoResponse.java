package com.timevale.saas.common.manage.core.model.response;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.SubjectFlagEnum;
import com.timevale.saas.common.manage.common.service.enums.SubjectTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/13 主体信息查询响应
 */
@Data
@ApiModel("用户主体信息查询响应")
@NoArgsConstructor
@AllArgsConstructor
public class QueryUserSubjectInfoResponse extends ToString {
    @ApiModelProperty("总数")
    private Integer total;

    @ApiModelProperty("用户所属主体信息")
    private List<UserSubjectVO> userSubjects;

    /**
     * <AUTHOR>
     * @since 2022/2/13
     */
    @Data
    @ApiModel("用户主体信息")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserSubjectVO extends ToString {

        @ApiModelProperty("主体功能标识")
        private List<SubjectFlagEnum> flags;

        @ApiModelProperty("主体oid")
        private String oid;

        @ApiModelProperty("主体gid")
        private String gid;

        @ApiModelProperty("主体名称")
        private String name;

        @ApiModelProperty("主体类型")
        private SubjectTypeEnum type;

        @ApiModelProperty("主体是否实名")
        private Boolean realName;

        @ApiModelProperty("企业主体下的用户角色信息")
        private List<UseSubjectRoleVO> userRoles;
    }

    /**
     * <AUTHOR>
     * @since 2022/2/13
     */
    @Data
    @ApiModel("用户主体下的角色信息")
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UseSubjectRoleVO extends ToString {

        @ApiModelProperty("角色id")
        private String id;

        @ApiModelProperty("角色描述")
        private String disc;

        @ApiModelProperty("角色名称")
        private String name;

        @ApiModelProperty("角色创建时间")
        private Long createTime;
    }
}
