package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.FileDocDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("压缩，透明化处理印章图片响应")
public class CompressSealResponse extends ToString {
    @ApiModelProperty(value = "透明处理后的图片信息列表")
    private List<FileDocDTO> seals;
}
