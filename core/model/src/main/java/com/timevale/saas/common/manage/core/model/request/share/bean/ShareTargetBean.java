package com.timevale.saas.common.manage.core.model.request.share.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

@ApiModel(value = "分享对象")
@Getter
@Setter
public class ShareTargetBean extends ToString {
    /** 分享对象主体oid */
    private String targetSubjectOid;
    /** 分享对象主体gid */
    private String targetSubjectGid;
    /** 分享对象操作人oid */
    private String targetOperatorOid;
    /** 分享对象操作人gid */
    private String targetOperatorGid;
    /**分享对象操作人账号**/
    private String targetOperatorAccount;
    /** 资源分享对象方操作人姓名 */
    private String targetOperatorName;
}
