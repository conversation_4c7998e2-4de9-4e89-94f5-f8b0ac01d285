package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2021-05-31 17:25
 **/
@ApiModel("弹窗信息")
public class DialogInfo extends ToString {

    @ApiModelProperty(value = "是否弹窗", required = true)
    private Boolean popFlag;

    @ApiModelProperty("弹窗提示信息")
    private String popMsg;


    public Boolean getPopFlag() {
        return popFlag;
    }

    public void setPopFlag(Boolean popFlag) {
        this.popFlag = popFlag;
    }

    public String getPopMsg() {
        return popMsg;
    }

    public void setPopMsg(String popMsg) {
        this.popMsg = popMsg;
    }
}
