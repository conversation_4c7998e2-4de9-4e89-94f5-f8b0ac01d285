package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-31 16:48
 **/
@ApiModel("banner管理列表分页响应信息")
public class BannerPageRes extends ToString {

    @ApiModelProperty("总数")
    private Integer total;

    @ApiModelProperty("banner列表")
    private List<BannerInfo> list;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<BannerInfo> getList() {
        return list;
    }

    public void setList(List<BannerInfo> list) {
        this.list = list;
    }
}
