package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author:jiany<PERSON>
 * @since 2021-07-05 16:11
 */
@Data
public class GetShareTargetsResourceResponse extends ToString {
	@ApiModelProperty(value = "分享人")
	private String shareTargetName;

	@ApiModelProperty(value = "分享人联系方式")
	private String shareTargetContact;

	@ApiModelProperty(value = "分享对象操作状态,0:初始状态,1:已查看")
	private Integer operateStatus;

	@ApiModelProperty(value = "查看时间")
	private Date operateTime;
}
