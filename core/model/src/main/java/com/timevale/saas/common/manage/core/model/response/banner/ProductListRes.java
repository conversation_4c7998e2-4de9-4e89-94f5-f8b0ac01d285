package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 产品列表响应参数
 *
 * <AUTHOR>
 * @since 2021-05-31 11:47
 **/
@ApiModel("产品列表信息")
public class ProductListRes extends ToString {

    @ApiModelProperty("产品列表")
    private List<ProductInfo> list;


    public List<ProductInfo> getList() {
        return list;
    }

    public void setList(List<ProductInfo> list) {
        this.list = list;
    }

    public ProductListRes() {
    }

    public ProductListRes(List<ProductInfo> list) {
        this.list = list;
    }
}
