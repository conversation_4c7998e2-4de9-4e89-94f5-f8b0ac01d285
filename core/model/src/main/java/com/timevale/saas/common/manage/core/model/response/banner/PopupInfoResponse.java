package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.enums.NoticeTypeEnum;
import com.timevale.saas.common.manage.core.model.model.banner.PictureDTO;
import com.timevale.saas.common.manage.core.model.model.banner.PushUserConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/2
 */
@Data
@ApiModel("弹窗详情结构")
public class PopupInfoResponse extends ToString {

    @ApiModelProperty("唯一标识")
    private Long id;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("位置编码")
    private String areaCode;

    @ApiModelProperty("图片地址")
    private String pictureUrl;

    @ApiModelProperty("跳转链接")
    private String linkUrl;

    @ApiModelProperty("有效用户类型 0全部 1指定 2指定功能灰度")
    private List<Integer> validityUserType;

    @ApiModelProperty("有效开始时间")
    private Date validityStartTime;

    @ApiModelProperty("有效结束时间")
    private Date validityEndTime;

    @ApiModelProperty("弹出配置类型 0首次")
    private Integer configType;

    @ApiModelProperty("扩展配置")
    private String configInfo;

    @ApiModelProperty("权重")
    private Integer weight;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("位置名称")
    private String areaName;

    @ApiModelProperty("展示图片key")
    private String fileKey;

    @ApiModelProperty("弹出名称")
    private String name;

    @ApiModelProperty("有效用户配置")
    private List<PushUserConfigDTO> validityUserConfig;

    @ApiModelProperty("指定推送用户数量")
    private Long pushUserCount;

    /**
     * @see NoticeTypeEnum 公告类型枚举
     */
    @ApiModelProperty("公告类型:1.产品迭代更新、2.新功能上线、3.交互体验升级、4.系统问题通知、5.其他")
    private Integer noticeType;

    @ApiModelProperty("弹窗类型，1. 单一图片 2.轮播图片")
    private Integer popupType;

    @ApiModelProperty("支持的vip版本，为空表示所有版本都展示")
    private List<String> notifyVersions;

    @ApiModelProperty("支持的角色，为空表示所有角色都展示")
    private List<String> notifyRoles;

    @ApiModelProperty("轮播图片列表")
    private List<PictureDTO> scrollPictures;
}
