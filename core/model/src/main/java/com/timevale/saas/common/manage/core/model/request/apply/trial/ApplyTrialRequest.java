package com.timevale.saas.common.manage.core.model.request.apply.trial;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 申请试用入参
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
public class ApplyTrialRequest extends ToString {

    //功能标识
    @NotBlank
    private String functionCode;
    
    // 申请人oid
    private String applicantOid;

    // 申请人主体oid
    private String applicantSubjectOid;
    
    //联系手机号
    @Pattern(regexp = "^[0-9]{11}$", message = "请输入正确的手机号")
    private String phone;
}
