package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.saas.common.manage.core.model.model.banner.PictureDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/3
 **/
@Data
public class ProductPopupInfoResponse {

    @ApiModelProperty("唯一标识")
    private Long id;

    @ApiModelProperty("展示图片地址")
    private String picUrl;

    @ApiModelProperty("跳转链接")
    private String linkUrl;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("弹窗类型，1. 单一图片 2.轮播图片")
    private Integer popupType;

    @ApiModelProperty("轮播图片列表")
    private List<PictureDTO> scrollPictures;

}
