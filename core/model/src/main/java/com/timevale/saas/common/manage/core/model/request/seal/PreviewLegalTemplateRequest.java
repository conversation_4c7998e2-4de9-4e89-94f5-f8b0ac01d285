package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("法人章预览请求")
public class PreviewLegalTemplateRequest extends ToString {
    @NotBlank(message = "印章模板样式不能为空")
    @ApiModelProperty(
            value =
                    "印章样式<br>"
                            + "矩形章-带框（RECTANGLE_BORDER）<br>"
                            + "矩形章-不带框(RECTANGLE_NO_BORDER)<br>"
                            + "方形左大字-带框（SQUARE_LEFT_BORDER）<br>"
                            + "方形左大字-不带框（SQUARE_LEFT_NO_BORDER）<br>"
                            + "方形右大字-带框（SQUARE_RIGHT_BORDER）<br>"
                            + "方形右大字-不带框（SQUARE_RIGHT_NO_BORDER）")
    @Pattern(
            regexp =
                    "RECTANGLE_BORDER|RECTANGLE_NO_BORDER|SQUARE_LEFT_BORDER|SQUARE_LEFT_NO_BORDER|SQUARE_RIGHT_BORDER|SQUARE_RIGHT_NO_BORDER",
            message = "印章模板样式不正确")
    private String templateType;

    @NotBlank(message = "印章尺寸不能为空")
    @ApiModelProperty(
            value =
                    "印章尺寸：10_20、20_20等枚举值，见印章尺寸枚举<br>"
                            + "矩形章-带框：10_20<br>"
                            + "矩形章-不带框：10_20<br>"
                            + "方形左大字-带框:20_20 、18_18 、 16_16 (其他方形章也一样)")
    private String widthHeight;

    @NotBlank(message = "印章颜色不能为空")
    @ApiModelProperty(value = "印章颜色，RED-红色，BLUE-蓝色，BLACK-黑色，PURPLE-紫色")
    @Pattern(regexp = "RED|BLUE|BLACK|PURPLE", message = "印章颜色不正确")
    private String color = "RED";

    @NotNull(message = "不透明度不能为空")
    @Min(value = 1, message = "不透明度必须在1~100以内")
    @Max(value = 100, message = "不透明度必须在1~100以内")
    @ApiModelProperty(value = "不透明度 0~100。 100表示不透明")
    private Short opacity = 60;

    @ApiModelProperty(
            value = "印章做旧样式，NONE-无，OLD-印章做旧(随机样式，如果需指定做旧样式，指定1-12样式id，如OLD_1、OLD_2)，默认NONE")
    @Pattern(
            regexp =
                    "NONE|OLD_1|OLD_2|OLD_3|OLD_4|OLD_5|OLD_6|OLD_7|OLD_8|OLD_9|OLD_10|OLD_11|OLD_12",
            message = "做旧样式不正确")
    private String style = "NONE";

    @ApiModelProperty(value = "印后缀规则, 0什么都不加，1加印，2加之印")
    @Min(value = 0, message = "内容规则不正确")
    @Max(value = 2, message = "内容规则不正确")
    private Integer stampRule = 0;

    @ApiModelProperty(value = "印章归属OID")
    private String sealOwnerOid;
}
