package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @since 2022/7/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("企业图片印章创建请求")
public class CreateOrgImageSealRequest extends ToString {

    @ApiModelProperty(value = "操作类型，sava保存，submit提交")
    private String operateType;

    @ApiModelProperty(value = "印章类型，3-自定义图片，4-手绘，默认3")
    private Short sealType = 3;

    @ApiModelProperty(value = "是否需要审核，默认false")
    private boolean auditFlag;

    @ApiModelProperty(value = "是否需要返回印章下载地址，默认true")
    private boolean downloadFlag = true;

    @ApiModelProperty(value = "是否需要对图片做单色处理，默认false")
    private boolean handleFlag;

    @ApiModelProperty(
            value =
                    "印章业务类型，COMMON-无业务类型(其他)，CANCELLATION-作废章, PUBLIC-公章，CONTRACT-合同专用章, FINANCE-财务章,  LEGAL_PERSON-法人章， PERSONNEL-人事专用章")
    private String sealBizType;

    @ApiModelProperty(value = "印章数据", required = true)
    private String data;

    @ApiModelProperty(value = "印章数据类型，BASE64-base64格式，FILEKEY-文件filekey")
    private String type;

    @ApiModelProperty(value = "印章是否做透明处理，默认false")
    private boolean transparentFlag = false;

    @ApiModelProperty(value = "上传文件的md5值")
    private String uploadFileMd5;

    /** 功能版本 */
    @ApiModelProperty(hidden = true)
    private Integer functionVersion;

    @ApiModelProperty(value = "印章别名")
    private String alias;

    @ApiModelProperty(value = "印章宽度")
    private Integer width;

    @ApiModelProperty(value = "印章高度")
    private Integer height;

    /** sealRef状态，这个状态不对外 请搜SealStatus类 */
    @ApiModelProperty(hidden = true)
    private Short sealStatus;

    @ApiModelProperty(value = "开发者appid", hidden = true)
    private String devAppId;

    @ApiModelProperty(value = "印章审核、领取回调地址，最长1024字符")
    @Length(max = 1024, message = "回调地址最长1024个字符")
    private String notifyUrl;

    @ApiModelProperty(value = "英文章备案材料fileKey")
    private String materialFileKeyOfEng;

    @ApiModelProperty(value = "印章归属OID")
    private String sealOwnerOid;
}
