package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.saas.common.manage.core.model.model.banner.ColumnInfoDTO;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/22
 */
@Data
@ApiModel("应用栏目内容返回结构")
public class AppColumnListResponse {

    @ApiModelProperty("位置code")
    private String areaCode;
    /**
     * 栏目位置名称
     */
    @NeedTranslateField
    private String areaName;
    @ApiModelProperty("跳转链接")
    private String linkUrl;
    @ApiModelProperty("图片")
    private String iconUrl;
    @HasTranslateField
    private List<ColumnInfoDTO> data;
    @ApiModelProperty("是否有下一页(建议使用)")
    private Boolean hasMore;
    @ApiModelProperty("总数据量")
    private Integer total;
}
