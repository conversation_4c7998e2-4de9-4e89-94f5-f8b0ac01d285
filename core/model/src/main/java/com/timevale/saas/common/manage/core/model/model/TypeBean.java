package com.timevale.saas.common.manage.core.model.model;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.TaskFailExportTypeEnum;
import com.timevale.saas.common.validator.EnumCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020-08-19
 */
@Data
@ApiModel("任务类型信息")
@NoArgsConstructor
@AllArgsConstructor
public class TypeBean extends ToString {
    @NotNull(message = "任务类型不能为空")
    @ApiModelProperty("任务类型")
    private Integer type;

    @NotBlank(message = "任务类型名称不能为空")
    @ApiModelProperty("任务类型名称")
    private String name;

    @ApiModelProperty("任务类型显示文案, 默认和任务类型名称一致")
    private String label;

    @ApiModelProperty("任务类型说明")
    private String desc;

    @ApiModelProperty("是否批量任务， 默认否")
    private boolean grouped;

    @ApiModelProperty("失败导出方式")
    @EnumCheck(target = TaskFailExportTypeEnum.class, enumField = "type", message = "不支持的失败导出方式")
    private String failExportType;

    @ApiModelProperty("任务结果处理器所在服务")
    private String handlerApp;

    @ApiModelProperty("任务结果处理器方法")
    private String handlerRpc;

    @ApiModelProperty("任务结果处理器方法返回值中失败导出地址对应的字段名")
    private String handlerFailExportField;
}
