package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取二次授权响应")
public class GetSecondRuleGrantListResponse extends ToString {
    @ApiModelProperty(value = "二级授权业务id")
    private String secGrantBizId;

    @ApiModelProperty(value = "授权人oid")
    private String granter;

    @ApiModelProperty(value = "授权人名称")
    private String granterName;

    @ApiModelProperty(value = "被授权人oid")
    private String grantedUser;

    @ApiModelProperty(value = "被授权人名称")
    private String grantedUserName;

    @ApiModelProperty(value = "是否自动落章")
    private Boolean autoFall;

    @ApiModelProperty(value = "落章类型")
    private Integer fallType;

    @ApiModelProperty(value = "角色类型")
    private String roleKey;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "授权范围编码")
    private String scope;

    @ApiModelProperty(value = "授权范围描述")
    private String scopeName;

    @ApiModelProperty(value = "生效时间")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间")
    private Long expireTime;

    @ApiModelProperty(value = "是否开启审批通知")
    private Boolean notifySetting;

    @ApiModelProperty(value = "授权状态编码")
    private Integer status;

    @ApiModelProperty(value = "授权状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "过期原因编码")
    private String reason;

    @ApiModelProperty(value = "过期原因描述")
    private String reasonDesc;

    @ApiModelProperty(value = "流程id")
    private String flowId;
}
