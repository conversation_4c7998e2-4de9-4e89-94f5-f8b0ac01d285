package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class BatchQueryAccountVipRequest extends ToString {
    @NotEmpty
    @Size(max = 50) // 目前先仅支持查询1个
    @ApiModelProperty("会员功能标识列表")
    private List<String> functionCodes;
}
