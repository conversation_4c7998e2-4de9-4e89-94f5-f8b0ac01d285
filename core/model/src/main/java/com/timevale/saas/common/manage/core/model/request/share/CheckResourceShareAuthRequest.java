package com.timevale.saas.common.manage.core.model.request.share;

import com.timevale.mandarin.common.result.ToString;

import javax.validation.constraints.NotBlank;

import lombok.Getter;
import lombok.Setter;

/**
 * 校验资源分享权限参数
 *
 * <AUTHOR>
 * @since 2020/12/11
 */
@Getter
@Setter
public class CheckResourceShareAuthRequest extends ToString {

    /** 当前访问操作人oid */
    @NotBlank(message = "accountId不能为空")
    private String accountId;

    /** 当前访问主体oid */
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;

    /** 资源分享id */
    @NotBlank(message = "resourceShareId不能为空")
    private String resourceShareId;

    /** 资源id */
    @NotBlank(message = "resourceId不能为空")
    private String resourceId;
}
