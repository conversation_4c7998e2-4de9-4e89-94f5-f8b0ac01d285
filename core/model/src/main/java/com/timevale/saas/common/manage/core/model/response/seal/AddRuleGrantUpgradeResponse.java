package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.UserRuleGrantDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("新增印章授权(兼容二次授权)响应")
public class AddRuleGrantUpgradeResponse extends FirstGrantBaseResponse {
    @ApiModelProperty(value = "授权id列表", required = true)
    private List<String> ruleGrantIds;

    @ApiModelProperty(value = "授权人与授权记录列表", required = true)
    private List<UserRuleGrantDTO> ruleGrantInfos;
}
