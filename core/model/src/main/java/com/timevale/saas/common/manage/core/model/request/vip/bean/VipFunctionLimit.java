package com.timevale.saas.common.manage.core.model.request.vip.bean;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.VipLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

@Data
public class VipFunctionLimit extends ToString {

    @ApiModelProperty("会员等级")
    private Integer level;

    @ApiModelProperty("会员等级标识")
    private String vipCode;

    @ApiModelProperty("是否支持")
    private boolean enable;

    @ApiModelProperty("会员功能限制，max_batch_count表示批量最大上限")
    private Map<String, Object> limit;

    public String getVipCode() {
        if (StringUtils.isBlank(vipCode) && null != level) {
            vipCode = VipLevelEnum.getVipLevelByValue(level).getCode();
        }
        return vipCode;
    }
}
