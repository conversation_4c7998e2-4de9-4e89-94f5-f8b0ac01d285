package com.timevale.saas.common.manage.core.model.bo.share;

import com.timevale.saas.common.manage.common.service.enums.share.ResourceTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.share.ShareOperateTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.share.SharePlatformEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 资源分享插件化扩展点定义
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@Getter
@Setter
public class BaseResourceShareBO {

    /** 资源分享id */
    private String resourceShareId;
    /** 资源id */
    private String resourceId;
    /** 资源类型 请参考{@link ResourceTypeEnum} */
    private String resourceType;
    /** 分享码 */
    private String shareCode;
    /** 分享url */
    private String shareUrl;
    /** 状态 */
    private int status;
    /** 分享操作类型 请参考{@link ShareOperateTypeEnum} */
    private String shareOperateType;
    /** 分享限制访问平台 请参考{@link SharePlatformEnum} */
    private String platform;
    /** 是否需要实名 */
    private boolean needRealName;
    /** 是否跳过实名，目前只有海外签跳过 */
    private boolean skipRealName;
    /** 资源分享截至时间 为空表示不限制时间 */
    private Date shareEndTime;
    /** 分享描述信息（可能为富文本） */
    private String shareDesc;
    /*分享二维码图片fileKey*/
    private String shareQrCodeFileKey;
    /** 简单版分享二维码图片fileKey */
    private String simpleShareQrCodeFileKey;
    /** 分享二维码：参与人信息（带前缀） */
    private String shareTitle;
    /** 简单版分享二维码：参与人信息（不带前缀） */
    private String shareTitleValue;
    /** 创建时间 */
    private Date createTime;
    /** 更新 */
    private Date updateTime;

    public void clear() {
        this.shareUrl = null;
        this.shareCode = null;
        this.shareQrCodeFileKey = null;
        this.simpleShareQrCodeFileKey = null;
        this.shareTitle = null;
        this.shareTitleValue = null;
    }
}
