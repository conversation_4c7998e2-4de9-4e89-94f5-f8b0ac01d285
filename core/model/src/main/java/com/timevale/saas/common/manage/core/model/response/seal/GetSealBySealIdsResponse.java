package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.saas.common.manage.core.model.model.seal.SealInfoDetailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("根据印章id查询印章详情响应")
public class GetSealBySealIdsResponse {
    @ApiModelProperty(value = "印章列表")
    private List<SealInfoDetailDTO> seals;
}
