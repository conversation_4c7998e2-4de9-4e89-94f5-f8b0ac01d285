package com.timevale.saas.common.manage.core.model.request.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("编辑印章授权请求")
public class UpdateRuleGrantRequest extends UrlBaseRequest {

    @ApiModelProperty(name = "orgId", value = "企业id", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiModelProperty(name = "ruleGrantedId", value = "规则授权id", required = true)
    @NotBlank(message = "规则授权id不能为空")
    private String ruleGrantedId;

    @ApiModelProperty(value = "资源id", required = true)
    private String resourceId;

    @ApiModelProperty(value = "资源类型", required = true)
    private String resourceType;

    @ApiModelProperty(value = "模板key", required = true)
    private String templateKey;

    @ApiModelProperty(value = "角色类型", required = true)
    private String roleKey;

    /**
     * 官网再用
     */
    @ApiModelProperty(value = "自动落章")
    private Boolean autoFall;

    /**
     * 钉签再用
     */
    @ApiModelProperty(value = "落章方式，0-手动落章，1-免意愿且自动落章，2-免意愿且手动落章， 默认手动落章")
    private Integer fallType;

    @ApiModelProperty(value = "授权范围(模板id/ALL/HR/FINANCE)", required = true)
    private String scope;

    @ApiModelProperty(value = "通知配值(默认开启)")
    private Boolean notifySetting = true;

    @ApiModelProperty(value = "授权人", required = true)
    private String granter;

    @ApiModelProperty(value = "授权人名称", required = true)
    private String granterName;

    @ApiModelProperty(value = "授权人证件号", required = true)
    private String granterCode;
    /**
     * 最初被授权人oid是通过grantedUser字段传参的，但后来需求上被授权人可以是多个，就增加了个grantedAccountIds属性，后续业务里就使用grantedAccountIds属性
     */
    @ApiModelProperty(value = "被授权对象", required = true)
    private String grantedUser;

    @ApiModelProperty(value = "被授权对象", required = true)
    private List<String> grantedAccountIds;

    @ApiModelProperty(value = "被授权对象证件号")
    private String grantedUserCode;

    @ApiModelProperty(value = "被授权对象")
    private String grantedUserName;

    @ApiModelProperty(value = "生效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "生效时间不能为空")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间(毫秒级时间戳)", required = true)
    @NotNull(message = "失效时间不能为空")
    private Long expireTime;

    @ApiModelProperty(value = "失效原因", required = true)
    private String expireReason;

    @ApiModelProperty(value = "授权签署重定向地址")
    private String grantRedirectUrl;

    @ApiModelProperty(value = "授权类型 ，1-企业内，2-企业间，默认1", required = true)
    private Integer grantType;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

    @ApiModelProperty(name = "sealOwnerAdminOid", value = "印章归属企业的管理员OID")
    private String sealOwnerAdminOid;
}
