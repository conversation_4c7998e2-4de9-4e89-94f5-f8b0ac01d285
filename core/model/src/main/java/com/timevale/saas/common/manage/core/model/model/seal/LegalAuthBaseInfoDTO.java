package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("法人章授权基本信息")
public class LegalAuthBaseInfoDTO {
	@ApiModelProperty(value = "法人授权数据唯一标识")
	private String legalAuthId;

	@ApiModelProperty(value = "授权状态，0-未发起过授权，2-授权中，3-授权失败，1-授权成功，4-授权失效")
	private Integer authStatus;

	@ApiModelProperty(value = "授权类型，1-在线授权，2-线下纸质授权")
	private Integer authType;

	@ApiModelProperty(value = "授权失效原因类型，1-管理员变更，2-法人变更，9-其它")
	private Integer expireType;

	@ApiModelProperty(value = "授权驳回原因")
	private String rejectReason;
}
