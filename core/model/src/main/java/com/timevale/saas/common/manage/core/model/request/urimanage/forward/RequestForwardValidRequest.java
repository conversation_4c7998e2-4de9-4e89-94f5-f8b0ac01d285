package com.timevale.saas.common.manage.core.model.request.urimanage.forward;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020-10-21
 */
@Data
@ApiModel("停用/启用saas接口转发配置请求参数")
public class RequestForwardValidRequest extends ToString {

    @ApiModelProperty("规则配置状态， true表示启用， false表示停用")
    private boolean valid;
}
