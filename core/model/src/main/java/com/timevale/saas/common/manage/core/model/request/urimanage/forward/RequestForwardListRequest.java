package com.timevale.saas.common.manage.core.model.request.urimanage.forward;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020-10-21
 */
@Data
@ApiModel("新增接口转发及参数替换规则请求参数")
public class RequestForwardListRequest extends ToString {

    @Length(max = 100, message = "接口地址最长不可超过100字符")
    @ApiModelProperty("接口地址")
    private String uri;

    @ApiModelProperty("规则状态, true-启用， false-停用")
    private Boolean valid;

    @ApiModelProperty("接口标签， 多个以逗号分隔")
    private String tags;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码, 默认1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    @NotNull(message = "页行数不能为空")
    @ApiModelProperty("页行数, 默认20")
    @Max(value = 100, message = "每页最多获取100条数据")
    private Integer pageSize;

    public String likeUri() {
        if (StringUtils.isNotBlank(uri) && !uri.endsWith("%")) {
            return uri + "%";
        }
        return uri;
    }
}
