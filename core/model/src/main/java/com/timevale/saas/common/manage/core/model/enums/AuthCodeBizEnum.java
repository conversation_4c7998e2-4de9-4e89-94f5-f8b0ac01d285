package com.timevale.saas.common.manage.core.model.enums;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import lombok.Getter;

/**
 * 验证码业务类型枚举
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Getter
public enum AuthCodeBizEnum {
    APPLY_TRIAL("apply-trial", "e签宝", "申请试用收费版本", 15*60, 60,
            "", "申请试用"),
    ;

    //业务类型
    private String bizTag;
    //模板参数
    private String displayName;
    //模板参数
    private String bizOperation;
    //验证过期时间，单位/秒
    private int codeExpire;
    //发送间隔，单位/秒
    private int sendingInterval;
    //验证成功后的有效时间
    private int authOkExpire;
    private String templateId;
    private String desc;

    AuthCodeBizEnum(String bizTag, String displayName, String bizOperation, int codeExpire, int sendingInterval,
                    int authOkExpire, String templateId, String desc) {
        this.bizTag = bizTag;
        this.displayName = displayName;
        this.bizOperation = bizOperation;
        this.codeExpire = codeExpire;
        this.sendingInterval = sendingInterval;
        this.authOkExpire = authOkExpire;
        this.templateId = templateId;
        this.desc = desc;
    }

    AuthCodeBizEnum(String bizTag, String displayName, String bizOperation, int codeAndOkExpire, int sendingInterval,
                    String templateId, String desc) {
        this.bizTag = bizTag;
        this.displayName = displayName;
        this.bizOperation = bizOperation;
        this.codeExpire = codeAndOkExpire;
        this.sendingInterval = sendingInterval;
        this.authOkExpire = codeAndOkExpire;
        this.templateId = templateId;
        this.desc = desc;
    }

    public static AuthCodeBizEnum from(String bizTag) {
        for (AuthCodeBizEnum value : AuthCodeBizEnum.values()) {
            if (value.bizTag.equals(bizTag)) {
                return value;
            }
        }
        throw new SaasCommonBizException(ResultEnum.PARAM_ILLEGAL, String.format("未知的验证码业务类型:%s", bizTag));
    }
}
