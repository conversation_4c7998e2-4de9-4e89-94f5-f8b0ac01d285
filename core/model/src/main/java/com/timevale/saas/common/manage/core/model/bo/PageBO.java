package com.timevale.saas.common.manage.core.model.bo;

import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * 通用page bo 对象
 *
 * @param <T>
 */
@Data
public class PageBO<T> {
    private Long total;
    private List<T> list;

    public static <T> PageBO<T> instance(Long total, List<T> list) {
        total = total == null || total < 0 ? 0 : total;
        list = list == null ? Collections.emptyList() : list;
        PageBO<T> pageBO = new PageBO<>();
        pageBO.setTotal(total);
        pageBO.setList(list);
        return pageBO;
    }

    public static <T> PageBO<T> empty() {
        PageBO<T> pageBO = new PageBO<>();
        pageBO.setTotal(0L);
        pageBO.setList(Collections.emptyList());
        return pageBO;
    }
}
