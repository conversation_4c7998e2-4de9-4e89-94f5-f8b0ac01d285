package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * VipImage
 *
 * <AUTHOR>
 * @since 2022/4/18 10:39 上午
 */
@Data
public class VipImage extends ToString {

    @ApiModelProperty("共享图片")
    private String sharedImage;

    @ApiModelProperty("非共享图片")
    private String unSharedImage;
}
