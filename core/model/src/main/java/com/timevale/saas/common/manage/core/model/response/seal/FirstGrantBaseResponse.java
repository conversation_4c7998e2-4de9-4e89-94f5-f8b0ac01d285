package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> 初霁
 * @version V1.0
 * @Project: saas-common-manage
 * @Description: 印章授权基础响应
 * @date Date : 2023年11月30日 17:37
 */
@Data
public class FirstGrantBaseResponse extends ToString {

    @ApiModelProperty(value = "流程id")
    private String flowId;

    @ApiModelProperty(value = "授权签署url")
    private String signUrl;

    @ApiModelProperty(value = "授权签署长链")
    private String longSignUrl;
}
