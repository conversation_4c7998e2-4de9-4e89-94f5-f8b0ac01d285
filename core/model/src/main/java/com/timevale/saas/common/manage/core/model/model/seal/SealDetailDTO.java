package com.timevale.saas.common.manage.core.model.model.seal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/12
 * 印章详细信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SealDetailDTO {
    private String id;
    private String gid;
    private String uid;
    private String oid;
    private String alias;
    private Short sealType;
    private Integer bizType;
    private String fileKey;
    private Integer width;
    private Integer height;
    private Short status;
    private Integer bizScene;
    private boolean recorded;
    private Long modifyDate;
}
