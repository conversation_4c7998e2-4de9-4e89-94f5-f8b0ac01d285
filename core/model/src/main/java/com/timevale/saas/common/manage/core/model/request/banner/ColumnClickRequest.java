package com.timevale.saas.common.manage.core.model.request.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *
 * @date 2021/11/22
 */
@Data
@ApiModel("栏目内容点击统计")
public class ColumnClickRequest {

    @NotBlank(message = "产品编码不能为空")
    @ApiModelProperty("产品编码")
    private String productCode;

    @NotBlank(message = "位置编码不能为空")
    @ApiModelProperty("位置编码")
    private String areaCode;
}
