package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */

@Data
@ApiModel("下载失败任务列表响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class ExportFailedTaskResponse {
    @ApiModelProperty("失败任务列表下载地址")
    private String failTaskUrl;
}
