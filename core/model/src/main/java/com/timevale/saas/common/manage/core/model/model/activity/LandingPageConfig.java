package com.timevale.saas.common.manage.core.model.model.activity;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.activity.bean.ButtonInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("落地页配置")
public class LandingPageConfig extends ToString {

    @ApiModelProperty("落地页背景图fileKey")
    @NotBlank(message = "落地页背景图不能为空")
    private String pic;

    @ApiModelProperty("落地页地址")
    @NotBlank(message = "落地页地址不能为空")
    private String url;

    @ApiModelProperty("按钮信息")
    private ButtonInfo button;
}
