package com.timevale.saas.common.manage.core.model.response.activities;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.activity.LandingPageConfigDetail;
import com.timevale.saas.common.manage.core.model.model.activity.ResultPageConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-01-30
 */
@Data
public class QueryActivityInfoResponse extends ToString {

    @ApiModelProperty("活动标识")
    private String activityCode;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动开始时间")
    private Date startTime;

    @ApiModelProperty("活动结束时间")
    private Date endTime;

    @ApiModelProperty("活动最大参与数")
    private Integer maxActiveCount;

    @ApiModelProperty("活动参与数是否已满")
    private boolean fullActiveCount;

    @ApiModelProperty("活动参与主体类型, PERSON-个人，ORGANIZE-企业")
    private List<String> subjectTypes;

    @ApiModelProperty("活动落地页配置")
    private LandingPageConfigDetail landingPageConfig;

    @ApiModelProperty("活动结果页配置")
    private ResultPageConfig resultPageConfig;

    @ApiModelProperty("额外配置")
    private Map<String, Object> extraConfig;
}
