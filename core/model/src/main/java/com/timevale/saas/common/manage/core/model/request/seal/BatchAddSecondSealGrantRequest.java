package com.timevale.saas.common.manage.core.model.request.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/7/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("二级印章授权新增请求")
public class BatchAddSecondSealGrantRequest extends UrlBaseRequest {
    @ApiModelProperty(value = "一级授权业务id")
    @NotBlank(message = "一级授权业务id不能为空")
    private String sealGrantBizId;

    @ApiModelProperty(value = "被授权人列表")
    @NotNull(message = "被授权人列表不能为空")
    @Size(min = 1, max = 10, message = "被授权人列表个数[1-10]")
    private Set<String> grantedAccountIds;

    @ApiModelProperty(value = "授予被授权人的角色")
    @NotBlank(message = "授予被授权人的角色不能为空")
    @Pattern(regexp = "SEAL_USER|SEAL_EXAMINER", message = "授予被授权人的角色不支持")
    private String roleKey;

    @ApiModelProperty(value = "授权范围（模板列表）")
    @NotNull(message = "授权范围不能为空")
    @Size(min = 1, max = 10, message = "授权范围大小范围为[1-10]")
    private Set<String> scopeList;

    @ApiModelProperty(value = "自动落章,和fallType 二选一，优先取fallType类型")
    private Boolean autoFall;

    @ApiModelProperty(
            value = "落章方式，0-手动落章，1-免意愿且自动落章，2-免意愿且手动落章， 默认手动落章，和fallType 二选一，优先取fallType类型")
    private Integer fallType;

    @ApiModelProperty(value = "生效时间")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间")
    @NotNull(message = "")
    private Long expireTime;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

    @ApiModelProperty(name = "sealOwnerAdminOid", value = "印章授权归属企业的管理员OID")
    private String sealOwnerAdminOid;
}
