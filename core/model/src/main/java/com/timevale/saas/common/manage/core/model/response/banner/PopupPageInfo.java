package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.saas.common.manage.core.model.model.banner.PictureDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/2
 */
@ApiModel("弹窗分页查询返回对象结果")
@Data
public class PopupPageInfo {

    @ApiModelProperty("弹窗唯一标识")
    private Long id;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("位置名称")
    private String areaName;

    @ApiModelProperty("弹窗名称")
    private String name;

    @ApiModelProperty("展示图片地址")
    private String picUrl;

    @ApiModelProperty("跳转链接")
    private String linkUrl;

    @ApiModelProperty("权重")
    private Integer weight;

    @ApiModelProperty("有效用户类型 0全部 1指定 2功能灰度")
    private List<Integer> validityUserType;

    @ApiModelProperty("有效开始时间")
    private Date validityStartTime;

    @ApiModelProperty("有效结束时间")
    private Date validityEndTime;

    @ApiModelProperty("启用状态")
    private Integer status;

    @ApiModelProperty("点击次数")
    private Integer clickNum;

    @ApiModelProperty("关闭次数")
    private Integer closeNum;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("修改时间")
    private Date modifyTime;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("公告类型:1.产品迭代更新、2.新功能上线、3.交互体验升级、4.系统问题通知、5.其他")
    private Integer noticeType;

    @ApiModelProperty("支持的vip版本label")
    private List<String> notifyVersionLabels;

    @ApiModelProperty("支持的角色label")
    private List<String> notifyRoleLabels;

    @ApiModelProperty("轮播图片列表")
    private List<PictureDTO> scrollPictures;

    @ApiModelProperty("弹窗类型，1. 单一图片 2.轮播图片")
    private Integer popupType;
}
