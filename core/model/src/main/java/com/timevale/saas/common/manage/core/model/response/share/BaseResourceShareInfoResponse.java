package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.mandarin.common.result.ToString;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取资源分享信息基本信息父类
 *
 * <AUTHOR>
 * @since 2020/12/8
 */
@ApiModel(value = "资源分享基本信息")
@Getter
@Setter
public class BaseResourceShareInfoResponse extends ToString {

    @ApiModelProperty(value = "是否需要实名")
    private Boolean needRealName;

    @ApiModelProperty(value = "是否跳过实名，目前只有海外签跳过")
    private Boolean skipRealName;

    /** 请参考{@link com.timevale.saas.common.manage.common.service.enums.share.SharePlatformEnum} */
    @ApiModelProperty(value = "分享平台访问限制 ALL WECHAT ALIPAY")
    private String platform;

    @ApiModelProperty(value = "资源id")
    private String resourceId;

    @ApiModelProperty(value = "资源分享id")
    private String resourceShareId;

    /** 请参考{@link com.timevale.saas.common.manage.common.service.enums.share.ResourceTypeEnum } */
    @ApiModelProperty(value = "资源类型 FLOW_TEMPLATE PROCESS PROCESS_GROUP")
    private String resourceType;

    @ApiModelProperty(value = "分享到期时间")
    private Long shareEndTime;

    /**
     * 请参考{@link com.timevale.saas.common.manage.common.service.enums.share.ShareOperateTypeEnum }
     */
    @ApiModelProperty(value = "分享操作类型 NORMAL_SHARE COPY CHECK BORROW_READ")
    private String shareOperateType;

    /**
     * 请参考{@link com.timevale.saas.common.manage.common.service.enums.share.ResourceShareStatusEnum}
     */
    @ApiModelProperty(value = "分享状态 0-不可用 1-可用")
    private Integer status;

    @ApiModelProperty(value = "分享描述")
    private String shareDesc;

    @ApiModelProperty(value = "资源名称")
    private String resourceName;

    @ApiModelProperty(value = "资源分享操作方操作人姓名")
    private String resourceOperatorName;
}
