package com.timevale.saas.common.manage.core.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 公告类型
 *
 * <AUTHOR>
 * @since 2022-08-12 11:22
 */
@Getter
@AllArgsConstructor
public enum NoticeTypeEnum {
    ITERATIVE(1, "产品迭代更新"),
    NEW_FEATURES(2, "新功能上线"),
    UI_UPGRADE(3, "交互体验升级"),
    SYSTEM_PROBLEM(4, "系统问题通知"),
    OTHER(5, "其他"),
    ;

    private Integer code;

    private String desc;
}
