package com.timevale.saas.common.manage.core.model.model.seal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("印章被授权信息")
public class SealGrantedDTO {
    @ApiModelProperty(value = "授权业务id")
    private String sealGrantBizId;

    @ApiModelProperty(value = "印章id")
    private String sealId;

    @ApiModelProperty(value = "印章宽度")
    private Integer width;

    @ApiModelProperty(value = "印章高度")
    private Integer height;

    @ApiModelProperty(value = "印章别名")
    private String alias;

    @ApiModelProperty(value = "印章下载地址")
    private String url;

    @ApiModelProperty(value = "印章业务类型")
    private String sealBizType;

    @JsonIgnore
    @ApiModelProperty(value = "印章fileKey")
    private String fileKey;

    @ApiModelProperty(value = "生效时间")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间")
    private Long expireTime;

    @ApiModelProperty(value = "授权企业名称")
    private String organizationName;

    @ApiModelProperty(value = "印章业务类型描述")
    @NeedTranslateField
    private String sealBizTypeDesc;

    @ApiModelProperty(value = "二级印章授权数量")
    private Integer secondSealGrantNum;

    @ApiModelProperty(value = "被授权角色")
    private String grantedUserRole;

    @ApiModelProperty(value = "授权状态")
    private Integer status;

    @ApiModelProperty(value = "授权状态描述")
    @NeedTranslateField
    private String statusDesc;

    @ApiModelProperty(value = "过期原因")
    private String expireReason;

    @ApiModelProperty(value = "过期原因描述")
    private String expireReasonDesc;
}
