package com.timevale.saas.common.manage.core.model.request.share;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Max;
import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-07-05 15:37
 */
@Data
public class GetResourceShareRecordsRequest extends ToString {

	@Max(100)
	@ApiModelProperty(value = "分页参数", required = true)
	private Integer pageSize = 20;

	@Max(10000)
	@ApiModelProperty(value = "分页参数", required = true)
	private Integer pageNum = 1;

	@ApiModelProperty(value = "分享状态,1:生效中，0:失效")
	private Integer status;

	@ApiModelProperty(value = "操作开始时间")
	private Long operateStartTime;

	@ApiModelProperty(value = "操作结束时间")
	private Long operateEndTime;

	@ApiModelProperty(value = "资源名称")
	private String resourceName;

	@ApiModelProperty(value = "分享对象(姓名/手机号/邮箱)")
	private String shareTargetSearch;

	@ApiModelProperty(value = "操作人(姓名/手机号/邮箱)")
	private String operatorSearch;

	@ApiModelProperty(value = "资源id")
	private String resourceId;
}
