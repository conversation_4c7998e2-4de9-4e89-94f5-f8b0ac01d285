package com.timevale.saas.common.manage.core.model.response;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 生效订单信息
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Data
public class EffectiveOrderSimpleResponse extends ToString {


    @ApiModelProperty(value = "签署模式")
    private String signMode;
    @ApiModelProperty(value = "是否无限数量")
    private boolean unlimitNum;
    @ApiModelProperty(value = "购买量")
    private BigDecimal totalNum;
    @ApiModelProperty(value = "产品id")
    private Long productId;
    @ApiModelProperty(value = "剩余量")
    private BigDecimal margin;
    @ApiModelProperty(value = "单位")
    private String units;
    @ApiModelProperty(value = "产品名称")
    private String productName;
    @ApiModelProperty(value = "售卖方案编号")
    private Long saleSchemaId;
    @ApiModelProperty(value = "售卖方案名称")
    private String saleSchemaName;
}
