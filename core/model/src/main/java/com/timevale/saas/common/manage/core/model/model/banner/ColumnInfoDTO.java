package com.timevale.saas.common.manage.core.model.model.banner;

import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/19
 */
@Data
public class ColumnInfoDTO {

    @ApiModelProperty("栏目内容唯一标识")
    private Long id;

    @ApiModelProperty("跳转链接")
    private String LinkUrl;

    @ApiModelProperty("标题")
    @NeedTranslateField
    private String title;

    @ApiModelProperty("文本内容")
    @NeedTranslateField
    private String content;

    @ApiModelProperty("标签")
    @NeedTranslateField
    private String label;

    @ApiModelProperty("图片列表")
    private List<ColumnPicConfigBaseDTO> picFileList;

    @ApiModelProperty("创建时间")
    private Date createTime;
}
