package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2021/11/2
 **/
@ApiModel("添加产品入参")
@Data
public class ProductAddRequest extends ToString {

    @ApiModelProperty("产品code")
    @NotBlank(message = "请输入产品code")
    @Length(max = 32, message = "产品编码不能超过32个字符")
    private String productCode;

    @ApiModelProperty("产品名称")
    @NotBlank(message = "请输入产品名称")
    @Length(max = 32, message = "产品名称不能超过32个字符")
    private String productName;
}
