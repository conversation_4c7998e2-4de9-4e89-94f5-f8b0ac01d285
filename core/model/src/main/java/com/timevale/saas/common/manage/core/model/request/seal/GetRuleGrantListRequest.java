package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取授权企业/成员列表请求")
public class GetRuleGrantListRequest extends ToString {
    @ApiModelProperty(name = "orgId", value = "企业id", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

    @ApiModelProperty(name = "resourceId", value = "资源ID", required = true)
    public String resourceId;

    @ApiModelProperty(name = "ruleGrantStatus", value = "授权状态")
    public String ruleGrantStatus;

    @ApiModelProperty(name = "offset", value = "数据下标")
    public Integer offset;

    @ApiModelProperty(name = "size", value = "返回数据条数")
    public Integer size;

    @ApiModelProperty(name = "type", value = "授权类型 1-企业内部 2-企业外部")
    public Integer type;
}
