package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: huifeng
 * @since: 2021-03-25 17:15
 **/
@Data
public class BatchDelayVipExpireRequest extends ToString {

    /**
     * 所有gid都延期到expireDateStr，版本：targetLevel
     */
    @NotEmpty
    private List<String> delayTenantGids;

    /**
     * 延期目标会员版本
     */
    @NotNull
    private Integer targetVipLevel;

    /**
     * 形式 yyyy/MM/dd hh:mm:ss
     */
    @NotBlank
    private String expireDateStr;
}
