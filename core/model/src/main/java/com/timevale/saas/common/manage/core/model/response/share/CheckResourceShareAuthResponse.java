package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.mandarin.common.result.ToString;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 校验资源分享访问权限返回结果
 *
 * <AUTHOR>
 * @since 2020/12/14
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckResourceShareAuthResponse extends ToString {

    /** 是否有权限 */
    private boolean hasAuth;

    /** 是否需要实名 */
    private boolean needRealName;

    /** 链接是否失效 **/
    private Boolean isExpired;
    /** 操作类型 */
    private String shareOperateType;
}
