package com.timevale.saas.common.manage.core.model.response.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取印章审核拒绝原因响应")
public class GetAuditRefuseInfoResponse {
	@ApiModelProperty(value = "审核失败原因")
	private String reason;
}
