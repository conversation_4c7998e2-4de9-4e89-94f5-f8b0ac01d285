package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2021-07-16
 */
@Data
@ApiModel("修改接口访问限制状态请求参数")
public class RequestAccessLimitUpdateStatusRequest extends ToString {

    @NotNull(message = "限制状态不能为空")
    @ApiModelProperty("限制状态，0-待生效， 1-生效中，2-已废弃")
    @Min(value =0 , message = "限制状态无效， 仅支持: 0-待生效， 1-生效中，2-已废弃")
    @Max(value = 2, message = "限制状态无效， 仅支持: 0-待生效， 1-生效中，2-已废弃")
    private Integer status;
}
