package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-16
 */
@Data
@ApiModel("批量修改接口访问限制状态请求参数")
public class BatchRequestAccessLimitUpdateStatusRequest extends RequestAccessLimitUpdateStatusRequest {

    @ApiModelProperty("访问限制id列表")
    @NotEmpty(message = "访问限制id列表不能为空")
    @Size(max = 100, message = "访问限制id列表单次不能超过100个")
    private List<Long> ids;
}
