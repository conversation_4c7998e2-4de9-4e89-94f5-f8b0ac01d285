package com.timevale.saas.common.manage.core.model.response.crm;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.roleandprivilege.SaasPrivilegeDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("运营支撑获取saas所有权限响应")
public class GetCRMSaasPrivilegesResponse extends ToString {
    @ApiModelProperty("saas权限列表")
    List<SaasPrivilegeDTO> saasPrivilegeList;
}
