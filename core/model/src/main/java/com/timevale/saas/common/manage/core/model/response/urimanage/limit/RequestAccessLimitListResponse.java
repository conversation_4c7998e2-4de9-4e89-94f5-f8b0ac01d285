package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
@ApiModel("接口访问限制列表返回数据")
public class RequestAccessLimitListResponse extends ToString {

    @ApiModelProperty("数据总数")
    private Long total;

    @ApiModelProperty("接口访问限制列表")
    private List<RequestAccessLimitListBean> uris;
}
