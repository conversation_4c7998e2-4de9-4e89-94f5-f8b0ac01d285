package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020-07-15
 */
@Data
@ApiModel("接口访问限制列表查询请求参数")
public class RequestAccessLimitListRequest extends ToString {

    @Length(max = 100, message = "接口地址最长不可超过100字符")
    @ApiModelProperty("接口地址，最长不可超过100字符")
    private String uri;

    @ApiModelProperty("业务域")
    private String bizDomain;

    @ApiModelProperty("限制状态，0-待生效， 1-生效中，2-已废弃")
    private Integer status;

    @NotNull(message = "页码不能为空")
    @ApiModelProperty("页码, 默认1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    @NotNull(message = "页行数不能为空")
    @ApiModelProperty("页行数, 默认20")
    @Max(value = 100, message = "每页最多获取100条数据")
    private Integer pageSize;
}
