package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.SealGrantedDTO;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("被授权信息列表响应")
public class GetRuleGrantedListResponse extends ToString {
    @ApiModelProperty(value = "查询总数")
    private Integer total;

    @ApiModelProperty(value = "被授权列表")
    @HasTranslateField
    private List<SealGrantedDTO> grantedList;
}
