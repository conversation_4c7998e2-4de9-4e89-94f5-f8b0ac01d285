package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @Author:jianyang
 * @since 2021-07-05 15:51
 */
@Data
@Builder
public class GetResourceShareRecordsResponse extends ToString {

	@ApiModelProperty(value = "资源名称")
	private String resourceName;

	@ApiModelProperty(value = "操作时间")
	private Date operateTime;

	@ApiModelProperty(value = "操作人")
	private String operator;

	@ApiModelProperty(value = "分享人数")
	private Integer shareNum= 0;

	@ApiModelProperty(value = "分享截止时间")
	private Date shareEndTime;

	@ApiModelProperty(value = "分享状态")
	private Integer status;

	@ApiModelProperty(value = "分享链接")
	private String shareUrl;

	@ApiModelProperty(value = "资源分享id")
	private String resourceShareId;

	@ApiModelProperty(value = "资源Id")
	private String resourceId;
}
