package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.OfficialSealTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("查询企业模板章详情")
public class GetOfficialTemplateDetailResponse extends ToString {
    @ApiModelProperty(value = "印章别名")
    private String alias;

    @ApiModelProperty(value = "印章下载地址")
    private String url;

    @ApiModelProperty(value = "印章状态 -1-待领取，1-已启用，2-待审核，3-审核不通过 4-已挂起 5-待提交")
    private Integer status;

    @ApiModelProperty(
            value =
                    "业务类型 <br>印章业务类型，COMMON-无业务类型(其他)，PUBLIC-公章，CONTRACT-合同专用章, FINANCE-财务章, PERSONNEL-人事专用章")
    private String bizType;

    @ApiModelProperty(value = "印章模版信息")
    private OfficialSealTemplateDTO sealTemplate;
}
