package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("二次授权更新响应")
public class SecondSealGrantUpdateResponse extends ToString {
    @ApiModelProperty(value = "二级授权业务id")
    private String secSealGrantBizId;

    @ApiModelProperty(value = "签署短链")
    private String shortSignUrl;

    @ApiModelProperty(value = "签署长链")
    private String longSignUrl;
}
