package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量获取用户会员及会员功能列表入参
 *
 * <AUTHOR>
 * @since 2021-01-26 10:57
 */
@Data
public class QueryUsersVipRequest extends ToString {

    /** 用户id列表 */
    @NotNull(message = "用户列表不能为空")
    @NotEmpty(message = "用户列表不能为空")
    @Size(max = 1000)
    private List<String> userIdList;

    /** 是否需要功能标识 默认false */
    private boolean withFuncs;
}
