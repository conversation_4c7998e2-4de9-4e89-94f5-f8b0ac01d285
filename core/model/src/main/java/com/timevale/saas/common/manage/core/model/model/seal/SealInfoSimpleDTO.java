package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 印章的简要信息
 *
 * <AUTHOR>
 * @since 2022/12/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("印章信息")
public class SealInfoSimpleDTO {

    @ApiModelProperty(value = "印章id")
    private String sealId;

    @ApiModelProperty(value = "印章名称")
    private String sealName;

    @ApiModelProperty(value = "印章类型")
    private String sealBizType;

    @ApiModelProperty(value = "默认印章标识")
    private boolean defaultSealFlag;

    @ApiModelProperty(value = "印章下载地址")
    private String sealImageUrl;
}
