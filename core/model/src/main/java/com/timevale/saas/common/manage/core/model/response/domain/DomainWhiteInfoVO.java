package com.timevale.saas.common.manage.core.model.response.domain;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/3/8
 */
@Setter
@Getter
public class DomainWhiteInfoVO extends ToString {

    @ApiModelProperty("域名id")
    private Long domainId;

    @ApiModelProperty("url")
    private String url;

    @ApiModelProperty("域名备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("修改时间")
    private Long updateTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String modifier;
}
