package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023-07-12 09:40
 */
@Data
public class VipLevelFunctionInfo extends ToString {

    @ApiModelProperty("功能标识")
    private String code;

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("功能描述")
    private String desc;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date modifyTime;
}
