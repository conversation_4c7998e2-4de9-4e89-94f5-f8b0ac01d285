package com.timevale.saas.common.manage.core.model.model;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020/12/10
 */
@Getter
@Setter
public class UserAccount {
    private String oid;
    private String gid;
    private String uid;
    private String appId;
    private String account;
    private String name;
    /** 是否已实名 */
    private boolean realName;

    private boolean person;

    /**
     * 统一社会信用吗
     */
    private String usccCode;

    private String legalName;

    /**
     * 校验是否企业账号
     */
    public boolean checkOrgan() {
        return !isPerson();
    }

    /**
     * 判断是否同一用户
     * @param userAccount
     * @return
     */
    public boolean checkSameAccount(UserAccount userAccount) {
        if (null == userAccount) {
            return false;
        }
        if (StringUtils.isNoneBlank(oid, userAccount.getOid())
                && StringUtils.equals(oid, userAccount.getOid())) {
            return true;
        }
        return StringUtils.isNoneBlank(gid, userAccount.getGid())
                && StringUtils.equals(gid, userAccount.getGid());
    }
}
