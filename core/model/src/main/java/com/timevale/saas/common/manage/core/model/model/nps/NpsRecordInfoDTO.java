package com.timevale.saas.common.manage.core.model.model.nps;

import com.timevale.mandarin.common.result.ToString;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/1/7
 */
@Setter
@Getter
public class NpsRecordInfoDTO extends ToString {

    /** 问卷id */
    private Long npsId;

    /** 用户gid */
    private String accountGid;

    /** 渠道产品码 */
    private String productCode;

    /** 设备号可空 */
    private String machineId;

    /** 位置code */
    private String areaCode;

    /** 展示窗口结束时间：秒 */
    private Long endTime;
}
