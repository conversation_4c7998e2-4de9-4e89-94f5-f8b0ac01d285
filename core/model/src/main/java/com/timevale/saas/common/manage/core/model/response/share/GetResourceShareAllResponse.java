package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.saas.common.manage.core.model.request.share.bean.ShareTargetBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 创建资源分享响应结果
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@ApiModel(value = "获取资源分享基本信息")
@Getter
@Setter
public class GetResourceShareAllResponse extends BaseResourceShareInfoResponse {

    @ApiModelProperty(value = "资源分享操作方操作人oid")
    private String resourceOperatorOid;

    @ApiModelProperty(value = "资源分享操作方操作人gid")
    private String resourceOperatorGid;

    @ApiModelProperty(value = "资源分享操作方操作人账号")
    private String resourceOperatorAccount;

    @ApiModelProperty(value = "资源分享操作方操作人姓名")
    private String resourceOperatorName;

    @ApiModelProperty(value = "资源分享操作方主体oid")
    private String resourceSubjectGid;

    @ApiModelProperty(value = "资源分享操作方主体gid")
    private String resourceSubjectOid;

    @ApiModelProperty(value = "分享对象")
    private List<ShareTargetBean> shareTargets;
}
