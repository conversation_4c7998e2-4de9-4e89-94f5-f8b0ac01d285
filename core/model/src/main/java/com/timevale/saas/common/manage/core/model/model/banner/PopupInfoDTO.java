package com.timevale.saas.common.manage.core.model.model.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/2
 */
@Data
public class PopupInfoDTO extends ToString {

    @ApiModelProperty("唯一标识")
    private Long id;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("位置编码")
    private String areaCode;

    @ApiModelProperty("图片地址")
    private String pictureUrl;

    @ApiModelProperty("跳转链接")
    private String linkUrl;

    @ApiModelProperty("有效用户类型 0全部 1指定 2指定功能灰度")
    private String validityUserType;

    @ApiModelProperty("有效开始时间")
    private Date validityStartTime;

    @ApiModelProperty("有效结束时间")
    private Date validityEndTime;

    @ApiModelProperty("弹出配置类型 0首次")
    private Integer configType;

    @ApiModelProperty("扩展配置")
    private String configInfo;

    @ApiModelProperty("权重")
    private Integer weight;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("弹窗类型，1. 单一图片 2.轮播图片")
    private Integer popupType;

    @ApiModelProperty("轮播图片列表")
    private List<PictureDTO> scrollPictures;

    @ApiModelProperty("支持的vip版本，为空表示所有版本都展示")
    private List<String> notifyVersions;

    @ApiModelProperty("支持的角色，为空表示所有角色都展示")
    private List<String> notifyRoles;
}
