package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * VipInfo
 *
 * <AUTHOR>
 * @since 2021/5/27 4:21 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class VipInfo extends ToString {

    /**
     * 会员套餐名称
     */
    @ApiModelProperty("会员套餐名称")
    private String vipName;

    /**
     * 有效期起
     */
    @ApiModelProperty("有效期起")
    private Date effectiveFrom;

    /**
     * 有效期止
     */
    @ApiModelProperty("有效期止")
    private Date effectiveTo;

    @ApiModelProperty("会员等级")
    private Integer vipLevel;

    @ApiModelProperty("会员等级标识")
    private String vipCode;

    @ApiModelProperty("是否使用会员等级标识")
    private boolean useVipCode;
}
