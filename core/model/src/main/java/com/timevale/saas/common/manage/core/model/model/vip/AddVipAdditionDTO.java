package com.timevale.saas.common.manage.core.model.model.vip;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2024-10-15
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddVipAdditionDTO extends ToString {

    /** 当前操作人花名 */
    private String alias;

    /** 功能标识 */
    private String funcCode;

    /** 配置信息 */
    private Map<String,Object> funcLimit;


    /** 账号gid */
    private String accountGid;

    /** 生效时间 */
    private Long effectiveFrom;

    /** 失效时间 */
    private Long effectiveTo;
}
