package com.timevale.saas.common.manage.core.model.model.vip;

import com.timevale.saas.common.manage.core.model.enums.VipCommodityCategoryEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-08-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VipCommoditySupportDTO {
    private String vipCode;
    private List<VipCommodityCategoryEnum> person;
    private List<VipCommodityCategoryEnum> org;

}
