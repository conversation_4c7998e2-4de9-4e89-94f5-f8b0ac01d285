package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *
 * @date 2021/11/3
 */
@Data
@ApiModel("弹窗回调入参")
public class PopupCallbackRequest extends ToString {

    @ApiModelProperty("用户ID")
    @NotBlank(message = "用户唯一标识不能为空")
    private String userId;

    @ApiModelProperty("弹窗id")
    @NotNull(message = "弹窗唯一标识不可为空")
    private Long popupId;

    @ApiModelProperty("位置code")
    @NotBlank(message = "位置编码不可为空")
    private String areaCode;

    @ApiModelProperty("产品code")
    @NotBlank(message = "产品编码不可为空")
    private String productCode;
}
