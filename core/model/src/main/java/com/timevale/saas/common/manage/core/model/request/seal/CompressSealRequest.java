package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("压缩，透明化处理印章图片请求")
public class CompressSealRequest extends ToString {
    @ApiModelProperty(value = "印章数据", required = true)
    @NotBlank(message = "印章数据不能为空")
    private String data;

    @ApiModelProperty(value = "印章数据类型，BASE64-base64格式，FILEKEY-fileKey格式")
    private String dataType;

    @ApiModelProperty(value = "处理后的颜色", required = true)
    private String color;

    @ApiModelProperty(value = "处理阈值", required = true)
    private List<Integer> cvalues;

    @ApiModelProperty(value = "是否返回下载地址，默认false")
    private boolean downloadFlag;

    /** 可以为空,为空则不做透明度处理 */
    @Min(value = 1, message = "不透明度必须在1~100以内")
    @Max(value = 100, message = "不透明度必须在1~100以内")
    @ApiModelProperty(value = "不透明度 1~100。 100表示不透明")
    private Short opacity;
}
