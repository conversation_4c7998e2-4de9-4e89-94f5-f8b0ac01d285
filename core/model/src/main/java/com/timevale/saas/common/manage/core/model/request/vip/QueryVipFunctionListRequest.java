package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2023-07-12 10:11
 */
@Data
@ApiModel("查询会员功能列表请求参数")
public class QueryVipFunctionListRequest extends ToString {

    @ApiModelProperty(value = "查询类型")
    private String selectType;

    @ApiModelProperty(value = "查询内容")
    private String selectValue;

    @ApiModelProperty(value = "页码", required = true)
    @NotNull(message = "pageNo不能为空")
    @Min(value = 1, message = "pageNo最小值为1")
    private Integer pageNo;

    @ApiModelProperty(value = "每页显示的数量", required = true)
    @NotNull(message = "pageSize不能为空")
    @Min(value = 1, message = "pageSize最小值为1")
    @Max(value = 100, message = "pageSize最大值为100")
    private Integer pageSize;
}
