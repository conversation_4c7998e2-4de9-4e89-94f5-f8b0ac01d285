package com.timevale.saas.common.manage.core.model.bo.bill;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/7 15:18
 * 从配置中心读取数据
 */
@Data
public class ProcessBillConfig {

    /**
     * saas扣费产品
     */
    private Long saasProductId;
    private String saasProductNO;

    /**
     * 全球签扣费产品
     */
    private Long saasGlobalProductId;
    private String saasGlobalProductNO;


    public List<Long> allProductId() {
       return Lists.newArrayList(saasProductId, saasGlobalProductId);
    }
}
