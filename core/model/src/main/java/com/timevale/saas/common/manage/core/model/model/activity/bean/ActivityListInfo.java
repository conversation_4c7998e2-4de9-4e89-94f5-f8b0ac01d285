package com.timevale.saas.common.manage.core.model.model.activity.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ActivityListInfo extends ToString {

    @ApiModelProperty("活动标识")
    private String activityCode;

    @ApiModelProperty("活动名称")
    private String activityName;

    @ApiModelProperty("活动起始时间")
    private Long startTime;

    @ApiModelProperty("活动截止时间")
    private Long endTime;

    @ApiModelProperty("活动已参与用户数")
    private Integer amount;

    @ApiModelProperty("活动标识")
    private String creator;

    /** @see com.timevale.saas.common.manage.core.model.enums.ActivityStatusEnum */
    @ApiModelProperty("活动状态，1-进行中，2-已过期，3-待开始")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("更新时间")
    private Long updateTime;
}
