package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@ApiModel("导出访问限制配置请求参数")
public class RequestAccessLimitExportRequest extends ToString {

    @ApiModelProperty("访问限制id列表")
    @NotEmpty(message = "访问限制id列表不能为空")
    @Size(max = 100, message = "访问限制id列表单次不能超过100个")
    private List<Long> ids;
}
