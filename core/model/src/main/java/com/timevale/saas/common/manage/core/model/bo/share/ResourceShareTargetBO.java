package com.timevale.saas.common.manage.core.model.bo.share;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 资源分享对象模型对象
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@Getter
@Setter
public class ResourceShareTargetBO {

    private Long id;
    /** 资源id */
    private String resourceId;
    /** 资源分享id */
    private String resourceShareId;
    /** 资源分享对象方主体oid */
    private String targetSubjectOid;
    /** 资源分享对象方主体gid */
    private String targetSubjectGid;
    /** 资源分享对象方操作人oid */
    private String targetOperatorOid;
    /** 资源分享对象方操作人gid */
    private String targetOperatorGid;
    /** 资源分享对象方操作人账号 */
    private String targetOperatorAccount;
    /** 资源分享对象方操作人姓名 */
    private String targetOperatorName;
    /** 资源分享对象方操作人操作状态 */
    private int operateStatus;
    /** 创建时间 */
    private Date createTime;
    /** 更新时间 */
    private Date updateTime;
    /** 操作时间 */
    private Date operateTime;

    /*对应分享对象的权限*/
    List<ResourceShareTargetAuthBO> shareTargetAuths;
}
