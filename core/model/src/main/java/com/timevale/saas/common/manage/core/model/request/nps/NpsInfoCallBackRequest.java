package com.timevale.saas.common.manage.core.model.request.nps;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022/1/12
 */
@Getter
@Setter
public class NpsInfoCallBackRequest extends ToString {

    @ApiModelProperty("内部问卷id")
    @NotNull(message = "内部问卷id唯一标示不能为空")
    private Long npsId;

    @ApiModelProperty("设备id")
    private String machineId;
}
