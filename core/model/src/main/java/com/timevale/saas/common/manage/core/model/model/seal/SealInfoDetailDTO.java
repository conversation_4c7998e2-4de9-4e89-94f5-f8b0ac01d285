package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("印章详细信息")
public class SealInfoDetailDTO extends SealInfoDTO {
    @ApiModelProperty(value = "印章水印图片下载地址")
    private String waterMarkUrl;

    @ApiModelProperty(value = "印章水印fileKey")
    private String watermarkFileKey;
}
