package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-31 17:48
 **/
@ApiModel("产品端某个位置的banner列表响应信息")
public class ProductAreaBannerListRes extends ToString {

    @ApiModelProperty("banner展示形式，1轮播，2独占")
    private Integer displayType;


    @ApiModelProperty("banner列表")
    private List<BannerPicInfo> bannerList;

    public Integer getDisplayType() {
        return displayType;
    }

    public void setDisplayType(Integer displayType) {
        this.displayType = displayType;
    }

    public List<BannerPicInfo> getBannerList() {
        return bannerList;
    }

    public void setBannerList(List<BannerPicInfo> bannerList) {
        this.bannerList = bannerList;
    }
}
