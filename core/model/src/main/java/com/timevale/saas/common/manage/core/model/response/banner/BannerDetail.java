package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.enums.NoticeTypeEnum;
import com.timevale.saas.common.manage.core.model.model.banner.PushUserConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-31 16:50
 **/
@Data
@ApiModel("Banner详情")
public class BannerDetail extends ToString {

    @ApiModelProperty("banner唯一标识")
    private Long bannerId;

    @ApiModelProperty("banner名称")
    private String bannerName;

    @ApiModelProperty("产品端编码")
    private String productCode;

    @ApiModelProperty("位置编码")
    private String areaCode;

    @ApiModelProperty("位置编码")
    private String fileKey;

    @ApiModelProperty("图片Url")
    private String picUrl;

    @ApiModelProperty("图片链接")
    private String linkUrl;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("有效用户类型 0全部 1指定 2功能灰度")
    private List<Integer> validityUserType;

    @ApiModelProperty("有效用户配置")
    private List<PushUserConfigDTO> validityUserConfig;

    @ApiModelProperty("指定推送用户count")
    private Long pushUserCount;

    @ApiModelProperty("权重")
    private Integer orderNo;

    /**
     * @see NoticeTypeEnum 公告类型枚举
     */
    @ApiModelProperty("公告类型:1.产品迭代更新、2.新功能上线、3.交互体验升级、4.系统问题通知、5.其他")
    private Integer noticeType;

    @ApiModelProperty("支持的vip版本，为空表示所有版本都展示")
    private List<String> notifyVersions;

    @ApiModelProperty("支持的角色，为空表示所有角色都展示")
    private List<String> notifyRoles;

    @ApiModelProperty("引导文案")
    private String guideText;

    @ApiModelProperty("有效开始时间")
    private Date validityStartTime;

    @ApiModelProperty("有效结束时间")
    private Date validityEndTime;

    @ApiModelProperty("客户端版本号")
    private Integer clientVersionCode;

    /** {@link ConditionTypeEnum} */
    @ApiModelProperty("版本号显示条件: lt:小于，le:小于等于，eq:等于，ge：大于等于，gt:大于")
    private String conditionType;
}
