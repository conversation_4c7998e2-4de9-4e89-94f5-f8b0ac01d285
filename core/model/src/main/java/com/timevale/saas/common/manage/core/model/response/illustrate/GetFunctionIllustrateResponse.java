package com.timevale.saas.common.manage.core.model.response.illustrate;

import com.timevale.saas.common.manage.core.model.enums.IllustrateShowModeEnum;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2022/11/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("查询功能介绍信息响应")
public class GetFunctionIllustrateResponse {

    @ApiModelProperty("功能介绍展示模式，1 图片 2 视频")
    /** @see IllustrateShowModeEnum#getCode() */
    private Integer showMode;

    @ApiModelProperty("功能介绍图片或者视频的文件下载地址")
    private String showFileUrl;

    @ApiModelProperty("功能介绍标题")
    @NeedTranslateField
    private String docTitle;

    @ApiModelProperty("功能介绍文档")
    @NeedTranslateField
    private String docContent;

    @ApiModelProperty("满足该功能的最低版本")
    private Integer satisfyMixVersion;

    @ApiModelProperty("满足该功能的最低版本标识")
    private String satisfyMinVipCode;

    @ApiModelProperty("是否使用版本标识")
    private boolean useVipCode;

    @ApiModelProperty("对外功能介绍链接")
    private String outWikiUrl;
}
