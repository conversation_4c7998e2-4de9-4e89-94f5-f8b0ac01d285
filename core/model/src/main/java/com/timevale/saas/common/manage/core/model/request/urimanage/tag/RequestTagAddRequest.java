package com.timevale.saas.common.manage.core.model.request.urimanage.tag;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
@ApiModel("新增接口标签请求参数")
public class RequestTagAddRequest extends ToString {

    @NotBlank(message = "接口标签不能为空")
    @Length(max = 40, message = "接口标签最多不可超出40个字符")
    @ApiModelProperty("接口标签，以小写字母和下划线组成， 最长不超过40个字符")
    private String tag;

    @NotNull(message = "标签类型不能为空，1-接口标签，2-业务域标签")
    @ApiModelProperty("标签类型，1-接口标签，2-业务域标签")
    private Integer type;

    @NotBlank(message = "接口标签名称不能为空")
    @Length(max = 40, message = "接口标签名称最多不可超出200个字符")
    @ApiModelProperty("接口标签名称， 最长不超过40个字符")
    private String name;

    @Length(max = 100, message = "接口标签说明最多不可超出100个字符")
    @ApiModelProperty("接口标签说明， 最长不超过100个字符")
    private String desc;
}
