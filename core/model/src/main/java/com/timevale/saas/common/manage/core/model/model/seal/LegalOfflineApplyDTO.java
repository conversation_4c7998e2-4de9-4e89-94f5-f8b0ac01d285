package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("线下授权信息")
public class LegalOfflineApplyDTO {
	@ApiModelProperty(value = "授权文档信心")
	private FileDocDTO authDoc;

	@ApiModelProperty(value = "身份证正面照")
	private FileDocDTO authIDFront;

	@ApiModelProperty(value = "身份证发面照")
	private FileDocDTO authIDReverse;

	@ApiModelProperty(value = "审核原因")
	private String reason;
}
