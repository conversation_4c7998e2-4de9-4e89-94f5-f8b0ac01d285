package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("企业图片印章创建响应")
public class CreateOrgImageSealResponse extends ToString {
    @ApiModelProperty("印章id")
    private String sealId;

    @ApiModelProperty("印章fileKey")
    private String fileKey;

    @ApiModelProperty("印章下载")
    private String url;

    @ApiModelProperty("印章宽度")
    private Integer width;

    @ApiModelProperty("印章高度")
    private Integer height;
}
