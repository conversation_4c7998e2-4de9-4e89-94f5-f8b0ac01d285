package com.timevale.saas.common.manage.core.model.model.illustrate;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.enums.IllustrateShowModeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-22 17:31
 */
@Data
public class BaseFunctionIllustrateDTO extends ToString {
    /** 功能介绍展示模式，1 图片 2 视频
     * @see IllustrateShowModeEnum#getCode() */
    private Integer showMode;

    /**功能介绍图片或者视频的文件下载地址*/
    private String showFileUrl;

    /**功能介绍标题*/
    private String docTitle;

    /**功能介绍文档*/
    private String docContent;

    /**对外功能介绍链接*/
    private String outWikiUrl;

    /** 是否支持试用 */
    private boolean supportTrial = Boolean.FALSE;
}
