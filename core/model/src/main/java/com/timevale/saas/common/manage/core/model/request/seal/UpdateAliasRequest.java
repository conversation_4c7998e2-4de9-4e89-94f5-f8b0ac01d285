package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("编辑印章名称请求")
public class UpdateAliasRequest extends ToString {
    @NotBlank(message = "印章id不能为空")
    private String sealId;

    @NotBlank(message = "印章别名不能为空")
    @Length(max = 50, message = "印章别名最长50个字符")
    private String alias;

    @ApiModelProperty(value = "印章归属OID")
    private String sealOwnerOid;
}
