package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020-08-18
 */

@Data
@ApiModel("获取并处理任务结果响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class HandlerTaskResultResponse {
    @ApiModelProperty("任务类型")
    private Integer taskType;

    @ApiModelProperty("任务状态")
    private Integer taskStatus;

    @ApiModelProperty("是否批量任务")
    private boolean isGroup;

    @ApiModelProperty("结果")
    private Object result;
}
