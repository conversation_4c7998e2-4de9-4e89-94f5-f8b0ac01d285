package com.timevale.saas.common.manage.core.model.bo.bill;

import com.timevale.billing.manager.sdk.enums.IsolationTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BillIsolateBO {
    /** 隔离场景列表 */
    private List<Integer> useScopeList;
    /** 场景类型 {@link IsolationTypeEnum} */
    private Integer sceneType;
    /** 场景绑定值 */
    private String sceneValue;
    /** 是否强隔离 */
    private boolean isIsolate;
}
