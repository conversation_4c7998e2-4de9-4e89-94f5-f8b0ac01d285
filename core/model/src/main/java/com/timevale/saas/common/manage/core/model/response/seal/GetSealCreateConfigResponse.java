package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取印章创建配置响应")
public class GetSealCreateConfigResponse extends ToString {
    @ApiModelProperty(value = "是否展示企业模板章选项")
    private boolean showOrgTemplateSeal;

    @ApiModelProperty(value = "印章业务类型及其约束")
    @HasTranslateField
    List<SealBizTypeRuleDTO> bizTypeRuleList;

    @ApiModelProperty(value = "印章状态列表")
    @HasTranslateField
    List<SealStatusDTO> sealStatusList;

    @ApiModelProperty(value = "被授权印章状态列表")
    @HasTranslateField
    List<SealStatusDTO> grantedSealStatusList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("印章业务类型及其约束")
    public static class SealBizTypeRuleDTO {
        private String type;

        @NeedTranslateField
        private String name;

        private String hTextDefault;

        private Boolean hTextEdit;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("印章状态类型")
    public static class SealStatusDTO {
        private Short value;

        @NeedTranslateField
        private String name;
    }
}
