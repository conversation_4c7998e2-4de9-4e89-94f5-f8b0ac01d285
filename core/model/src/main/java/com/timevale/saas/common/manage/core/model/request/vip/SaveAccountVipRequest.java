package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020-08-20
 */
@Data
public class SaveAccountVipRequest extends ToString {

    /** 订单id */
    private String orderId;

    /**
     * 计费商品id
     */
    private Long commodityId;

    /**
     * 计费商品套餐id
     */
    private Long saleInfoId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 订单类型 1 普通 2 赠送
     */
    private Integer orderType;

    /**
     * @see com.timevale.saas.common.manage.common.service.enums.VipTypeEnum
     */
    private Integer vipType;

    /**
     * 用户gid
     */
    private String gid;

    /**
     * 套餐有效期起始时间
     */
    private Date effectiveFrom;

    /**
     * 套餐有效期截止时间
     */
    private Date effectiveTo;

    /** 时间单位 */
    private String effectiveUnit; //

    /** 时长 */
    private Integer effectiveDate; //

    /** 是否无限时长 */
    private boolean isUnLimitTime;

    /** 是否赠送 */
    private boolean present;
}
