package com.timevale.saas.common.manage.core.model.response.urimanage.tag;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
@ApiModel("接口标签列表返回数据")
public class RequestTagListResponse extends ToString {

    @ApiModelProperty("接口标签列表")
    private List<RequestTagBean> tags;
}
