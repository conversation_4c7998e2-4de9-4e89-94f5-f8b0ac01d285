package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2022/3/17
 */
@Data
public class PushUserPageResponse extends ToString {

    @ApiModelProperty("数据量")
    private Long total;

    @ApiModelProperty("数据列表")
    private List<PushUserInfo> data;
}
