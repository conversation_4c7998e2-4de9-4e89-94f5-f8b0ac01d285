package com.timevale.saas.common.manage.core.model.request.share;

import com.timevale.saas.common.manage.common.service.enums.share.SharePlatformEnum;
import com.timevale.saas.common.manage.core.model.request.share.bean.ShareTargetBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 资源分享插件化扩展点定义
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@ApiModel(value = "资源分享创建分享")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateResourceShareRequest extends BaseResourceShareRequest {

    @ApiModelProperty(value = "是否需要分享码,默认false不需要")
    private boolean needShareCode = false;

    @ApiModelProperty(value = "是否需要分享二维码,默认false不需要")
    private boolean needShareQrCode = false;

    @ApiModelProperty(
            value = "调用来源,CONTRACT:经办合同,ORG_ARCHIVE:企业合同已归档,ORG_ARCHIVE_MENU_ID:企业合同已归档分类,ORG_WAITING_ARCHIVE:企业合同待归档",
            required = true)
    private String callSorce;

    @ApiModelProperty(value = "分类id")
    private String menuId;

    @ApiModelProperty(value = "分享类型1:分享给合同参与人,2:分享给非合同参与人")
    private Integer shareType;

    @ApiModelProperty(value = "分享对象列表")
    @Valid
    private List<ShareTargetBean> shareTargets;

    @ApiModelProperty(value = "是否使用简单版二维码,默认:false")
    private Boolean useSimpleQrCode = false;
}
