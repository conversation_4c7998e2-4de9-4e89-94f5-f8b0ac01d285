package com.timevale.saas.common.manage.core.model.request.authrelation;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.Max;
import java.util.Date;

/**
 * @Author:jianyang
 * @since 2022-05-05 11:42
 */
@Data
public class AuthRelationOperatorListRequest extends ToString {

	@ApiModelProperty(value = "页大小", required = true)
	@Max(100)
	private Integer pageSize = 100;

	@ApiModelProperty(value = "页码", required = true)
	private Integer pageNo = 1;

	@ApiModelProperty(value = "主企业名称")
	private String parentName;

	@ApiModelProperty(value = "添加时间起始时间")
	private Long operatorStartTime;

	@ApiModelProperty(value = "添加时间结束时间")
	private Long operatorEndTime;

	@ApiModelProperty(value = "gid")
	private String parentGid;
}
