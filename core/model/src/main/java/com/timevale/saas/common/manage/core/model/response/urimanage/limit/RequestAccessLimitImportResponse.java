package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.request.urimanage.limit.RequestAccessLimitAddRequest;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@ApiModel("导入访问限制配置响应数据")
public class RequestAccessLimitImportResponse extends ToString {

    /** 导入访问限制列表 */
    private List<RequestAccessLimitAddRequest> urlLimits;
}
