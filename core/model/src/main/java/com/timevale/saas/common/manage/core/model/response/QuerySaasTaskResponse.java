package com.timevale.saas.common.manage.core.model.response;

import com.timevale.saas.common.manage.core.model.response.bean.TaskBean;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */

@Data
@ApiModel("获取任务列表响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class QuerySaasTaskResponse {
    @ApiModelProperty("总数")
    private long total;
    @ApiModelProperty("任务列表")
    @HasTranslateField
    private List<TaskBean> tasks;
}
