package com.timevale.saas.common.manage.core.model.response.banner;

import io.swagger.annotations.ApiModel;

/**
 * <AUTHOR>
 * @since 2021-05-31 17:13
 **/
@ApiModel("banner新增/修改响应信息")
public class BannerSaveRes extends DialogInfo {

    private Long bannerId;

    public Long getBannerId() {
        return bannerId;
    }

    public void setBannerId(Long bannerId) {
        this.bannerId = bannerId;
    }
}
