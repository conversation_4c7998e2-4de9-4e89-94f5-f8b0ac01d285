package com.timevale.saas.common.manage.core.model.response.urimanage.forward;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-10-21
 */
@Data
@ApiModel("接口转发及参数替换规则配置列表")
public class RequestForwardListResponse extends ToString {

    @ApiModelProperty("配置总数")
    private long total;

    @ApiModelProperty("配置接口列表")
    private List<RequestForwardBean> uris;
}
