package com.timevale.saas.common.manage.core.model.response;

import com.timevale.saas.common.manage.core.model.response.bean.FailTaskBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@ApiModel("获取失败子任务列表响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class QueryFailedTaskResponse {
    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("子任务列表")
    private List<FailTaskBean> tasks;
}
