package com.timevale.saas.common.manage.core.model.enums;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.Getter;

/**
 * TODO 印章类型
 *
 * <AUTHOR>
 * @since 2022/11/15
 */
@Getter
public enum SealTypeEnum {
    ORGAN_SEAL("ORGAN_SEAL", "企业章"),
    LEGAL_SEAL("LEGAL_SEAL", "法人章");
    private final String type;
    private final String desc;

    SealTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static SealTypeEnum convert(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }
        for (SealTypeEnum sealTypeEnum : values()) {
            if (sealTypeEnum.getType().equals(type)) {
                return sealTypeEnum;
            }
        }
        return null;
    }
}
