package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("法人章模版详细信息")
public class LegalSealTemplateDTO {

    @ApiModelProperty(
            value =
                    "印章样式<br>"
                            + "矩形章-带框（RECTANGLE_BORDER）<br>"
                            + "矩形章-不带框(RECTANGLE_NO_BORDER)<br>"
                            + "方形左大字-带框（SQUARE_LEFT_BORDER）<br>"
                            + "方形左大字-不带框（SQUARE_LEFT_NO_BORDER）<br>"
                            + "方形右大字-带框（SQUARE_RIGHT_BORDER）<br>"
                            + "方形右大字-不带框（SQUARE_RIGHT_NO_BORDER）")
    private String templateType;

    @ApiModelProperty(
            value =
                    "印章尺寸：10_20、20_20等枚举值，见印章尺寸枚举<br>"
                            + "矩形章-带框：10_20<br>"
                            + "矩形章-不带框：10_20<br>"
                            + "方形左大字-带框:20_20 、18_18 、 16_16 (其他方形章也一样)")
    private String widthHeight;

    @ApiModelProperty(value = "印章颜色，RED-红色，BLUE-蓝色，BLACK-黑色，PURPLE-紫色")
    private String color = "RED";

    @ApiModelProperty(value = "不透明度 0~100。 100表示不透明")
    private Short opacity = 60;

    @ApiModelProperty(
            value = "印章做旧样式，NONE-无，OLD-印章做旧(随机样式，如果需指定做旧样式，指定1-12样式id，如OLD_1、OLD_2)，默认NONE")
    private String style = "NONE";

    @ApiModelProperty(value = "印后缀规则, 0什么都不加，1加印，2加之印")
    private Integer stampRule = 0;

    @ApiModelProperty(value = "印章内容文本")
    private String text;
}
