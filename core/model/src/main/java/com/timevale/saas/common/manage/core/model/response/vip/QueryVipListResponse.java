package com.timevale.saas.common.manage.core.model.response.vip;

import com.google.common.collect.Lists;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */

@Data
@ApiModel("获取会员版本列表响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class QueryVipListResponse {
    @ApiModelProperty("会员信息列表")
    private List<VipBean> vips = Lists.newArrayList();
}
