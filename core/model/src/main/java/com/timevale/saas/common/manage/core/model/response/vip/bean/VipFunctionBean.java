package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-08-20
 */
@Data
public class VipFunctionBean extends ToString {

    @ApiModelProperty("功能标识")
    private String code;

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("是否支持")
    private boolean enable;

    @ApiModelProperty("是否增配的会员功能")
    private boolean addition;

    @ApiModelProperty("会员功能限制，max_batch_count表示批量最大上限")
    private Map<String, Object> limit;
    
    @ApiModelProperty("试用状态")
    private String trialStatus;

    @ApiModelProperty("试用截止日期")
    private Long trialEndTime;
}
