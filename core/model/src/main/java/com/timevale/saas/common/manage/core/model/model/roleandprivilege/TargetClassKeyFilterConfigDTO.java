package com.timevale.saas.common.manage.core.model.model.roleandprivilege;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/9 权限资源过滤数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TargetClassKeyFilterConfigDTO {
    /** 权限资源 */
    private String targetClassKey;
    /** 权限资源操作 */
    private List<String> operationPermits;
    /** 是否过滤权限的所有操作 */
    private Boolean isAllOperationPermitFilter = false;
}
