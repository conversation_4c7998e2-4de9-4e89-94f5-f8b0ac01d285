package com.timevale.saas.common.manage.core.model.model.activity.bean;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("按钮信息")
public class ButtonInfo extends ToString {

    @ApiModelProperty("按钮文案")
    @NotBlank(message = "按钮文案不能为空")
    private String label;

    @ApiModelProperty("按钮颜色")
    private String color;

    @ApiModelProperty("按钮链接地址")
    private String url;
}
