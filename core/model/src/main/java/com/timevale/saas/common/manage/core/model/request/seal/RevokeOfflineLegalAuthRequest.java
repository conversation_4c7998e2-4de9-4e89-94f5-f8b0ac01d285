package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("撤销线下法人授权请求")
public class RevokeOfflineLegalAuthRequest extends ToString {

    @ApiModelProperty(value = "orgId", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiParam(name = "legalAuthId", value = "授权业务id")
    private String legalAuthId;
}
