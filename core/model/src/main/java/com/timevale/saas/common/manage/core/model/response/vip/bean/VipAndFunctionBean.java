package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.base.util.MapUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

import static com.timevale.saas.common.manage.common.service.constant.FunctionLimitConstant.MAX_BATCH_COUNT;

/**
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VipAndFunctionBean extends ToString {
    @ApiModelProperty("功能标识")
    private String code;

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("是否支持")
    private boolean enable;

    @ApiModelProperty("会员功能限制，max_batch_count表示批量最大上限")
    private Map<String, Object> limit;

    @ApiModelProperty("会员等级标识")
    private String vipCode;

    @ApiModelProperty("会员等级名称")
    private String vipName;

    @ApiModelProperty("会员类型 1 普通 2 赠送")
    private Integer vipType;

    public Integer parseBatchLimit() {
        if (MapUtils.isNotEmpty(limit)) {
            Object obj = limit.get(MAX_BATCH_COUNT);
            if (null != obj && StringUtils.isNumeric(obj.toString())) {
                return Integer.valueOf(obj.toString());
            }
        }
        return null;
    }
}
