package com.timevale.saas.common.manage.core.model.bo.share;

import lombok.Getter;
import lombok.Setter;

/**
 * 资源分享模型基类对象
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@Setter
@Getter
public class ResourceShareTargetAuthBO {

    /** 资源分享id */
    private String resourceShareId;
    /** 资源id */
    private String resourceId;
    /** 角色id,对应企业控制台一级权限 */
    private String roleId;
    /** 角色key,对齐企业控制台一级权限 */
    private String roleKey;
    /** 创建时间 */
    private String createTime;
    /** 更新时间 */
    private String updateTime;
}
