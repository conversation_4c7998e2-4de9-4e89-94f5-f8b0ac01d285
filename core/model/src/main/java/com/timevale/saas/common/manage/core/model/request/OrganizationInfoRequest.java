package com.timevale.saas.common.manage.core.model.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/10/14
 */
@Setter
@Getter
@AllArgsConstructor
public class OrganizationInfoRequest extends ToString {

    @ApiModelProperty("当前企业orgId")
    private String orgId;

    @ApiModelProperty("当前用户oid")
    private String accountOid;

    @ApiModelProperty("钉签场景中corpId")
    private String dingCorpId;

    @ApiModelProperty("钉签场景中isvAppId")
    private String isvAppId;
    @ApiModelProperty(value = "主企业oid,目前只支持主企业对应计费信息查询")
    private String primaryOid;

    /**
     * 当前请求是否来自钉钉
     *
     * @return true or false
     */
    public boolean isFromDing() {
        return StringUtils.isNoneBlank(this.getDingCorpId(), this.getIsvAppId());
    }
}
