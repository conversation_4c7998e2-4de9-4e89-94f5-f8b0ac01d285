package com.timevale.saas.common.manage.core.model.model;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.response.QueryOrganizationInfoResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/30
 */
@Data
public class OrganAccountCacheBean extends ToString {

    private String orgOid;

    private String orgGid;
    /** 主体名称 */
    private String subjectName;
    /** 主体头像 */
    private String subjectPic;
    /** 管理员名称 */
    private String subjectAdmin;
    /** 是否是虚拟管理员 */
    private Boolean virtualAdmin = false;

    /** 是否有管理员 */
    private Boolean existAdmin = false;
    /** 会员等级 */
    private Integer vipLevel;
    /** 会员等级标识 */
    private Integer vipCode;
    /** 是否使用会员等级标识 */
    private boolean useVipCode;
    /** 是否即将过期 */
    private Boolean expiring;
    /** 截止时间 */
    private Date effectiveTo;
    /** 起始时间 */
    private Date effectiveFrom;
    /** 是否无限 */
    private Boolean unlimitNum = false;
    /** 剩余量 */
    private BigDecimal margin = new BigDecimal(0);
    /** 计量单位 */
    private String units;
    /** 购买量 */
    private BigDecimal totalNum = new BigDecimal(0);
    /**
     * 主体类型
     */
    private boolean subjectOrgan;

    /** 套餐是否即将过期：0.未到期 1. 将到期 2. 已到期" */
    private Integer packageExpiring;

    /** "套餐到期日期" */
    private Date packageExpireDate;

    @ApiModelProperty("套餐不足比例，eg：30、15、5，不提醒为null")
    private BigDecimal packageWarnPct;


    /**
     * 计费产品
     */
    private List<QueryOrganizationInfoResponse.Margin> signMargin = new ArrayList<>();
}
