package com.timevale.saas.common.manage.core.model.model;

import com.timevale.account.service.enums.AccountType;
import com.timevale.account.service.model.constants.BuiltinProperty;
import com.timevale.account.service.model.service.biz.helper.acc.BizICUserOutputHelper;
import com.timevale.account.service.model.service.biz.icuser.output.BizICUserOutput;
import com.timevale.mandarin.common.result.ToString;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@NoArgsConstructor
public class AccountBean extends ToString {
    private String oid;
    private String gid;
    private String uid;
    private String name;
    /** 是否企业账号 */
    private boolean organ;

    public AccountBean(BizICUserOutput output) {
        oid = output.getId().getOuid();
        gid = output.getId().getGuid();
        uid = output.getId().getUuid();
        organ = AccountType.ORGANIZE.equals(output.getBase().getType());

        BizICUserOutputHelper detailHelper = new BizICUserOutputHelper(output);
        name = detailHelper.getPropertyValue(BuiltinProperty.INFO_NAME);
    }
}
