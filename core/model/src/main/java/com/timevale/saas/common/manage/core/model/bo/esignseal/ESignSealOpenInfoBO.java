package com.timevale.saas.common.manage.core.model.bo.esignseal;

import com.timevale.saas.common.manage.common.service.enums.ESignSealServiceStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/2/8 e签章开通信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ESignSealOpenInfoBO {
    /** 主体oid */
    private String subjectOid;
    /** 主体gid */
    private String subjectGid;
    /** e签章服务到期时间 */
    private Date expireDate;
    /** e签章状态 */
    private ESignSealServiceStatusEnum status;
}
