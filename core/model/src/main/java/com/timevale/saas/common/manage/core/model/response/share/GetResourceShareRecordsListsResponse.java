package com.timevale.saas.common.manage.core.model.response.share;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Author:jiany<PERSON>
 * @since 2021-07-10 17:35
 */
@Data
@Builder
public class GetResourceShareRecordsListsResponse extends ToString {
	@ApiModelProperty("分享记录总条数")
	private Long totalSize;

	@ApiModelProperty("分享记录列表")
	private List<GetResourceShareRecordsResponse> recordsResponses;
}
