package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.VipCodeEnum;
import com.timevale.saas.common.manage.common.service.enums.VipLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020-08-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountVip extends ToString {

    @ApiModelProperty("会员等级，1-基础版，2-高级版，3-智能版， 4-区块链版 5-尊享版")
    private Integer level = VipCodeEnum.defaultVipCode().getLevel();

    @ApiModelProperty("会员等级标识，BASE-基础版，SENIOR-高级版，PROFESSIONAL-专业版")
    private String vipCode = VipCodeEnum.defaultVipCode().getCode();

    @ApiModelProperty("会员等级名称，体验版，基础版，高级版，专业版")
    private String vipName= VipCodeEnum.defaultVipCode().getDesc();

    @ApiModelProperty("是否使用会员等级标识")
    private boolean useVipCode;

    private Integer vipType;

    @ApiModelProperty("会员有效期截止时间")
    private Date effectiveTo;

    @ApiModelProperty("会员有效起始时间")
    private Date effectiveFrom;

    @ApiModelProperty("租户企业名")
    private String tenantName;

    @ApiModelProperty("用户gid")
    private String gid;

    @ApiModelProperty("已过期的会员等级，只有当前等级是基础版时才可能有值，2-高级版，3-智能版， 4-区块链版")
    private Integer expiredLevel;

    @ApiModelProperty("已过期的会员等级标识，只有当前等级是基础版时才可能有值，BASE-基础版，PROFESSIONAL-专业版，SENIOR-高级版")
    private String expiredVipCode;

    public AccountVip(boolean useVipCode) {
        this.useVipCode = useVipCode;
    }

    public boolean expired() {
        return null != effectiveTo && new Date().after(effectiveTo);
    }

    public String getVipCode() {
        if (StringUtils.isBlank(vipCode) && null != level) {
            return VipLevelEnum.getVipLevelByValue(level).getCode();
        }
        return vipCode;
    }
}
