package com.timevale.saas.common.manage.core.model.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.List;

/**
 * 有效订单分组
 *
 * <AUTHOR>
 * @since 2023-02-10
 */
@Data
public class EffectiveOrderAggreResponse extends ToString {
    private List<EffectiveOrderSimpleResponse> other;
    private EffectiveOrderAggreSaasResponse saas;

    @Data
    public static class EffectiveOrderAggreSaasResponse extends ToString{
        private List<EffectiveOrderSimpleResponse> combos;
        private List<EffectiveOrderSimpleResponse> products;
    }
}