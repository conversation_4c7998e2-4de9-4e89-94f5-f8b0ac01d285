package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.SealInfoDTO;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取印章列表响应")
public class GetSealListResponse extends ToString {

    @ApiModelProperty(value = "查询总数")
    private Long total;

    @ApiModelProperty(value = "印章列表")
    @HasTranslateField
    private List<SealInfoDTO> seals;
}
