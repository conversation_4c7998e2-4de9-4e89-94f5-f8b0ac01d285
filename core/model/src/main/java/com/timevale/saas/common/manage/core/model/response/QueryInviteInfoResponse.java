package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/17
 */
@ApiModel("查询邀请详情返回结果")
@Data
public class QueryInviteInfoResponse {
    
    @ApiModelProperty("邀请id")
    private String inviteId;
    @ApiModelProperty("邀请人oid ")
    private String inviteOid;
    @ApiModelProperty("邀请人名称")
    private String inviteName;
    @ApiModelProperty("邀请主体oid ")
    private String inviteSubjectOid;
    @ApiModelProperty("邀请主体名称")
    private String inviteSubjectName;
    @ApiModelProperty("被邀请人手机号 ")
    private String inviteePhone;
    @ApiModelProperty("被邀请人姓名 ")
    private String inviteeName;
    @ApiModelProperty("被邀请人oid ")
    private String inviteeOid;
    @ApiModelProperty("被邀请人认证状态 ")
    private Integer inviteeAuthStatus;
    @ApiModelProperty("被邀请主体名称 ")
    private String inviteeSubjectName;
    @ApiModelProperty("被邀请主体oid ")
    private String inviteeSubjectOid;
    @ApiModelProperty("被邀请主体认证状态 ")
    private Integer inviteeSubjectAuthStatus;
}
