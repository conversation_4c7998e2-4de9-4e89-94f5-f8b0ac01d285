package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-12-08
 */
@Data
@ApiModel("批量修改接口访问限制配置信息请求参数")
public class BatchRequestAccessLimitUpdateInfoRequest extends ToString {

    @ApiModelProperty("访问限制id列表")
    @NotEmpty(message = "访问限制id列表不能为空")
    @Size(max = 100, message = "访问限制id列表单次不能超过100个")
    private List<Long> ids;

    @ApiModelProperty("业务域，非空场景下触发修改")
    private String bizDomain;

    @ApiModelProperty("接口负责人，非空场景下触发修改")
    private String owner;
}
