package com.timevale.saas.common.manage.core.model.request.subject;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/5/7
 */
@Data
@ApiModel("批量删除用户请求")
public class OrgBatchDeleteUsersRequest extends ToString {
    @ApiModelProperty("删除用户的oid列表")
    @NotEmpty(message = "删除用户信息不能为空")
    private List<String> deleteAccountList;
}
