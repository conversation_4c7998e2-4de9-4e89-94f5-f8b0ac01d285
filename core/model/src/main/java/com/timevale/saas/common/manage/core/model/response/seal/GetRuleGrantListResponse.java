package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.seal.SealGrantDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("授权企业/成员信息列表响应")
public class GetRuleGrantListResponse extends ToString {
    @ApiModelProperty(value = "查询总数")
    private Integer total;

    @ApiModelProperty(value = "授权列表")
    private List<SealGrantDTO> grantList;
}
