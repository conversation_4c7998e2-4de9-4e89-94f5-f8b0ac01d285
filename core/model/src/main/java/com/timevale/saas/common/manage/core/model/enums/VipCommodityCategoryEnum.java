package com.timevale.saas.common.manage.core.model.enums;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.enums.VipCodeEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024-08-26
 */
@Getter
public enum VipCommodityCategoryEnum {
    /** 版本 */
    TRIAL(VipCodeEnum.TRIAL.getCode()),
    BASE(VipCodeEnum.BASE.getCode()),
    SENIOR(VipCodeEnum.SENIOR.getCode()),
    PROFESSIONAL(VipCodeEnum.PROFESSIONAL.getCode()),
    FLAGSHIP(VipCodeEnum.FLAGSHIP.getCode()),
    /** 签署流量 */
    SIGN("SIGN"),
    /** 增值服务 */
    ADDED("ADDED");

    private final String code;

    VipCommodityCategoryEnum(String code) {
        this.code = code;
    }

    public static boolean isLevel(String value) {
        return TRIAL.getCode().equals(value)
                || BASE.getCode().equals(value)
                || SENIOR.getCode().equals(value)
                || PROFESSIONAL.getCode().equals(value);
    }

    public static boolean isSign(String value) {
        return SIGN.getCode().equals(value);
    }

    public static boolean isAdded(String value) {
        return ADDED.getCode().equals(value);
    }
}
