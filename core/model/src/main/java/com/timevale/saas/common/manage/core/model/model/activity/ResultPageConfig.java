package com.timevale.saas.common.manage.core.model.model.activity;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.activity.bean.ButtonInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class ResultPageConfig extends ToString {

    @ApiModelProperty("app下载按钮文案")
    @NotBlank(message = "app下载按钮文案不能为空")
    private String appDownloadLabel;

    @ApiModelProperty("app下载按钮颜色")
    private String appDownloadColor;

    @ApiModelProperty("app下载地址")
    private String appDownloadUrl;

    @ApiModelProperty("app跳转路由")
    private String appRedirectRoute;

    @ApiModelProperty("其他按钮配置")
    private List<ButtonInfo> extraButtons;
}
