package com.timevale.saas.common.manage.core.model.model.cert;

import com.timevale.saas.common.manage.common.service.enums.cert.CertInfoType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/28 证书信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CertInfoBO {

    /** 证书主体名称 */
    private String certName;

    /** 发证方名称 */
    private String caName;

    /** 证件号 */
    private String idNumber;
    /** 有效期开始时间 */
    private Date startTime;

    /** 有效期结束时间 */
    private Date endTime;

    /** 证书证件号SN */
    private String sn;

    /** 证书信息类型 */
    private CertInfoType certInfoType;
    /** 证书签发协议图片地址列表 pdf的1页为一张图片 */
    private List<String> issueImageUrls;
    /** 证书签发协议pdf地址 */
    private String issuePdfUrl;
}
