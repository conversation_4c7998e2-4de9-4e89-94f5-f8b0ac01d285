package com.timevale.saas.common.manage.core.model.response.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/19
 **/
@Data
@ApiModel("栏目管理端分页查询返回结构")
public class ColumnPageResponse {

    @ApiModelProperty("当前页返回数据")
    private List<ColumnPageInfo> columnList;

    @ApiModelProperty("数据总数")
    private Integer total;
}
