package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2021-05-31 17:52
 **/
@ApiModel("banner图片信息")
public class BannerPicInfo extends ToString {

    @ApiModelProperty("banner唯一标示")
    private Long bannerId;

    @ApiModelProperty("图片url")
    private String picUrl;

    @ApiModelProperty("图片链接")
    private String linkUrl;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("引导文案")
    private String guideText;

    public String getGuideText() {
        return guideText;
    }

    public void setGuideText(String guideText) {
        this.guideText = guideText;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getBannerId() {
        return bannerId;
    }

    public void setBannerId(Long bannerId) {
        this.bannerId = bannerId;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }
}
