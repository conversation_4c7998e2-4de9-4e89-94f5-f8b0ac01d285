package com.timevale.saas.common.manage.core.model.model;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020-11-06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FailBean extends ToString {

    @ApiModelProperty("失败类型 1-可重试失败 2-不可重试并明确失败原因 3-不可重试未明确失败原因")
    private Integer type;

    @ApiModelProperty("失败原因")
    private String reason;
}
