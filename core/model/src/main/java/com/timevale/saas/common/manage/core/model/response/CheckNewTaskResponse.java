package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */

@Data
@ApiModel("判断是否有新任务响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class CheckNewTaskResponse {
    @ApiModelProperty("是否有新任务")
    private boolean hasNewTask;
}
