package com.timevale.saas.common.manage.core.model.response.nps;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/1/5
 */
@Setter
@Getter
public class NpsInfoAdminDetailResponse extends ToString {

    @ApiModelProperty("第三方问卷id")
    private String thirdNpsId;

    @ApiModelProperty("当前nps策略信息id")
    private Long id;

    @ApiModelProperty("问卷名称")
    private String name;

    @ApiModelProperty("问卷位置")
    private String areaCode;

    @ApiModelProperty("问卷位置")
    private String areaName;

    @ApiModelProperty("问卷产品")
    private String productCode;

    @ApiModelProperty("问卷产品")
    private String productName;

    @ApiModelProperty("页面停留时长： 秒")
    private Long pageStayTime;

    @ApiModelProperty("1-用户gid维度  2-用户gid维度+设备维度")
    private Integer showType;

    @ApiModelProperty("显示周期：多长时间显示一次问卷 ：天")
    private Integer showCycle;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("修改时间")
    private Long updateTime;

    @ApiModelProperty("上架状态 0下架 1上架")
    private Integer status;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("修改人")
    private String modifier;

}
