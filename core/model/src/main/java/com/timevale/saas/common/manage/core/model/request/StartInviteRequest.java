package com.timevale.saas.common.manage.core.model.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 *
 * @date 2022/11/14
 */
@ApiModel("邀请发起请求")
@Data
public class StartInviteRequest {

    @ApiModelProperty("被邀请人手机号")
    @NotBlank(message = "被邀请手机号不能为空")
    private String inviteePhone;

    @ApiModelProperty("被邀请人姓名")
    @NotBlank(message = "被邀请人姓名不能为空")
    @Size(min = 2, max = 16, message = "用户姓名长度为2-16字，请修改")
    private String inviteeName;

    @ApiModelProperty("被邀请主体名称")
    @NotBlank(message = "被邀请企业名称不能为空")
    @Size(max = 199, message = "企业名称最多199字，请修改")
    private String inviteeCompanyName;

    @ApiModelProperty("邀请方式 0短信 1微信")
    @NotNull(message = "请选择邀请方式")
    @Max(value = 1, message = "未知邀请类型")
    @Min(value = 0, message = "未知邀请类型 ")
    private Integer type;
}
