package com.timevale.saas.common.manage.core.model.model.vip;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR>
 * @since 2024-10-15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeleteVipAdditionDTO extends ToString {

    /** 当前操作人花名 */
    private String alias;

    /** 功能标识 */
    private String funcCode;

    /** 账号gid */
    private String accountGid;
}
