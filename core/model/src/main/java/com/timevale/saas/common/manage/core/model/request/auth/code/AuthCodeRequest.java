package com.timevale.saas.common.manage.core.model.request.auth.code;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 验证手机验证码参数
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
@Data
public class AuthCodeRequest extends ToString {
    //验证码唯一id
    @NotBlank
    private String authCodeId;
    //验证码
    @NotBlank
    private String code;
}
