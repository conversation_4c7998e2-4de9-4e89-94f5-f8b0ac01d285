package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2022/11/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取对应类型印章的已创建印章数量")
public class GetSealCreatedCountResponse extends ToString {
    @ApiModelProperty(value = "已创建的印章数量")
    private Integer createdCount;

    @ApiModelProperty(value = "会员版本最大允许创建数量，null代表不限制")
    private Integer vipMaxCreateCount;
}
