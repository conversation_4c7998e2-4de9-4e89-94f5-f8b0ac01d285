package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.banner.ColumnPicConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/22
 */
@Data
@ApiModel("栏目内容保存请求体")
public class ColumnSaveRequest extends ToString {

    @ApiModelProperty("产品code")
    @NotBlank(message = "产品唯一标识不可为空")
    private String productCode;

    @ApiModelProperty("位置code")
    @NotBlank(message = "位置唯一标识不可为空")
    private String areaCode;

    @ApiModelProperty("内容名称（管理端展示用）")
    private String name;

    @ApiModelProperty("弹出名称")
    private String title;

    @ApiModelProperty("跳转链接")
    private String linkUrl;

    @ApiModelProperty("文本内容")
    private String content;

    @ApiModelProperty("标签")
    private String label;

    @ApiModelProperty("权重")
    private Integer weight;

    @ApiModelProperty("图片配置信息")
    private List<ColumnPicConfigDTO> picFileList;

    @Data
    @ApiModel("图片配置信息")
    public static class ColumnPic {
        @ApiModelProperty("文件Key")
        private String fileKey;

        @ApiModelProperty("图片配置位置标识")
        private String position;
    }
}
