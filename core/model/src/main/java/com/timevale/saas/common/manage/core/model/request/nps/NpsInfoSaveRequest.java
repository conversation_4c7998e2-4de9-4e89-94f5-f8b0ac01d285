package com.timevale.saas.common.manage.core.model.request.nps;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @since 2022/1/5
 */
@Setter
@Getter
public class NpsInfoSaveRequest extends ToString {

    @ApiModelProperty("第三方问卷id")
    private String thirdNpsId;

    @ApiModelProperty("问卷名称")
    private String name;

    @ApiModelProperty("问卷位置")
    private String areaCode;

    @ApiModelProperty("问卷产品")
    private String productCode;

    @ApiModelProperty("页面停留时长： 秒")
    @Min(value = 0, message = "请输入正确的停留时长")
    @Max(value = 60, message = "停留时长不能超过60秒")
    @Valid
    private Integer pageStayTime;

    @ApiModelProperty("1-用户gid维度+渠道  2-用户gid维度+渠道+设备维度(目前不支持)")
    private Integer showType;

    @ApiModelProperty("显示周期：多长时间显示一次问卷 ：天")
    @Min(value = 1, message = "显示周期不能小于1天")
    @Max(value = 60, message = "显示周期不能大于60")
    @Valid
    private Integer showCycle;
}
