package com.timevale.saas.common.manage.core.model.response;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import java.util.Date;

/**
 * 用户行为记录返回
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/25 20:51
 */
@Data
public class UserActionResponse extends ToString {
    /**
     * 用户id
     */
    private String accountId;
    /**
     * 用户id 类型 0 oid, 1 gid
     */
    private Integer accountType;
    /**
     * 主体id
     */
    private String subjectId;
    /**
     * 主体id 类型  0 oid, 1 gid
     */
    private Integer subjectType;
    /**
     * 用户行为记录
     */
    private String action;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
}
