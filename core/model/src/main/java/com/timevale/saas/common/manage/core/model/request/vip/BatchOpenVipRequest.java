package com.timevale.saas.common.manage.core.model.request.vip;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.VipLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * batchOpenVipRequest
 *
 * <AUTHOR>
 * @since 2021/5/27 3:51 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BatchOpenVipRequest extends ToString {

    /**
     * gid列表
     */
    @NotNull(message = "GID列表不能为空")
    @NotEmpty(message = "GID列表不能为空")
    @Size(max = 500, message = "GID不能超过500条")
    @ApiModelProperty(value = "租户id列表", required = true)
    private List<String> gidList;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer vipLevel;

    /**
     * 版本标识
     */
    @ApiModelProperty(value = "版本标识")
    private String vipCode;

    /**
     * 版本日期
     */
    @NotNull(message = "版本开始日期不能为空")
    @ApiModelProperty(value = "版本开始日期", required = true)
    private Long expireDateStartStr;

    @NotNull(message = "版本结束日期不能为空")
    @ApiModelProperty(value = "版本结束日期", required = true)
    private Long expireDateEndStr;

    /**
     * 版本日期
     */
    @JsonIgnore
    private Date expireDateStart;

    @JsonIgnore
    private Date expireDateEnd;

    public String getVipCode() {
        if (StringUtils.isBlank(vipCode) && null != vipLevel) {
            vipCode = VipLevelEnum.getVipLevelByValue(vipLevel).getCode();
        }
        return vipCode;
    }
}
