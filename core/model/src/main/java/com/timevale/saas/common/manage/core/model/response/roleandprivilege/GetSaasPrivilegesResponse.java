package com.timevale.saas.common.manage.core.model.response.roleandprivilege;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.roleandprivilege.SaasPrivilegeDTO;
import com.timevale.saas.multilingual.translate.annotation.HasTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取saas所有权限响应")
public class GetSaasPrivilegesResponse extends ToString {
    @ApiModelProperty("saas权限列表")
    @HasTranslateField
    List<SaasPrivilegeDTO> saasPrivilegeList;
}
