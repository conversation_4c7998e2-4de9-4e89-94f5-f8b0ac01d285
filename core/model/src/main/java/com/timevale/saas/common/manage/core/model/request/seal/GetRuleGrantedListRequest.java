package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取被授权企业列表请求")
public class GetRuleGrantedListRequest extends ToString {
    @ApiModelProperty(name = "orgId", value = "企业id", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiModelProperty(name = "offset", value = "分页起始位置", required = true)
    @Min(value = 0, message = "offset不能小于0")
    Integer offset = 0;

    @ApiModelProperty(name = "size", value = "单页数量", required = true)
    @Min(value = 1, message = "size不能小于1")
    @Max(value = 30, message = "size不能大于30")
    Integer size = 10;

    @ApiModelProperty(name = "downloadFlag", value = "是否返回下载地址，默认true")
    boolean downloadFlag;

    @ApiModelProperty(value = "是否查询二级授权数量，默认false")
    private boolean secSealGrantNumQueryFlag = false;

    @ApiModelProperty(value = "企业下查询的印章id")
    private String sealId;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;
}
