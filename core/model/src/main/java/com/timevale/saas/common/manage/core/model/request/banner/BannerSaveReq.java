package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.banner.PushUserConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-31 16:59
 **/
@Data
@ApiModel("banner新增/修改请求信息")
public class BannerSaveReq extends ToString {

    @ApiModelProperty("banner唯一标示")
    private Long bannerId;

    @ApiModelProperty("产品编码")
    @NotBlank(message = "请选择产品端")
    @Length(max = 32, message = "产品编码不能超过32个字符")
    private String productCode;

    @ApiModelProperty("位置编码")
    @NotBlank(message = "请选择位置编码")
    @Length(max = 32, message = "位置编码不能超过32个字符")
    private String areaCode;

    @ApiModelProperty("banner名称")
    @NotBlank(message = "banner名称不能为空")
    @Length(max = 20, message = "banner名称不能超过20个字符")
    private String bannerName;

    @ApiModelProperty("图片链接")
    @NotBlank(message = "图片链接不能为空")
    @Length(max = 1024, message = "图片链接不能超过1024个字符")
    private String linkUrl;

    @ApiModelProperty("图片key")
    @NotBlank(message = "图片key不能为空")
    private String fileKey;

    @ApiModelProperty("排序号")
    @NotNull(message = "排序号不能为空")
    @Min(value = 0, message = "排序号不能是负数")
    @Max(value = 100, message = "排序好最大支持100")
    private Integer orderNo;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("有效用户配置")
    private List<PushUserConfigDTO> validityUserConfig;

    @ApiModelProperty("公告类型:1.产品迭代更新、2.新功能上线、3.交互体验升级、4.系统问题通知、5.其他")
    private Integer noticeType;

    @ApiModelProperty("支持的vip版本，为空表示所有版本都展示")
    private List<String> notifyVersions;

    @ApiModelProperty("支持的角色，为空表示所有角色都展示")
    private List<String> notifyRoles;

    @ApiModelProperty("引导文案")
    private String guideText;

    @ApiModelProperty("有效开始时间")
    private String validityStartTime;

    @ApiModelProperty("有效结束时间")
    private String validityEndTime;

    @ApiModelProperty("客户端版本号")
    private Integer clientVersionCode;

    /** {@link ConditionTypeEnum} */
    @ApiModelProperty("版本号显示条件: lt:小于，le:小于等于，eq:等于，ge：大于等于，gt:大于")
    private String conditionType;
}
