package com.timevale.saas.common.manage.core.model.request.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量编辑印章授权请求")
public class UpdateRuleGrantsRequest extends UrlBaseRequest{

    @ApiModelProperty(name = "orgId", value = "企业id", required = true)
    @NotBlank(message = "企业id不能为空")
    private String orgId;

    @ApiModelProperty(value = "授权资源列表", required = true)
    List<BatchRuleItemDTO> ruleGrantedList;

    @ApiModelProperty(value = "资源id", required = true)
    private String resourceId;

    @ApiModelProperty(value = "授权类型  1-企业内授权  2-企业外授权", required = true)
    private Integer type;

    @ApiModelProperty(value = "授权签署重定向地址")
    private String grantRedirectUrl;

    @ApiModelProperty(value = "是否获取h5端地址", allowableValues = "false,true")
    private boolean h5;

    @ApiModelProperty("登录token")
    private String token;

    @ApiModelProperty(value = "app协议")
    private String appScheme;
    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

    @ApiModelProperty(name = "sealOwnerAdminOid", value = "印章归属企业的管理员OID")
    private String sealOwnerAdminOid;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel("批量印章授权条目信息")
    public static class BatchRuleItemDTO {
        @ApiModelProperty(value = "业务主键id", required = true)
        @NotNull(message = "业务主键id不能为空")
        private String ruleGrantedId;

        @ApiModelProperty(value = "失效时间", required = true)
        @NotNull(message = "失效时间不能为空")
        private Long expireTime;

        @ApiModelProperty(value = "生效时间", required = true)
        @NotNull(message = "生效时间不能为空")
        private Long effectiveTime;
    }
}
