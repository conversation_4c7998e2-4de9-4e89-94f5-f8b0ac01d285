package com.timevale.saas.common.manage.core.model.request.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

/**
 * <AUTHOR>
 *
 * @date 2022/3/17
 */
@Data
@ApiModel("新增/删除指定推送用户请求体")
public class UpdatePushUserListRequest {
    @ApiModelProperty("内容类型 1banner 2弹窗 3栏目内容")
    @NotNull(message = "请选择内容类型")
    private Integer type;

    @ApiModelProperty("数据类型 1oid 2手机号")
    @NotNull(message = "请选择数据类型")
    private Integer dataType;

    @ApiModelProperty("添加用户列表")
    private Set<String> addUserIds;

    @ApiModelProperty("删除用户列表 重复优先删除")
    private Set<String> delUserIds;
}
