package com.timevale.saas.common.manage.core.model.model.roleandprivilege;

import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/6/7 saas权限
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
/**
 * saas权限
 */
public class SaasPrivilegeDTO {
    /** 权限操作资源 */
    private String targetClassKey;

    /** 权限操作资源名称 */
    @NeedTranslateField
    private String targetClassKeyName;
    /** 权限操作类型 */
    private String operationPermit;
    /** 权限操作类型名称 */
    @NeedTranslateField
    private String operationPermitName;

    /** 权限操作类型描述 */
    private String operationDisc;

    /** 权限的权重值 */
    private Integer weight;
}
