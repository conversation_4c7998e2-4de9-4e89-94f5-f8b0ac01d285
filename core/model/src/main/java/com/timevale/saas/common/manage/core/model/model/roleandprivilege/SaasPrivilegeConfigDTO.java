package com.timevale.saas.common.manage.core.model.model.roleandprivilege;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/6/7 saas权限
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
/** saas权限配置 */
public class SaasPrivilegeConfigDTO {
    /** 权限操作资源 */
    private String targetClassKey;

    /** 权限的权重值 */
    private Integer weight;
}
