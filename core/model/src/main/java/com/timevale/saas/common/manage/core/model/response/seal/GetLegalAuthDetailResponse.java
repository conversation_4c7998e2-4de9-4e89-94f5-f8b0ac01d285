package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.base.util.SecurityUtil;
import com.timevale.saas.common.manage.core.model.model.seal.*;
import com.timevale.tool.ValidateUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("查看法人章授权详情响应")
public class GetLegalAuthDetailResponse extends ToString {
    @ApiModelProperty(value = "授权基本信息")
    private LegalAuthBaseInfoDTO legalAuthInfo;

    @ApiModelProperty(value = "法人信息")
    private LegalAuthAccountDTO legalAuthAccountInfo;

    @ApiModelProperty(value = "授权申请人信息")
    private LegalAuthApplyPersonDTO applyPerson;

    @ApiModelProperty(value = "授权流程信息")
    private LegalAuthFlowInfoDTO authFlowInfo;

    @ApiModelProperty(value = "授权模板文档")
    private FileDocDTO authSignDoc;

    @ApiModelProperty(value = "线下授权信息")
    private LegalOfflineApplyDTO authFileInfo;

    @ApiModelProperty(value = "法人授权数据唯一标识")
    private String legalAuthId;

    @ApiModelProperty(value = "授权状态，0-未发起过授权，2-授权中，3-授权失败，1-授权成功，4-授权失效")
    private Integer authStatus;

    @ApiModelProperty(value = "授权类型，1-在线授权，2-线下纸质授权")
    private Integer authType;

    @ApiModelProperty(value = "授权失效原因类型，1-管理员变更，2-法人变更，9-其它")
    private Integer expireType;

    /**
     * 身份证号和手机号的脱敏
     *
     * @return 身份证脱敏后的结果
     */
    public GetLegalAuthDetailResponse maskingIdAndMobileNumber() {
        try {
            if (legalAuthAccountInfo != null) {
                legalAuthAccountInfo.setLegalNumber(
                        SecurityUtil.idNumberMasking(legalAuthAccountInfo.getLegalNumber()));
            }
            if (applyPerson != null) {
                applyPerson.setIdNo(SecurityUtil.idNumberMasking(applyPerson.getIdNo()));
                if (ValidateUtil.mobileValid(applyPerson.getIdCard())) {
                    // 如果登录凭证是手机号，需要做下脱敏
                    applyPerson.setIdCard(SecurityUtil.mobileMaking(applyPerson.getIdCard()));
                }
            }
        } catch (Exception e) {
            // 脱敏的地方做下降级直接返回，但是问题需要关注，打印error
            log.error("脱敏异常", e);
        }
        return this;
    }
}
