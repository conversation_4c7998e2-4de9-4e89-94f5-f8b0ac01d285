package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("请求的url基本信息")
public class UrlBaseRequest extends ToString {
    /** 建议使用 clientType */
    @ApiModelProperty(value = "是否获取h5端地址", allowableValues = "false,true")
    private boolean h5;

    @ApiModelProperty("登录token")
    private String token;

    @ApiModelProperty(value = "app协议")
    private String appScheme;

    /** 过度，用于印章授权链接 */
    @ApiModelProperty(value = "免登key")
    @Deprecated
    private String authKey;

    @ApiModelProperty(value = "应用id", hidden = true)
    private String appId;

    @ApiModelProperty(value = "重定向地址", hidden = true)
    private String grantRedirectUrl;

    /** 可以传 x-tsign-client-id，但是目前只支持的类型 参考 signReturnPageType */
    @ApiModelProperty(value = "客户端标识", hidden = true)
    private String clientType;

    /** 签署返回页类型, 此值不需要主动赋值 逻辑可以参考 SignReturnPageTypeEnum#getSignReturnPageType */
    @ApiModelProperty(value = "客户端类型： ALL - 自动适配移动端或PC端;H5 - 移动端适配;PC - PC端适配;默认ALL", hidden = true)
    private String signReturnPageType;
}
