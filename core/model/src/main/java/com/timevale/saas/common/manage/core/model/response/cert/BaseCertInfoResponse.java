package com.timevale.saas.common.manage.core.model.response.cert;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/10/28
 */
@ApiModel("证书基本信息响应")
@Data
public class BaseCertInfoResponse extends ToString {
	@ApiModelProperty("证书持有者名称")
	private String certName;

	@ApiModelProperty("证书颁发者名称")
	private String caName;

	@ApiModelProperty("证书序列号")
	private String sn;

	@ApiModelProperty("证件号")
	private String idNumber;

	@ApiModelProperty("证书有效开始时间")
	private Long validStartTime;

	@ApiModelProperty("证书有效截止时间")
	private Long validEndTime;

    @ApiModelProperty("证书签发协议图片地址列表，pdf的1页为一张图片")
    private List<String> issueImageUrls;

    @ApiModelProperty("证书签发协议pdf地址")
    private String issuePdfUrl;
}
