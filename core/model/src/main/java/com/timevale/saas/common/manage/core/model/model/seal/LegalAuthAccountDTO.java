package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("法人信息")
public class LegalAuthAccountDTO {
    @ApiModelProperty(value = "被授权企业主体名称")
    private String orgName;

    @ApiModelProperty(value = "被授权企业主体证件号")
    private String orgNumber;

    @ApiModelProperty(value = "授权法人名称")
    private String legalName;

    @ApiModelProperty(value = "授权法人证件号")
    private String legalNumber;

    @ApiModelProperty(value = "授权法人证件类型")
    private String legalCertType;

    @ApiModelProperty(value = "授权法人账号oid")
    private String legalAccount;

    @ApiModelProperty(value = "授权法人手机号邮箱")
    private String legalPrincipal;

    @ApiModelProperty(value = "法人信息是否完整，法人名称和法人证件号都不为空才算完整")
    private boolean legalInfoCheck;

    /** 看这个类 LegalInfoStateConstant */
    @ApiModelProperty(value = "法人当前的状况，1、法人有账号名称和证件号；2、法人证件号为空，但是有办法拿到；3、法人证件号为空，无法拿到")
    private Integer legalInfoState;
}
