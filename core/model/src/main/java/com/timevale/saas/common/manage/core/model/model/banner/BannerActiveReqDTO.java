package com.timevale.saas.common.manage.core.model.model.banner;

import com.timevale.mandarin.common.result.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2021-06-02 10:37
 **/
public class BannerActiveReqDTO extends ToString {

    @NotNull(message = "bannerId不能为空")
    @Min(value = 1, message = "bannerId不能小于1")
    private Long bannerId;

    @NotNull(message = "active不能为空")
    private Boolean active;

    private String token;


    public Long getBannerId() {
        return bannerId;
    }

    public void setBannerId(Long bannerId) {
        this.bannerId = bannerId;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }
}
