package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@ApiModel("批量新增接口访问限制请求参数")
public class RequestAccessLimitBatchAddRequest extends ToString {

    @ApiModelProperty("接口访问限制列表")
    @NotEmpty(message = "接口访问限制列表不能为空")
    private List<RequestAccessLimitAddRequest> urlLimits;
}
