package com.timevale.saas.common.manage.core.model.request.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.VipAdditionFunctionHandleSceneEnum;
import com.timevale.saas.common.manage.common.service.enums.VipAdditionFunctionExecuteActionEnum;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 企业会员功能入参
 *
 * <AUTHOR>
 * @since 2024-10-15
 */

@Data
public class CompanyVipAdditionFunctionInput extends ToString {

    /**
     * 授权企业 gid
     */
    private String authTenantGid;

    /**
     * 授权企业 oid
     */
    private String authTenantOid;

    /**
     * 主企业 gid
     */
    private String parentTenantGid;

    /**
     * 主企业 oid
     */
    private String parentTenantOid;

    /**
     * 关联企业 gid
     */
    private String childTenantGid;

    /**
     * 关联企业 oid
     */
    private String childTenantOid;

    /**
     * 有效期开始时间
     */
    private Date effectiveStartTime;

    /**
     * 有效期结束时间
     */
    private Date effectiveEndTime;
    
    /** 
     * 场景 
     * @see VipAdditionFunctionHandleSceneEnum 
     */
    private Integer sceneCode;

    /**
     * 执行动作
     * @see VipAdditionFunctionExecuteActionEnum
     */
    private Integer executeAction;

    /**
     * 指定的增配续费内容
     */
    private List<String> funcCodesFromRenew;
    
    
}
