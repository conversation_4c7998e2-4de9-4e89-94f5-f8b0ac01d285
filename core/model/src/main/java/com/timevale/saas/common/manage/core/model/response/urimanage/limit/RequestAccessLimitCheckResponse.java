package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
@ApiModel("根据指定接口获取访问限制配置返回数据")
public class RequestAccessLimitCheckResponse extends ToString {

    @ApiModelProperty("是否需要登录")
    private Boolean needLogin;

    @ApiModelProperty("是否白名单")
    private Boolean whitelist;
}
