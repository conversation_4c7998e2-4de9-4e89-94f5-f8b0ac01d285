package com.timevale.saas.common.manage.core.model.request.share;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.share.ResourceShareStatusEnum;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 修改资源分享配置信息
 *
 * <AUTHOR>
 * @since 2020/12/6
 */
@ApiModel(value = "修改资源分享配置信息")
@Getter
@Setter
public class ChangeResourceShareConfigRequest extends ToString {

    @ApiModelProperty(value = "当前登录操作人oid")
    @NotBlank(message = "accountId不能为空")
    private String accountId;

    @ApiModelProperty(value = "当前空间主体oid")
    @NotBlank(message = "subjectId不能为空")
    private String subjectId;

    @ApiModelProperty(value = "资源分享id", required = true)
    @NotBlank(message = "resourceShareId不能为空")
    private String resourceShareId;

    /** 修改资源分享状体 请参考{@link ResourceShareStatusEnum} */
    @ApiModelProperty(value = "分享状态")
    @Min(value = 0, message = "status值不正确")
    @Max(value = 1, message = "status值不正确")
    private Integer status;

    /** 是否需要实名 */
    @ApiModelProperty(value = "是否需要实名")
    private Boolean needRealName;
}
