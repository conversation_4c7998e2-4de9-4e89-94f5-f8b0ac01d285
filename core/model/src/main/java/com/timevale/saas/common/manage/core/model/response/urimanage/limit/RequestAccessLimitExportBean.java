package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.request.urimanage.limit.RequestAccessLimitAddRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@ApiModel("导出访问限制配置对应的导出信息")
public class RequestAccessLimitExportBean extends ToString {

    @ApiModelProperty("接口访问限制导出列表")
    private List<RequestAccessLimitAddRequest> urlLimits;
}
