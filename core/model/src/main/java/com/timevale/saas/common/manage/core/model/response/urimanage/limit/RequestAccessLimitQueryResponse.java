package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
@ApiModel("根据精确接口获取访问限制配置返回数据")
public class RequestAccessLimitQueryResponse extends ToString {

    @ApiModelProperty("接口符合正则规范的有效访问限制配置")
    private RequestAccessLimitBean validLimit;
    @ApiModelProperty("接口本身精确匹配的访问限制配置")
    private RequestAccessLimitBean uriLimit;
}
