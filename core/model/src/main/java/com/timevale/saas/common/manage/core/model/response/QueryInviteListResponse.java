package com.timevale.saas.common.manage.core.model.response;

import com.timevale.saas.common.manage.common.dal.dataobject.InviteRecordDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2022/11/14
 */
@Data
@ApiModel("查询列表返回结果")
public class QueryInviteListResponse {
    @ApiModelProperty("返回数据")
    private List<Record> data;

    @ApiModelProperty("数据总数")
    private Long total;

    @Data
    @ApiModel("列表返回数据结构")
    public static class Record {
        @ApiModelProperty("邀请id")
        private String inviteId;

        @ApiModelProperty("被邀请人手机号")
        private String inviteePhone;

        @ApiModelProperty("被邀请人姓名")
        private String inviteeName;

        @ApiModelProperty("被邀请人oid")
        private String inviteeOid;

        @ApiModelProperty("被邀请人认证状态")
        private Integer inviteeAuthStatus;

        @ApiModelProperty("被邀请企业名称")
        private String inviteeCompanyName;

        @ApiModelProperty("被邀请主体名称")
        private String inviteeSubjectName;

        @ApiModelProperty("被邀请主体oid")
        private String inviteeSubjectOid;

        @ApiModelProperty("被邀请主体认证状态")
        private Integer inviteeSubjectAuthStatus;

        @ApiModelProperty("邀请类型 0短信 1微信")
        private Integer type;

        @ApiModelProperty("创建时间 邀请发起时间")
        private Date createTime;
    }
}
