package com.timevale.saas.common.manage.core.model.response.nps;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2022/1/4
 */
@Setter
@Getter
public class NpsInfo extends ToString {

    @ApiModelProperty("第三方问卷id")
    private String thirdNpsId;

    @ApiModelProperty("当前nps策略信息id")
    private Long npsId;

    @ApiModelProperty("问卷名称")
    private String name;

    @ApiModelProperty("问卷位置")
    private String areaCode;

    @ApiModelProperty("问卷产品")
    private String productCode;

    @ApiModelProperty("页面停留时长： 秒")
    private Integer pageStayTime;
}
