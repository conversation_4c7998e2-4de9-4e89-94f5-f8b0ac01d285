package com.timevale.saas.common.manage.core.model.response.nps;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/5
 */
@Setter
@Getter
public class NpsInfoPageResponse extends ToString {

    @ApiModelProperty("问卷信息")
    private List<NpsInfoAdminDetailResponse> npsInfoAdmins;

    @ApiModelProperty("数据总数")
    private Integer total;
}
