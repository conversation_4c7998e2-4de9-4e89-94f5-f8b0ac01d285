package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 印章审核催办入参
 *
 * <AUTHOR>
 * @since 2022-09-20 15:17
 */
@Data
public class SealGrantAuditUrgeRequest extends ToString {

    @ApiModelProperty(name = "sealId", value = "印章id", required = true)
    @NotBlank(message = "sealId不能为空")
    private String sealId;


    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;
}
