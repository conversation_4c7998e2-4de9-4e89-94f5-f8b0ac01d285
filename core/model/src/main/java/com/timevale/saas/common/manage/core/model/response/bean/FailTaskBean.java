package com.timevale.saas.common.manage.core.model.response.bean;

import com.timevale.saas.common.manage.common.service.model.input.bean.ParticipantBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@ApiModel("失败子任务信息")
public class FailTaskBean {

    @ApiModelProperty("任务关联的业务主键id, eg.processId")
    private String bizId;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("任务接收者信息")
    private String taskReceiver;

    @ApiModelProperty("参与人信息")
    private List<ParticipantBean> participants;

    @ApiModelProperty("任务失败原因")
    private String failReason;

    @ApiModelProperty("任务失败类型 1-可重试失败 2-不可重试并明确失败原因 3-不可重试未明确失败原因")
    private Integer failType;

    @ApiModelProperty("任务处理建议")
    private String solution;
}
