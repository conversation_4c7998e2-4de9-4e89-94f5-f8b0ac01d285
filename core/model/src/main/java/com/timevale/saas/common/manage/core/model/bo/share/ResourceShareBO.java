package com.timevale.saas.common.manage.core.model.bo.share;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 资源分享模型基类对象
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@Setter
@Getter
public class ResourceShareBO extends BaseResourceShareBO {

    /** 资源分享操作方操作人oid */
    private String resourceOperatorOid;
    /** 资源分享操作方操作人gid */
    private String resourceOperatorGid;
    /** 资源分享操作方操作人账号 */
    private String resourceOperatorAccount;
    /** 资源分享操作方操作人姓名 */
    private String resourceOperatorName;
    /** 资源分享操作方主体oid */
    private String resourceSubjectGid;
    /** 资源分享操作方主体gid */
    private String resourceSubjectOid;

    /**
     * 调用来源,1:经办合同,2:企业合同
     */
    private String callSource;

    /**
     * 分类id
     */
    private String menuId;

    /* 资源名称 */
    private String resourceName;

    /** 是否有权限 **/
    private Boolean hasAuth;
    /** 分享类型1:分享给合同参与人,2:分享给非合同参与人 **/
    private Integer shareType;
    /*资源分享对象集合*/
    List<ResourceShareTargetBO> resourceShareTargets;
}
