package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/10/19
 */
@Setter
@Getter
public class BannerAreaCodeListRequest extends ToString {

    @ApiModelProperty("产品code")
    @NotBlank(message = "请选择位置所属产品")
    @Length(max = 32, message = "产品编码不能超过32个字符")
    private String productCode;

    @ApiModelProperty("位置code")
    @NotBlank(message = "位置编码不能为空")
    @Length(max = 32, message = "位置编码不能超过32个字符")
    private String areaCode;

    @ApiModelProperty("当前登录用户oid")
    private String accountOid;

    @ApiModelProperty("端的版本号:463")
    private Integer clientVersionCode;

    /** {@link com.timevale.saas.common.enums.SourceEnum} */
    @ApiModelProperty(value = "请求端来源", hidden = true)
    private String clientId;

}
