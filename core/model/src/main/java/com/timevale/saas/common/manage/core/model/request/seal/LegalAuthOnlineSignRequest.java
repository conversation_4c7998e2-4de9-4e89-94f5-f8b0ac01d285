package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/7/1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("发起线上法人授权签署流程请求")
public class LegalAuthOnlineSignRequest extends ToString {
    @ApiModelProperty("法定代表人账号标识")
    private String principal;

    @ApiModelProperty(value = "法定代表人证件类型")
    private String legalNoType;

    @ApiModelProperty("企业id")
    @NotBlank(message = "组织oid不能为空")
    private String orgId;

    @ApiModelProperty("发起申请的开发者appId，如果是标准签内部申请发起，则为空")
    private String developerAppId;

    @ApiModelProperty("发起申请的开发者回调地址，如果是标准签内部申请发起，则为空")
    private String developerCallbackUrl;

    @ApiModelProperty(value = "印章归属OID")
    private String sealOwnerOid;
}
