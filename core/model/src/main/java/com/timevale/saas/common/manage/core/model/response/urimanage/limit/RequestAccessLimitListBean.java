package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020-07-15
 */
@Data
@ApiModel("接口访问限制配置信息-列表")
public class RequestAccessLimitListBean extends ToString {

    @ApiModelProperty("限制记录id")
    private Long id;

    @ApiModelProperty("接口地址")
    private String uri;

    @ApiModelProperty("接口描述")
    private String desc;

    @ApiModelProperty("业务域")
    private String bizDomain;

    @ApiModelProperty("业务域描述")
    private String bizDomainDesc;

    @ApiModelProperty("访问限制列表")
    private Set<String> limits;

    @ApiModelProperty("限制状态，0-待生效， 1-生效中， 2-已废弃")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date modifyTime;

    @ApiModelProperty("负责人")
    private String owner;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("更新人")
    private String modifier;
}
