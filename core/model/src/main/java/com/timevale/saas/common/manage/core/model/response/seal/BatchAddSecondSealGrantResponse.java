package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量新增印章二次授权响应")
public class BatchAddSecondSealGrantResponse extends ToString {

    @ApiModelProperty(value = "二级授权业务id列表")
    private List<String> secSealGrantBizIds;

    @ApiModelProperty(value = "授权签署流程id")
    private String flowId;

    @ApiModelProperty(value = "签署短链")
    private String shortSignUrl;

    @ApiModelProperty(value = "签署长链")
    private String longSignUrl;

    @ApiModelProperty(value = "流程类型")
    private Integer flowType;
}
