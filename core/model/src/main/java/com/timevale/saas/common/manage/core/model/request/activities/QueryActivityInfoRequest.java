package com.timevale.saas.common.manage.core.model.request.activities;

import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021-01-30
 */
@Data
public class QueryActivityInfoRequest extends ToString {

    @ApiModelProperty("活动标识")
    private String activityCode;

    @ApiModelProperty("活动分享id, 活动标识为空时，分享id必填")
    private String activityShareId;

    public void valid() {
        if (StringUtils.isBlank(activityCode) && StringUtils.isBlank(activityShareId)) {
            throw new SaasCommonBizException(ResultEnum.PARAM_ILLEGAL, "缺少活动标识");
        }
    }
}
