package com.timevale.saas.common.manage.core.model.request.domain;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022/3/8
 */
@Setter
@Getter
public class DomainWhiteAddRequest extends ToString {

    @ApiModelProperty("域名地址比如：http://dhep.tsign.cn")
    @NotBlank
    private String url;

    @ApiModelProperty("域名添加白名单描述以及原因")
    @NotBlank
    private String remark;

}
