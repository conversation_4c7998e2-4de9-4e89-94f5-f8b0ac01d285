package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2023-06-16
 */
@Data
@ApiModel("导入访问限制配置请求参数")
public class RequestAccessLimitImportRequest extends ToString {

    @ApiModelProperty("导入地址")
    @NotBlank(message = "导入地址不能为空")
    private String importUrl;
}
