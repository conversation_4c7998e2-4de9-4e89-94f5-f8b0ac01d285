package com.timevale.saas.common.manage.core.model.request.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.annotation.EnumListValid;
import com.timevale.saas.common.manage.common.service.enums.LimitValueEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
@ApiModel("新增接口访问限制请求参数")
public class RequestAccessLimitAddRequest extends ToString {

    @NotBlank(message = "接口地址不能为空")
    @Length(max = 100, message = "接口地址最长不可超过100字符")
    @ApiModelProperty("接口地址，最长不可超过100字符")
    private String uri;

    @Length(max = 200, message = "接口描述最长不可超过200字符")
    @ApiModelProperty("接口描述，最长不可超过200字符")
    private String desc;

    @ApiModelProperty("负责人")
    private String owner;

    @ApiModelProperty("业务域")
    private String bizDomain;

    @NotEmpty(message = "限制列表不能为空")
    @ApiModelProperty("限制列表, NEED_LOGIN:需登录,SKIP_LOGIN:无需登录,WHITE_LIST:白名单,BLACK_LIST:非白名单,MEMBER_CHECK:需成员校验,MEMBER_SKIP_CHECK:无需成员校验")
    @EnumListValid(message = "存在无法识别的限制", target = LimitValueEnum.class, srcField = "value")
    private Set<String> limits;
}
