package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-04-13 19:02
 */
@Data
@ApiModel("生效订单")
@NoArgsConstructor
public class EffectiveOrderResponse {
    @ApiModelProperty("主键")
    private String id;

    @ApiModelProperty("用户id")
    private String gid;

    @ApiModelProperty("对外订单号")
    private String showId;

    @ApiModelProperty("存量id")
    private Long poolId;

    @ApiModelProperty("数量是否无限")
    private boolean unlimitNum;

    @ApiModelProperty("购买量")
    private BigDecimal totalNum;

    @ApiModelProperty("售卖方案编号")
    private Long saleSchemaId;

    @ApiModelProperty("产品编号")
    private Long productId;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("到期时间")
    private Date endTime;

    @ApiModelProperty("剩余量")
    private BigDecimal margin;

    @ApiModelProperty("产品类型 1-套餐 2-充值")
    private Integer saleType;

    @ApiModelProperty("计量单位")
    private String units;

    @ApiModelProperty("0:通用， 1：开发者， 2：项目")
    private Integer useScope;

    @ApiModelProperty("售卖方案名称")
    private String saleSchemaName;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("商品id")
    private Long commodityId;

    @ApiModelProperty("商品名称")
    private String commodityName;

    @ApiModelProperty("商品编号")
    private String commodityNo;

    @ApiModelProperty("售卖信息")
    private Long saleInfoId;

    @ApiModelProperty("售卖规则名称")
    private String saleInfoName;

    @ApiModelProperty("使用量(数量无限订单用,普通订单直接看剩余量)")
    private BigDecimal used;

    @ApiModelProperty("套餐是否即将过期：0.未到期 1. 将到期 2. 已到期")
    private Integer packageExpiring;

    @ApiModelProperty("套餐不足比例，eg：30、15、5，不提醒为null")
    private BigDecimal packageWarnPct;
}
