package com.timevale.saas.common.manage.core.model.model.activity;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel("活动通知配置")
@NoArgsConstructor
public class NoticeConfig extends ToString {

    @ApiModelProperty("是否发送通知")
    private Boolean notice;

    @ApiModelProperty("通知内容")
    private String noticeContent;

    @ApiModelProperty("通知链接")
    private String noticeUrl;

}
