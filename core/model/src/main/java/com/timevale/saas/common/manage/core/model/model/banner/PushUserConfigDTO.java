package com.timevale.saas.common.manage.core.model.model.banner;

import com.timevale.saas.common.manage.common.service.constant.banner.OperateConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * @date 2022/3/18
 */
@Data
public class PushUserConfigDTO {

    @ApiModelProperty("是否满足推送 true 满足添加时推送 false 不满足添加时推送")
    private Boolean hasSatisfy = true;

    @ApiModelProperty("配置类型 1指定用户 2功能灰度 3指定会员版本")
    private Integer type;

    @ApiModelProperty("对应类型配置")
    private Map<String, String> configMap = new HashMap<>();

    @ApiModelProperty("条件组合方式  0:and 1:or")
    private Integer conditionType = OperateConstant.AND;
}
