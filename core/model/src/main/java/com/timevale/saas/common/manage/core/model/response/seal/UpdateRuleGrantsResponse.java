package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/4
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("批量编辑印章授权响应")
public class UpdateRuleGrantsResponse extends FirstGrantBaseResponse {

    @ApiModelProperty(value = "授权成功数量")
    private Integer successCount;

    @ApiModelProperty(value = "授权失败数量")
    private Integer errorCount;
}
