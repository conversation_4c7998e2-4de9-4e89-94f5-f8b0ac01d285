package com.timevale.saas.common.manage.core.model.request.vip;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.request.vip.bean.BuyerExpireInfo;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: huifeng
 * @since: 2021-03-24 11:11
 **/
@Data
public class DelayVipExpireDateRequest extends ToString {

    @NotEmpty
    private List<BuyerExpireInfo> infoList;

    @NotNull
    private List<String> blackListGids;

    @NotNull
    private List<String> whiteListGids;

    /**
     * 如果skipWhiteList为true，对infoList剔除blackListGids后的所有gid操作延期，否则只处理白名单里的
     */
    @NotNull
    private Boolean skipWhiteList;
}
