package com.timevale.saas.common.manage.core.model.response.activities;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022-05-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParticipateInActivityResponse extends ToString {

    @ApiModelProperty("参与主体oid")
    private String subjectId;

    @ApiModelProperty("参与主体是否企业")
    private Boolean subjectOrgan;

    @ApiModelProperty("参与主体是否已实名")
    private Boolean subjectRealNamed;

    @ApiModelProperty("活动赠送份数")
    private Integer presentNum;
}
