package com.timevale.saas.common.manage.core.model.enums;

import lombok.Getter;

/**
 * Created by tianlei on 2022/2/17
 */
public enum AuthRelationEffectiveUnitEnum {

    YEAR(1, "年"),
    ;

    @Getter
    private Integer code;
    private String desc;


    AuthRelationEffectiveUnitEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AuthRelationEffectiveUnitEnum from(Integer code) {
        if (null == code) {
            return null;
        }

        for (AuthRelationEffectiveUnitEnum statusEnum : AuthRelationEffectiveUnitEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }

        return null;
    }

}
