package com.timevale.saas.common.manage.core.model.model.banner;

import com.timevale.mandarin.common.result.ToString;

/**
 * banner产品端位置容量信息
 * <AUTHOR>
 * @since 2021-06-01 14:35:00
 */
public class BannerAreaCapacityInfoDTO extends ToString {

    /**
    * 产品编码
    */
    private String productCode;

    /**
    * 地区编码
    */
    private String areaCode;

    /**
     * 当前容量
     */
    private Integer capacity;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Integer getCapacity() {
        return capacity;
    }

    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }
}