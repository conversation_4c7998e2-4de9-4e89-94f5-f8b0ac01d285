package com.timevale.saas.common.manage.core.model.response.share;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 创建资源分享响应结果
 *
 * <AUTHOR>
 * @since 2020/12/04
 */
@ApiModel(value = "获取资源分享基本信息")
@Getter
@Setter
public class GetResourceShareInfoResponse extends BaseResourceShareInfoResponse {

    @ApiModelProperty(value = "分享地址")
    private String shareUrl;

    @ApiModelProperty(value = "资源分享二维码图片地址")
    private String qrCode;

    @ApiModelProperty(value = "是否可以修改配置")
    private boolean canChangeConfig;
}
