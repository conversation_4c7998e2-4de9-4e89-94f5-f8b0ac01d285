package com.timevale.saas.common.manage.core.model.bo.vip;

import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.saas.common.manage.common.dal.dataobject.SaasFunctionIllustrateDO;
import com.timevale.saas.common.manage.common.service.enums.IllustratePopModeEnum;
import com.timevale.saas.common.manage.common.service.enums.IllustrateSupportTrialEnum;
import com.timevale.saas.common.manage.core.model.enums.IllustrateShowModeEnum;
import com.timevale.saas.common.manage.core.model.model.illustrate.FunctionIllustrateExtDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO 版本变更的展示信息业务对象
 *
 * <AUTHOR>
 * @since 2022/11/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VipFunctionChangeIllustrateBO {

    /** 功能值 */
    private String functionCode;
    /** 功能介绍展示模式，1 图片 2 视频 */
    private IllustrateShowModeEnum illustrateShowMode;

    /** 功能介绍图片或者视频的文件fileKey */
    private String illustrateShowFile;

    /** 功能介绍图片或者视频的文件fileName */
    private String illustrateShowFileName;

    /** 功能介绍标题 */
    private String illustrateDocTitle;

    /** 功能介绍文档 */
    private String illustrateDocContent;

    /** 对内介绍文档地址 */
    private String illustrateInnerDocUrl;

    /** 对外介绍文档地址 */
    private String illustrateOutDocUrl;

    /** 对外申请文案内容 */
    private String illustrateApplyDoc;

    /** 对外展示模式 */
    private IllustratePopModeEnum illustratePopMode;
    
    /** 是否支持试用 */
    private boolean supportTrial;

    public SaasFunctionIllustrateDO convert2SaasFunctionIllustrateDO() {
        SaasFunctionIllustrateDO illustrateDO = new SaasFunctionIllustrateDO();
        illustrateDO.setFuncCode(functionCode);
        illustrateDO.setDocTitle(illustrateDocTitle);
        illustrateDO.setDocContent(illustrateDocContent);
        illustrateDO.setShowFile(illustrateShowFile);
        illustrateDO.setShowMode(illustrateShowMode == null ? null : illustrateShowMode.getCode());
        illustrateDO.setInnerWikiUrl(illustrateInnerDocUrl);
        illustrateDO.setOutWikiUrl(illustrateOutDocUrl);
        illustrateDO.setSupportTrial(supportTrial ? IllustrateSupportTrialEnum.SUPPORT.getCode() : IllustrateSupportTrialEnum.NO_SUPPORT.getCode());
        FunctionIllustrateExtDTO extDTO = new FunctionIllustrateExtDTO();
        extDTO.setShowFileName(illustrateShowFileName);
        extDTO.setApplyDoc(illustrateApplyDoc);
        extDTO.setPopMode(illustratePopMode == null ? null : illustratePopMode.getCode());
        String extJson = JsonUtils.obj2json(extDTO);
        illustrateDO.setExt(extJson);
        return illustrateDO;
    }
}
