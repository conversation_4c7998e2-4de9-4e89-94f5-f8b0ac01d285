package com.timevale.saas.common.manage.core.model.request.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2021/11/3
 **/
@ApiModel("弹窗启用/禁用入参")
@Data
public class PopupEnableRequest extends PopupDeleteRequest{

    @ApiModelProperty("启用/禁用状态 0禁用 1启用")
    @NotNull(message = "请选择启用状态")
    @Min(value = 0, message = "未知状态")
    @Max(value = 1,message = "未知状态")
    private Integer status;

}
