package com.timevale.saas.common.manage.core.model.request.crm;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.model.roleandprivilege.SaasPrivilegeConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("运营支撑设置saas所有权限请求")
public class CRMSetSaasPrivilegesConfigRequest extends ToString {
    @ApiModelProperty("权限配置信息")
    @Size(min = 1)
    private List<SaasPrivilegeConfigDTO> privilegeConfigList;

    @ApiModelProperty("运营支撑的操作人姓名")
    private String operatorName;
}
