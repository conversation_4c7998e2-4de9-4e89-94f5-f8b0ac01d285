package com.timevale.saas.common.manage.core.model.request.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022/7/15
 */
@Setter
@Getter
@ApiModel(description = "二次授权更新请求")
public class SecondSealGrantUpdateRequest extends UrlBaseRequest {
    @ApiModelProperty(value = "二级授权业务id")
    @NotBlank(message = "二级授权业务id不能为空")
    private String secondSealGrantBizId;

    @ApiModelProperty(value = "生效时间")
    @NotNull(message = "生效时间不能为空")
    @Min(value = 1, message = "生效时间必须是正数")
    private Long effectiveTime;

    @ApiModelProperty(value = "失效时间")
    @NotNull(message = "失效时间不能为空")
    @Min(value = 1, message = "失效时间必须是正数")
    private Long expireTime;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

    @ApiModelProperty(name = "sealOwnerAdminOid", value = "印章归属企业的管理员OID")
    private String sealOwnerAdminOid;
}
