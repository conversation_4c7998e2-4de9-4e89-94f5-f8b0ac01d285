package com.timevale.saas.common.manage.core.model.response.bean;

import com.timevale.saas.multilingual.translate.annotation.NeedTranslateField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
@Data
@ApiModel("主任务信息")
public class TaskBean {
    @ApiModelProperty("任务id")
    private String taskId;
    @ApiModelProperty("任务名称")
    private String taskName;
    @ApiModelProperty("任务类型，1-合同批量发起，2-模板批量发起，3-合同批量下载，4-合同批量导出")
    private Integer taskType;
    @ApiModelProperty("任务类型显示文案， 前端可根据当前字段渲染")
    @NeedTranslateField
    private String taskTypeLabel;
    @ApiModelProperty("任务状态，1-进行中， 2-完成, 3-部分失败，4-全部失败")
    private Integer taskStatus;
    @ApiModelProperty("完成任务数")
    private long doneTasks;
    @ApiModelProperty("总任务数")
    private long totalTasks;
    @ApiModelProperty("是否已导出失败任务列表")
    private boolean failTaskExported;
    @ApiModelProperty("是否支持失败导出")
    private boolean supportFailTaskExport;
    @ApiModelProperty("任务发起主体id")
    private String taskBizOwner;
    @ApiModelProperty("任务创建者oid")
    private String taskCreatOr;
    @ApiModelProperty("任务创建时间")
    private Date createTime;
    @ApiModelProperty("任务结束时间")
    private Date finishTime;
    @ApiModelProperty("失败原因")
    private String reason;
}
