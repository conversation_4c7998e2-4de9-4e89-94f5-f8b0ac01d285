package com.timevale.saas.common.manage.core.model.response.urimanage.forward;

import com.timevale.saas.common.manage.common.service.model.output.bean.DefenderBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2020-10-21
 */
@Data
@ApiModel("接口转发及参数替换规则详情信息")
public class RequestForwardDetailResponse extends RequestForwardBean {

    @ApiModelProperty("是否需登录")
    private Boolean needLogin;

    @ApiModelProperty("是否白名单")
    private Boolean whitelist;

    @ApiModelProperty("参数替换规则")
    private DefenderBean defender;
}
