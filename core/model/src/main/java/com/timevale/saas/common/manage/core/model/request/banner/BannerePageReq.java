package com.timevale.saas.common.manage.core.model.request.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * banner管理列表
 * <AUTHOR>
 * @since 2021-05-31 11:45
 **/
@ApiModel("banner管理列表分页请求信息")
public class BannerePageReq extends ToString {

    @ApiModelProperty("产品编码")
    private String productCode;


    @ApiModelProperty("位置编码")
    private String areaCode;

    @ApiModelProperty("状态，1上架，2下架")
    private Integer status;

    @ApiModelProperty("banner名称")
    private String bannerName;

    @ApiModelProperty(value = "页码", required = true)
    private Integer pageNum;

    @ApiModelProperty(value = "单页数量，默认20", required = true)
    private Integer pageSize;

    /**
     * @see com.timevale.saas.common.manage.common.service.constant.banner.BannerOrderFieldConstant
     */
    @ApiModelProperty("排序字段")
    private Integer orderField;

    @ApiModelProperty("是否降序")
    private Boolean descFlag;


    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getBannerName() {
        return bannerName;
    }

    public void setBannerName(String bannerName) {
        this.bannerName = bannerName;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getOrderField() {
        return orderField;
    }

    public void setOrderField(Integer orderField) {
        this.orderField = orderField;
    }

    public Boolean getDescFlag() {
        return descFlag;
    }

    public void setDescFlag(Boolean descFlag) {
        this.descFlag = descFlag;
    }
}
