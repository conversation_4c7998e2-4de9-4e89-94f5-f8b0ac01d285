package com.timevale.saas.common.manage.core.model.model.vip;

import lombok.Data;

import java.util.Map;

/**
 * 更新会员增配
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
@Data
public class UpdateVipAdditionDTO {

    /** 当前操作人花名,用于权限校验 */
    private String alias;

    /** 配置信息 */
    private Map<String,Object> funcLimit;

    /** 生效时间 */
    private Long effectiveFrom;

    /** 失效时间 */
    private Long effectiveTo;

    /** 功能标识，用于更新条件 */
    private String funcCode;

    /** 账号gid，用于更新条件 */
    private String accountGid;

    /** 状态 */
    private Integer status;
    
}
