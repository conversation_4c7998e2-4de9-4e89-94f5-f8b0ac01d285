package com.timevale.saas.common.manage.core.model.request.urimanage.forward;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-12-02
 */
@Data
@ApiModel("批量更新saas接口转发配置状态请求参数")
public class BatchRequestForwardUpdateStatusRequest extends RequestForwardUpdateStatusRequest {

    @ApiModelProperty("转发配置规则id列表")
    @NotEmpty(message = "转发配置规则id列表不能为空")
    @Size(max = 100, message = "转发配置规则id列表单次不能超过100个")
    private List<Long> ids;
}
