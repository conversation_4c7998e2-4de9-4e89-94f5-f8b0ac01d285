package com.timevale.saas.common.manage.core.model.request.banner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 *
 * @date 2021/11/2
 */
@Data
@ApiModel("弹窗分页查询入参")
public class PopupPageRequest {

    @ApiModelProperty("产品code")
    private String productCode;

    @ApiModelProperty("位置Code")
    private String areaCode;

    @ApiModelProperty("弹窗名称搜索字段 支持模糊")
    private String name;

    @ApiModelProperty("是否启用 0禁用 1启用")
    private Integer status;

    @ApiModelProperty(value = "页码", example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageNum;

    @ApiModelProperty(value = "单页数据量", example = "20")
    @NotNull(message = "每页数量不能为空")
    @Min(value = 1, message = "pageSize不能小于1")
    @Max(value = 100, message = "pageSize不能大于100")
    private Integer pageSize;
}
