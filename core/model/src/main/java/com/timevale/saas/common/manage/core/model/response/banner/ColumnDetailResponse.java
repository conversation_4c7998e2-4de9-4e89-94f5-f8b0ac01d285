package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.saas.common.manage.core.model.model.banner.ColumnInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 *
 * @date 2021/11/19
 */
@Data
@ApiModel("栏目详情返回结构")
public class ColumnDetailResponse extends ColumnInfoDTO {

    @ApiModelProperty("栏目内容名称")
    private String name;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("位置名称")
    private String areaName;

    @ApiModelProperty("产品唯一标识")
    private String productCode;

    @ApiModelProperty("地址唯一标识")
    private String areaCode;

    @ApiModelProperty("上架状态 0下架 1上架")
    private Integer status;

    @ApiModelProperty("权重")
    private Integer weight;

    @ApiModelProperty("点击数量")
    private Integer clickNum;
}
