package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.enums.NoticeTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-31 16:50
 **/
@ApiModel("Banner信息")
public class BannerInfo extends ToString {

    @ApiModelProperty("banner唯一标识")
    private Long bannerId;

    @ApiModelProperty("banner名称")
    private String bannerName;

    @ApiModelProperty("产品端名称")
    private String productName;

    @ApiModelProperty("位置名称")
    private String areaName;

    @ApiModelProperty("图片Url")
    private String picUrl;

    @ApiModelProperty("图片链接")
    private String linkUrl;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("修改时间")
    private Long updateTime;

    /**
     * @see com.timevale.saas.common.manage.common.service.constant.banner.BannerStatusConstant
     */
    @ApiModelProperty("banner状态，1上架，2下架")
    private Integer status;

    @ApiModelProperty("点击次数")
    private Integer clickTimes;

    @ApiModelProperty("权重")
    private Integer orderNo;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("有效用户类型 0全部 1指定 2功能灰度 3 会员版本")
    private List<Integer> validityUserType;

    /**
     * @see NoticeTypeEnum 公告类型枚举
     */
    @ApiModelProperty("公告类型:1.产品迭代更新、2.新功能上线、3.交互体验升级、4.系统问题通知、5.其他")
    private Integer noticeType;

    @ApiModelProperty("支持的vip版本label")
    private List<String> notifyVersionLabels;

    @ApiModelProperty("支持的角色label")
    private List<String> notifyRoleLabels;

    @ApiModelProperty("引导文案")
    private String guideText;

    @ApiModelProperty("有效开始时间")
    private Date validityStartTime;

    @ApiModelProperty("有效结束时间")
    private Date validityEndTime;

    @ApiModelProperty("客户端版本号")
    private Integer clientVersionCode;

    /** {@link ConditionTypeEnum} */
    @ApiModelProperty("版本号显示条件: lt:小于，le:小于等于，eq:等于，ge：大于等于，gt:大于")
    private String conditionType;

    public Integer getClientVersionCode() {
        return clientVersionCode;
    }

    public void setClientVersionCode(Integer clientVersionCode) {
        this.clientVersionCode = clientVersionCode;
    }

    public String getConditionType() {
        return conditionType;
    }

    public void setConditionType(String conditionType) {
        this.conditionType = conditionType;
    }

    public Date getValidityStartTime() {
        return validityStartTime;
    }

    public void setValidityStartTime(Date validityStartTime) {
        this.validityStartTime = validityStartTime;
    }

    public Date getValidityEndTime() {
        return validityEndTime;
    }

    public void setValidityEndTime(Date validityEndTime) {
        this.validityEndTime = validityEndTime;
    }

    public String getGuideText() {
        return guideText;
    }

    public void setGuideText(String guideText) {
        this.guideText = guideText;
    }

    public Integer getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(Integer noticeType) {
        this.noticeType = noticeType;
    }

    public List<String> getNotifyVersionLabels() {
        return notifyVersionLabels;
    }

    public void setNotifyVersionLabels(List<String> notifyVersionLabels) {
        this.notifyVersionLabels = notifyVersionLabels;
    }
    public List<String> getNotifyRoleLabels() {
        return notifyRoleLabels;
    }

    public void setNotifyRoleLabels(List<String> notifyRoleLabels) {
        this.notifyRoleLabels = notifyRoleLabels;
    }
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getBannerId() {
        return bannerId;
    }

    public void setBannerId(Long bannerId) {
        this.bannerId = bannerId;
    }

    public String getBannerName() {
        return bannerName;
    }

    public void setBannerName(String bannerName) {
        this.bannerName = bannerName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getLinkUrl() {
        return linkUrl;
    }

    public void setLinkUrl(String linkUrl) {
        this.linkUrl = linkUrl;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getClickTimes() {
        return clickTimes;
    }

    public void setClickTimes(Integer clickTimes) {
        this.clickTimes = clickTimes;
    }

    public List<Integer> getValidityUserType() {
        return validityUserType;
    }

    public void setValidityUserType(List<Integer> validityUserType) {
        this.validityUserType = validityUserType;
    }

    public Integer getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(Integer orderNo) {
        this.orderNo = orderNo;
    }
}
