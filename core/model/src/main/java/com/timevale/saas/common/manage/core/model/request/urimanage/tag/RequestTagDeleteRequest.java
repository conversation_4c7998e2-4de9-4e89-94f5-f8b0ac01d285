package com.timevale.saas.common.manage.core.model.request.urimanage.tag;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2021--22
 */
@Data
@ApiModel("删除接口标签请求参数")
public class RequestTagDeleteRequest extends ToString {

    @NotNull(message = "标签类型不能为空，1-接口标签，2-业务域标签")
    @ApiModelProperty("标签类型，1-接口标签，2-业务域标签")
    private Integer type;
}
