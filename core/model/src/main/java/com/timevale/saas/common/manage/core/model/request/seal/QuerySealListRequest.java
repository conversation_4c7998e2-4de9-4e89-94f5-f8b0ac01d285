package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查询印章列表
 *
 * <AUTHOR>
 * @since 2022/12/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取印章列表请求")
public class QuerySealListRequest extends ToString {

    /**
     * 印章业务类型 参考 footstone-seal服务的 ealBizType
     */
    @ApiModelProperty(value = "印章业务类型")
    @NotNull(message = "印章类型不能为空")
    private List<String> sealBizTypes;

    @ApiModelProperty(value = "页码", required = true)
    @NotNull(message = "pageNo不能为空")
    @Min(value = 1, message = "pageNo最小值为1")
    private Integer pageNo;

    @ApiModelProperty(value = "每页显示的数量", required = true)
    @NotNull(message = "pageSize不能为空")
    @Min(value = 1, message = "pageSize最小值为1")
    @Max(value = 20, message = "pageSize最大值为20")
    private Integer pageSize;

    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;

}
