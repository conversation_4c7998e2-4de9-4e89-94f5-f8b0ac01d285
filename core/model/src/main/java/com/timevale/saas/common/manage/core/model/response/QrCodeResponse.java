package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 *
 * @date 2022/6/8
 */
@Data
@ApiModel("生成二维码返回结果")
@NoArgsConstructor
@AllArgsConstructor
public class QrCodeResponse {

    @ApiModelProperty("生成返回链接 失败返回空")
    private String url;
}
