package com.timevale.saas.common.manage.core.model.model;

import com.timevale.saas.common.manage.common.service.enums.SubjectFlagEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/2/13 主体及标识信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SubjectFlagDTO {
    /** 主体及角色信息 */
    private SubjectAndRolesDTO subjectAndRoles;
    /** 标识信息 */
    private List<SubjectFlagEnum> flags;
}
