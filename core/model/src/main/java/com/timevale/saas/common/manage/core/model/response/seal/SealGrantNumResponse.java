package com.timevale.saas.common.manage.core.model.response.seal;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 印章授权数量
 *
 * <AUTHOR>
 * @since 2022-09-20 16:13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SealGrantNumResponse extends ToString {

    /** 印章已授权数量 */
    private Integer sealGrantedNum;
}
