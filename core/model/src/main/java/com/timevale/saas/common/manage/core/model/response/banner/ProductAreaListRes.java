package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-05-31 16:27
 **/
@ApiModel("产品端位置列表信息")
public class ProductAreaListRes extends ToString {

    @ApiModelProperty("banner产品端位置列表信息")
    private List<BannerProductAreaInfo> list;

    public List<BannerProductAreaInfo> getList() {
        return list;
    }

    public void setList(List<BannerProductAreaInfo> list) {
        this.list = list;
    }
}
