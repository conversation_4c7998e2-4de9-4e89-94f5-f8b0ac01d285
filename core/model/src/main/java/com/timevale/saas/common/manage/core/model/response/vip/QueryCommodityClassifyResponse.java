package com.timevale.saas.common.manage.core.model.response.vip;

import com.google.common.collect.Lists;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipCommodity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */

@Data
@ApiModel("获取指定分类的商品列表响应数据")
@NoArgsConstructor
@AllArgsConstructor
public class QueryCommodityClassifyResponse {
    @ApiModelProperty("会员商品列表")
    private List<VipCommodity> vipCommodities = Lists.newArrayList();

    @ApiModelProperty("签署份数商品列表")
    private List<String> signCommodities = Lists.newArrayList();
}
