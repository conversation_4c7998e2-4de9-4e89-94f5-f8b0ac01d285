package com.timevale.saas.common.manage.core.model.response.contractNo;

import com.timevale.mandarin.common.result.ToString;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * ContractNoPrefixCheckResponse
 *
 * <AUTHOR>
 * @since 2022/11/10 9:59 上午
 */
@Data
@AllArgsConstructor
public class ContractNoPrefixCheckResponse extends ToString {

    /**
     * 前缀是否通过
     */
    private Boolean pass;

    /**
     * 不通过描述
     */
    private String message;
}
