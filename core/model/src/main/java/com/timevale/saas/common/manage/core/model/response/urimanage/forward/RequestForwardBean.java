package com.timevale.saas.common.manage.core.model.response.urimanage.forward;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2020-10-21
 */
@Data
public class RequestForwardBean extends ToString {

    @ApiModelProperty("转发及参数替换规则记录id")
    private Long id;

    @ApiModelProperty("接口说明")
    private String desc;

    @ApiModelProperty("接口地址")
    private String uri;

    @ApiModelProperty("接口对应的method类型，GET/POST/PUT/DELETE，若为空，表示不限制")
    private String method;

    @ApiModelProperty("转发类型, 0-无需转发，1-接口转发，2-webserver转发")
    private Integer forwardType;

    @ApiModelProperty("接口转发地址")
    private String forwardUri;

    @ApiModelProperty("接口转发地址对应的method类型，GET/POST/PUT/DELETE")
    private String forwardMethod;

    @ApiModelProperty("接口标签， 多个以逗号分隔")
    private String tags;

    @ApiModelProperty("接口标签名称， 多个以逗号分隔")
    private String tagNames;

    @ApiModelProperty("规则配置状态，true-启用，false-停用")
    private boolean valid;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date modifyTime;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("更新人")
    private String modifier;
}
