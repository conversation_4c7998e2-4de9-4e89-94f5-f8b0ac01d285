package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020-07-15
 */
@Data
@ApiModel("接口访问限制配置信息")
public class RequestAccessLimitBean extends ToString {

    @ApiModelProperty("限制记录id")
    private Long id;

    @ApiModelProperty("接口地址")
    private String uri;

    @ApiModelProperty("业务域")
    private String bizDomain;

    @ApiModelProperty("业务域描述")
    private String bizDomainDesc;

    @ApiModelProperty("限制状态，0-待生效， 1-生效中， 2-已废弃")
    private Integer status;

    @ApiModelProperty("限制列表, NEED_LOGIN:需登录,SKIP_LOGIN:无需登录,WHITE_LIST:白名单,BLACK_LIST:非白名单,MEMBER_CHECK:需成员校验,MEMBER_SKIP_CHECK:无需成员校验")
    private Set<String> limits;
}
