package com.timevale.saas.common.manage.core.model.response.activities;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021-01-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ParticipateActivityResponse extends ToString {

    @ApiModelProperty("企业oid")
    private String orgId;

    @ApiModelProperty("企业是否已实名")
    private Boolean orgRealNamed;

    @ApiModelProperty("活动赠送份数")
    private Integer presentNum;
}
