package com.timevale.saas.common.manage.core.model.request.seal;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 * @since 2022/7/15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取二次授权请求")
public class GetSecondRuleGrantListRequest extends ToString {

    @ApiModelProperty(name = "sealGrantBizId", value = "一级授权业务id", required = true)
    @NotBlank(message = "一级授权业务id不能为空")
    private String sealGrantBizId;

    @ApiModelProperty(name = "pageNum", value = "页码", required = true)
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum;

    @ApiModelProperty(name = "pageSize", value = "每页显示数量", required = true)
    @NotNull(message = "pageSize不能为空")
    @Min(value = 1, message = "页码最小为1")
    @Max(value = 100, message = "页码最大为100")
    private Integer pageSize;
    @ApiModelProperty(name = "sealOwnerOid", value = "印章归属ID")
    private String sealOwnerOid;
}
