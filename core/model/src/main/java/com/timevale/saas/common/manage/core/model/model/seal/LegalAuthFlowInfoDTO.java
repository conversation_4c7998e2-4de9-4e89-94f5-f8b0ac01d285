package com.timevale.saas.common.manage.core.model.model.seal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/6
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("授权流程信息")
public class LegalAuthFlowInfoDTO {
	@ApiModelProperty(value = "授权签署流程id")
	private String flowId;

	@ApiModelProperty(value = "授权签署文档filekey")
	private String docFilekey;

	@ApiModelProperty(value = "授权签署文档下载地址")
	private String docUrl;

	@ApiModelProperty(value = "授权失败原因")
	private String reason;
}
