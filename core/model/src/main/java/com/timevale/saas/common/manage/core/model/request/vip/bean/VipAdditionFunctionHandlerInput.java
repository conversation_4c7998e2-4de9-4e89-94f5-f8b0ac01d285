package com.timevale.saas.common.manage.core.model.request.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.dal.dataobject.SaasAdditionFunctionDO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 2024-10-13
 */
@Data
public class VipAdditionFunctionHandlerInput extends ToString {
    /** 授权企业 gid */
    private String authTenantGid;

    /** 主企业 gid */
    private String parentTenantGid;

    /** 关联企业 gid */
    private String childTenantGid;
    
    /** 企业增配功能(key：企业gid,value：增配功能) */
    private Map<String, List<SaasAdditionFunctionDO>> companyFunctionMap;

    /** 指定的增配续费内容 */
    private List<String> funcCodesFromRenew;

    /**
     * 有效期开始时间
     */
    private Date effectiveStartTime;

    /**
     * 有效期结束时间
     */
    private Date effectiveEndTime;
            
}
