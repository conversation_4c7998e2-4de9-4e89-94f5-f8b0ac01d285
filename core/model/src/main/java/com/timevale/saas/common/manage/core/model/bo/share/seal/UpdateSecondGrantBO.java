package com.timevale.saas.common.manage.core.model.bo.share.seal;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/7/17
 * 跟新二次授权
 */
@Data
public class UpdateSecondGrantBO {

	/**
	 * 更新二次授权结果
	 */
	@Data
	public static class UpdateSecondGrantResultBO{

		private String secSealGrantBizId;
		private String flowId;
		private Integer flowType;
		private String shortSignUrl;
		private String longSignUrl;

	}
}
