package com.timevale.saas.common.manage.core.model.response.urimanage.limit;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020-10-22
 */
@Data
@ApiModel("根据id获取访问限制配置详情返回数据")
public class RequestAccessLimitDetailResponse extends RequestAccessLimitListBean {

    @ApiModelProperty("限制列表, NEED_LOGIN:需登录,SKIP_LOGIN:无需登录,WHITE_LIST:白名单,BLACK_LIST:非白名单,MEMBER_CHECK:需成员校验,MEMBER_SKIP_CHECK:无需成员校验")
    private Set<String> limits;
}
