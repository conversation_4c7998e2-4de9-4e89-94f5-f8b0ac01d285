package com.timevale.saas.common.manage.core.model.response.vip;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipLevelFunctionInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-12 09:15
 */
@Data
@ApiModel(value = "获取会员功能列表响应数据")
public class QueryVipFunctionListResponse extends ToString {

    @ApiModelProperty("总数")
    private long total;

    @ApiModelProperty("会员功能列表")
    private List<VipLevelFunctionInfo> functions;
}
