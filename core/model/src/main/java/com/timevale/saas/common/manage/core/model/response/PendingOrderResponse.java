package com.timevale.saas.common.manage.core.model.response;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-04-06 15:31
 */
@Data
@ApiModel("支付中订单")
@NoArgsConstructor
public class PendingOrderResponse {

    /** 订单id */
    private String id;

    /** 对外订单号 */
    private String showId;

    /** 下单时间 */
    private Date createTime;

    /** 支付截止时间 */
    private Date payDeadLine;
}
