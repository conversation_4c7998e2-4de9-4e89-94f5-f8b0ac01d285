package com.timevale.saas.common.manage.core.model.model.illustrate;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-12-22 15:43
 */
@Data
public class FunctionIllustrateInfoDTO extends BaseFunctionIllustrateDTO {


    /**满足该功能的最低版本*/
    private Integer satisfyMixVersion;

    /**满足该功能的最低版本标识*/
    private String satisfyMinVipCode;

    /** 是否使用版本标识 */
    private boolean useVipCode;


    /** 申请文案内容 */
    private String applyDoc;

    /** 展示模式
     * @see com.timevale.saas.common.manage.common.service.enums.IllustratePopModeEnum */
    private Integer popMode;

    /** 满足该功能的最低版本会员等级名称 */
    private String satisfyMinVipName;
}
