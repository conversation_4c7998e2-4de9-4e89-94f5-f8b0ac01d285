package com.timevale.saas.common.manage.core.model.request;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @since 2022-04-13 19:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "有效订单查询参数")
public class EffectiveOrderRequest extends ToString {
    @NotBlank
    @ApiModelProperty("oid")
    private String oid;

    @ApiModelProperty("产品id")
    private Long productId;
}
