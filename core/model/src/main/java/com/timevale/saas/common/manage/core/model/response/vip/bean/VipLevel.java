package com.timevale.saas.common.manage.core.model.response.vip.bean;

import com.timevale.mandarin.common.result.ToString;
import com.timevale.saas.common.manage.common.service.enums.VipCodeEnum;
import com.timevale.saas.common.manage.common.service.enums.VipLevelEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会员等级
 *
 * <AUTHOR>
 * @since 2023-02-13
 */
@Data
@NoArgsConstructor
public class VipLevel extends ToString {

    /** 会员等级 */
    private Integer vipLevel;

    /** 会员等级标识 */
    private String vipCode;

    /** 是否使用会员等级标识 */
    private boolean useCode;
    
    /** 会员等级名称 */
    private String vipName;

    /**
     * 老版本构造函数
     * @param vipLevel
     */
    public VipLevel(Integer vipLevel) {
        this.vipLevel = vipLevel;
        this.vipCode = VipLevelEnum.getVipLevelByValue(vipLevel).getCode();
        this.useCode = false;
    }

    /**
     * 新版本构造函数
     * @param code
     */
    public VipLevel(String code) {
        this.vipCode = code;
        this.vipLevel = VipCodeEnum.parseVipCode(code).getLevel();
        this.useCode = true;
    }

    /**
     * 判断是否基础版
     *
     * @return
     */
    public boolean isBase() {
        return VipLevelEnum.isBase(vipLevel) || VipCodeEnum.BASE.getCode().equals(vipCode);
    }
}
