package com.timevale.saas.common.manage.core.model.request.auth.code;

import com.timevale.mandarin.common.result.ToString;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 发送验证码入参
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Data
public class SendAuthCodeRequest extends ToString {

    /**
     * 业务标识
     */
    @NotBlank
    private String bizTag;

    /**
     * 手机号
     */
    @NotBlank
    @Pattern(regexp = "^[0-9]{11}$", message = "请输入正确的手机号")
    private String phone;
}
