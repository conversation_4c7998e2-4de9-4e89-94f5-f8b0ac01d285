package com.timevale.saas.common.manage.core.model.response.banner;

import com.timevale.mandarin.common.result.ToString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2021/11/2
 */
@ApiModel("弹窗分页查询返回结果")
@Data
public class PopupPageResponse extends ToString {

    @ApiModelProperty("总数")
    private Integer total;

    @ApiModelProperty("弹窗列表")
    private List<PopupPageInfo> data;
}
