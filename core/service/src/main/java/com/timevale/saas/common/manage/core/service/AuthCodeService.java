package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.model.UserAccount;

/**
 * 验证码功能
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public interface AuthCodeService {

    /**
     * 发送手机验证码
     * @param phone
     * @param bizTag
     * @return
     */
    String sendPhoneAuthCode(String phone, String bizTag, UserAccount userAccount);

    /**
     * 校验验证码
     * @param authCodeId
     * @return
     */
    boolean authCode(String authCodeId, String authCode, UserAccount userAccount);

    /**
     * 校验手机号是否验证通过
     * @param phone
     * @param bizTag
     * @return
     */
    boolean checkAuthOk(String phone, String bizTag, String userOid);
}
