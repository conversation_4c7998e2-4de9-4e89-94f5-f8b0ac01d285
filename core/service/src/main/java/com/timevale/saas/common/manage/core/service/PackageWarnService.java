package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.response.EffectiveOrderAggreResponse;
import com.timevale.saas.common.manage.core.model.response.EffectiveOrderResponse;
import com.timevale.saas.common.manage.core.model.response.PackageWarnResponse;
import com.timevale.saas.common.manage.core.model.response.PendingOrderResponse;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 套餐提醒服务
 *
 * <AUTHOR>
 * @since 2022-04-01 17:58
 */
public interface PackageWarnService {

    /**
     * 套餐效期状态
     *
     * @param packageEndDate 套餐到期日
     * @return 到期类型 1. 临期 2. 到期，未临过期返回null
     */
    Integer getExpireStatus(Date packageEndDate);

    /**
     * 计算提醒比例
     *
     * @param margin 套餐剩余量
     * @param totalNum 套餐总量
     * @return
     */
    BigDecimal calcWarnPct(BigDecimal margin, BigDecimal totalNum);

    /**
     * 获取套餐告警信息，余量不足、将到期等
     *
     * @param orgOid 空间oid
     * @return
     */
    PackageWarnResponse getWarnInfo(String clientId, String orgOid);

    /**
     * 获取用户待支付列表
     *
     * @param orgOid
     * @param userOid
     * @return
     */
    List<PendingOrderResponse> listPendingOrder(String clientId, String orgOid, String userOid);

    /**
     * 获取用户所有已购订单（不支持计费隔离的单独查询）
     *
     * @param orgOid
     * @return
     */
    List<EffectiveOrderResponse> listAllEffectiveOrder(String orgOid);

    /**
     * 获取订单列表，按场景分组
     *
     * @param orgOid
     * @return
     */
    EffectiveOrderAggreResponse aggreEffectiveOrder(String clientId, String orgOid);
}
