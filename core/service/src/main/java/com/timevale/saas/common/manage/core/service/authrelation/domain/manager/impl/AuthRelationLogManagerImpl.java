package com.timevale.saas.common.manage.core.service.authrelation.domain.manager.impl;

import com.timevale.saas.common.manage.common.dal.dao.authrelation.AuthRelationLogDAO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationLogDO;
import com.timevale.saas.common.manage.common.dal.query.authrelation.AuthRelationLogQuery;
import com.timevale.saas.common.manage.core.service.authrelation.domain.convert.AuthRelationLogDomainConverter;
import com.timevale.saas.common.manage.core.service.authrelation.domain.manager.AuthRelationLogManager;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationLogModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/25 16:36
 */
@Component
public class AuthRelationLogManagerImpl implements AuthRelationLogManager {

    @Autowired
    private AuthRelationLogDAO authRelationLogDAO;

    @Override
    public int save(AuthRelationLogModel data) {
        AuthRelationLogDO authRelationLogDO = AuthRelationLogDomainConverter.model2do(data);
        int save = authRelationLogDAO.save(authRelationLogDO);
        data.setId(authRelationLogDO.getId());
        return save;
    }

    @Override
    public int updateById(AuthRelationLogModel data) {
        return authRelationLogDAO.updateById(AuthRelationLogDomainConverter.model2do(data));
    }

    @Override
    public AuthRelationLogModel getById(Long id) {
        return AuthRelationLogDomainConverter.do2Model(authRelationLogDAO.getById(id));
    }

    @Override
    public AuthRelationLogModel getByFlowId(String flowId) {
        return AuthRelationLogDomainConverter.do2Model(authRelationLogDAO.getByFlowId(flowId));
    }

    @Override
    public AuthRelationLogModel getByProcessId(String processId) {
        return AuthRelationLogDomainConverter.do2Model(authRelationLogDAO.getByProcessId(processId));
    }

    @Override
    public List<AuthRelationLogModel> listByIds(List<Long> ids) {
        return AuthRelationLogDomainConverter.do2ModelList(authRelationLogDAO.listByIds(ids));
    }

    @Override
    public List<AuthRelationLogModel> listByAuthRelationId(Long authRelationId, Integer offset, Integer pageSize) {
        return AuthRelationLogDomainConverter.do2ModelList(authRelationLogDAO.listByAuthRelationId(authRelationId, offset, pageSize));
    }

    @Override
    public List<AuthRelationLogModel> listByAuthTenantGidAndChildTenantGid(String authTenantGid, String childTenantGid, String currTenantGid) {
        return AuthRelationLogDomainConverter.do2ModelList(authRelationLogDAO.listByAuthTenantGidAndChildTenantGid(authTenantGid, childTenantGid, currTenantGid));
    }

    @Override
    public List<AuthRelationLogModel> listAvailable(Long authRelationId, List<Integer> statusList, Integer bizVersion, Integer size) {
        return AuthRelationLogDomainConverter.do2ModelList(authRelationLogDAO.listAvailable(Arrays.asList(authRelationId), statusList, bizVersion, size));
    }

    @Override
    public List<AuthRelationLogModel> listAvailable(List<Long> authRelationIds, List<Integer> statusList, Integer bizVersion, Integer size) {
        return AuthRelationLogDomainConverter.do2ModelList(authRelationLogDAO.listAvailable(authRelationIds, statusList, bizVersion, size));
    }

    @Override
    public List<AuthRelationLogModel> listByQuery(AuthRelationLogQuery query) {
        return AuthRelationLogDomainConverter.do2ModelList(authRelationLogDAO.listByQuery(query));
    }
}
