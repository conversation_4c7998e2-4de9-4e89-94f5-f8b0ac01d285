package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.account.service.model.service.mods.prop.Property;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.service.model.base.Page;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationOrderInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationOrderQueryInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationQueryAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationQueryShareConfigInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.PageAuthRelationLastEffectiveTimeInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.PageAuthRelationLastEffectiveTimeOutput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.PageEffectiveAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.PageEffectiveAuthRelationOutput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.QueryPageAuthRelationListInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.QueryPageAuthRelationLogListInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.SearchAuthRelationLastEffectiveTimeInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.SearchAuthRelationLastEffectiveTimeOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationBackendDTO;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationGetAuthRelationLastProcessOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationLogBackendDTO;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationOrderDTO;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationQueryShareConfigOutput;

import java.util.List;

/**
 * Created by tianlei on 2022/2/20
 */
public interface AuthRelationBizService {


    Page<AuthRelationBackendDTO> pageBackendAuthRelation(AuthRelationQueryAuthRelationInput input);


    AuthRelationQueryShareConfigOutput queryShareConfig(AuthRelationQueryShareConfigInput input);

    List<AuthRelationBackendDTO> listBackendAuthRelation(AuthRelationQueryAuthRelationInput input);

    AuthRelationGetAuthRelationLastProcessOutput getAuthRelationLastProcess(Long authRelationId);

    AuthRelationDO getById(Long authRelationId);

    List<AuthRelationDO> getByIds(List<Long> authRelationIds);

//    Integer countExistNeedLimitAuthRelationAdd(CountExistNeedLimitAuthRelationAddInput input);

    SearchAuthRelationLastEffectiveTimeOutput searchAuthRelationLastEffectiveTime(SearchAuthRelationLastEffectiveTimeInput input);

    PageAuthRelationLastEffectiveTimeOutput pageAuthRelationLastEffectiveTime(PageAuthRelationLastEffectiveTimeInput input);

    PageEffectiveAuthRelationOutput pageEffectiveAuthRelation(PageEffectiveAuthRelationInput input);


    void openVipGiveBalance(String tenantGid, String tenantOid, Integer level, String orderId);


    void adminChange(String tenantOid, String adminOid);


    void tenantInfoChange(String oid, List<Property> properties);
    

    List<AuthRelationDO> listEffectiveAuthRelation(Long lastAuthRelationId, Integer size);

    /**
     * 获取非严格有效的授权关系  生效 or 非正式生效都符合
     */
    AuthRelationDO getNotStrictEffectiveAuthRelationByChildTenantGid(String childTenantGid);

    Page<AuthRelationBackendDTO> queryPageAuthRelation(QueryPageAuthRelationListInput input);

    Page<AuthRelationLogBackendDTO> queryPageAuthRelationLog(QueryPageAuthRelationLogListInput input);

    void createAuthRelationOrder(AuthRelationOrderInput input);

    void updateAuthRelationOrder(AuthRelationOrderInput input);

    List<AuthRelationOrderDTO> queryAuthRelationLockOrderList(AuthRelationOrderQueryInput input);

    void changeAuthRelationTree(String authTenantGid);

}
