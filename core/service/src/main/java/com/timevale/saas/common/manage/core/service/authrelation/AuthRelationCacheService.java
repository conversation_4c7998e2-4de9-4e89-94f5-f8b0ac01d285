package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationShareConfigDO;
import com.timevale.saas.common.manage.common.service.model.base.Page;

import java.util.List;
import java.util.Map;

/**
 * Created by tianlei on 2022/2/18
 */
public interface AuthRelationCacheService {

    /****** 业务场景移除缓存 *********/
    void deleteWhenBuildAuthRelation(Long authRelationId, String parentTenantGid, String childTenantGid, List<String> configKeyList);

    void deleteListWhenBuildAuthRelation(List<Long> authRelationIds, List<String> parentGidList, List<String> childGidList, List<String> configKeyList);

    // 移除authRelation缓存
    void deleteAuthRelationCacheById(Long authRelationId);

    void deleteAuthRelationCacheByIds(List<Long> authRelationIds);


    Page<AuthRelationDO> pageHistoryEffectiveAuthRelationByTenantGid(String tenantGid, Integer authLevel,
            Integer pageIndex, Integer pageSize);

    List<AuthRelationDO> listHistoryEffectiveAuthRelationByTenantGid(String tenantGid);

    List<AuthRelationDO> listHistoryEffectiveAuthRelationByTenantGid(String tenantGid, Integer authLevel);

    //    void deleteHistoryEffectiveAuthRelation(String tenantGid);

    /************  有效关联企业列表 ************/

    Page<AuthRelationDO> pageEffectiveAuthRelationListByTenantGid(String tenantGid, Integer pageIndex, Integer pageSize);

    Page<AuthRelationDO> pageEffectiveAuthRelationListByTenantGid(String tenantGid, Integer authLevel,
            Integer pageIndex, Integer pageSize);

    List<AuthRelationDO> queryEffectiveAuthRelationListByTenantGid(String tenantGid);

    List<AuthRelationDO> queryEffectiveAuthRelationListByTenantGid(String tenantGid, Integer authLevel);

    /************  有效 + 非正式生效 关联企业列表 ************/
    List<AuthRelationDO> queryNotStrictEffectiveAuthRelationListByParentTenantGid(String parentTenantGid);

    /************  有效的授权关系操作 ************/
    // 根据gidList 查询有效的授权关系列表
    List<AuthRelationDO> listEffectiveAuthRelationByChildTenantGidList(List<String> childTenantGidList);

    List<AuthRelationDO> listEffectiveAuthRelationByChildTenantGidList(List<String> childTenantGidList, Integer authLevel);

    // 获取有效的授权关系
    AuthRelationDO getEffectiveAuthRelationByChildTenantGid(String childTenantGid);

    // 获取一级授权关系
    AuthRelationDO getTopEffectiveAuthRelationByChildTenantGid(String childTenantGid);

    // 从列表中获取一级授权关系
    AuthRelationDO getTopEffectiveAuthRelationByAuthRelationList(List<AuthRelationDO> relationList,String childTenantGid);

    AuthRelationDO getEffectiveAuthRelationByChildTenantGid(String childTenantGid, Integer authLevel);

    // 历史有效
    Map<String, List<AuthRelationDO>> listHistoryEffectiveAuthRelationByChildTenantGidList(List<String> childTenantGidList);

    Map<String, List<AuthRelationDO>> listHistoryEffectiveAuthRelationByChildTenantGidList(List<String> childTenantGidList,
            Integer authLevel);

    /**
     * 查看当前企业是否是 “主企业"
     * 判断依据：有有效的子企业，一个企业可能是子企业也可能是主企业
     */
    Boolean checkTenantNowIsParent(String tenantGid);


    /************  是否成为过主企业 **************/
    Boolean checkTenantWasParent(String tenantGid);


    /***********  是否建立过有效授权关系 ***********/
    Boolean checkHistoryAndNowBuildEffectiveAuthRelation(String parentTenantGid, String childTenantGid);

     List<AuthRelationDO> listHistoryOrNowEffectiveAuthRelation(String parentTenantGid, List<String> childTenantGidList);


    /***********  共享配置key相关操作 ***********/
    AuthRelationShareConfigDO getShareConfig(Long authRelationId, String configKey);

    void deleteShareConfig(Long authRelationId, String configKey);

    void deleteShareConfig(List<Long> authRelationIds, String configKey);

}
