package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.common.dal.dataobject.ApplyTrialLogDO;
import com.timevale.saas.common.manage.common.service.model.output.bean.ApplyTrialQueryDTO;
import com.timevale.saas.common.manage.common.service.model.output.bean.ApplyTrialDTO;
import com.timevale.saas.common.manage.core.model.model.UserAccount;
import com.timevale.saas.common.manage.core.model.model.UserAccountDetail;

/**
 * 申请试用
 *
 * <AUTHOR>
 * @since 2022-11-21
 */
public interface ApplyTrialService {

    /**
     * 申请试用
     *
     * @param funcCode
     * @param userAccount
     * @return 申请记录
     */
    ApplyTrialDTO applyTrial(String funcCode, String client, UserAccountDetail userAccount,
                             UserAccount subjectAccount, String applicantOid, String applicantSubjectOid);

    /**
     * 获取最近30天内申请试用记录
     * @param funcCode
     * @param userAccount
     * @return
     */
    ApplyTrialLogDO getRecent30DaysLog(String funcCode, UserAccount userAccount);

    /**
     * 根据条件获取申请日志
     * @param funcCode
     * @param personGid
     * @param subjectGid
     * @param status
     * @return
     */
    ApplyTrialLogDO queryLogByConditions(String funcCode, String personGid, String subjectGid, Integer status);

    /**
     * 查询会员功能申请记录
     * @param funcCode
     * @param applicantOid
     * @param subjectOid
     * @param operatorId
     * @param language
     * @return
     */
    ApplyTrialQueryDTO queryApplyLog(String funcCode, String applicantOid, String applicantSubjectOid, String subjectOid, String operatorId, String language);
}
