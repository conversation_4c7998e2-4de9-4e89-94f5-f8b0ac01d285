package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationOrderDO;

import java.util.List;

/**
 * AuthRelationOrderService
 *
 * <AUTHOR>
 * @since 2023/4/10 8:08 下午
 */
public interface AuthRelationOrderService {

    void lockOrder(String authLogId, String orderId);

    void unlockOrder(String authLogId, String orderId);

    List<AuthRelationOrderDO> queryOrderListByGid(String authGid, Integer status);

}
