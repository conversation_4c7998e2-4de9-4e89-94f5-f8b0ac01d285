package com.timevale.saas.common.manage.core.service.authrelation.domain.manager;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationModel;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/25 17:25
 */
public interface AuthRelationManager {


    /**
     * 查询上级链表
     * 构建 a （树顶） -> b -> c 这种关系， 根据c查出来 [b, a]
     * 不要随便修改这个方法顺序，外部依赖这个顺序
     */
    List<AuthRelationModel> effectiveChain(String tenantGid);


    /**
     * 查询上级链表
     * a -> b -> c -> d -> e
     *             -> j -> k
     * parentTenantGid = b childTenantGid = c
     * 返回 <[ab], [cd, cj], [cd, cj, de, dk]> 这几个授权关系
     */
    Pair<List<AuthRelationModel>, Pair<List<AuthRelationModel>, List<AuthRelationModel>>>
    effectiveChain(String parentTenantGid, String childTenantGid);

    /**
     * 列表查询
     */
    List<AuthRelationModel> listPresentAuthRelationByParentAndBizHierarchyType(List<String> parentTenantGids,
                                                                               List<Integer> statusList,
                                                                               List<Integer> bizHierarchyTypes);

}
