package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.model.roleandprivilege.SaasPrivilegeConfigDTO;
import com.timevale.saas.common.manage.core.model.model.roleandprivilege.SaasPrivilegeDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/6/7
 * 角色和权限相关业务
 */
public interface RoleAndPrivilegeService {
    /**
     * 获取saas的所有权限信息
     *
     * @return
     */
    List<SaasPrivilegeDTO> getSaasPrivileges();

    /** 运营支撑设置saas的权限配置 */
    void setSaasPrivilegesConfig(List<SaasPrivilegeConfigDTO> saasPrivilegesConfigList);
}
