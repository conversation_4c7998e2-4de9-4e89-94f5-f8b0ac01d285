package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.enums.SealTypeEnum;
import com.timevale.saas.common.manage.core.model.request.seal.CancelOnlineLegalAuthRequest;
import com.timevale.saas.common.manage.core.model.request.seal.CreateLegalImageSealRequest;
import com.timevale.saas.common.manage.core.model.request.seal.CreateLegalTemplateRequest;
import com.timevale.saas.common.manage.core.model.request.seal.CreateOfficialTemplateSealRequest;
import com.timevale.saas.common.manage.core.model.request.seal.CreateOrgImageSealRequest;
import com.timevale.saas.common.manage.core.model.request.seal.GetSealBySealIdsRequest;
import com.timevale.saas.common.manage.core.model.request.seal.GetSealListRequest;
import com.timevale.saas.common.manage.core.model.request.seal.LegalAuthOnlineSignRequest;
import com.timevale.saas.common.manage.core.model.request.seal.OfflineApplyLegalAuthRequest;
import com.timevale.saas.common.manage.core.model.request.seal.PreviewLegalTemplateRequest;
import com.timevale.saas.common.manage.core.model.request.seal.PreviewOfficialTemplateRequest;
import com.timevale.saas.common.manage.core.model.request.seal.QuerySealListRequest;
import com.timevale.saas.common.manage.core.model.request.seal.RevokeOfflineLegalAuthRequest;
import com.timevale.saas.common.manage.core.model.request.seal.SetOrgDefaultSealRequest;
import com.timevale.saas.common.manage.core.model.request.seal.UpdateAliasRequest;
import com.timevale.saas.common.manage.core.model.response.seal.*;
import com.timevale.saas.common.manage.core.model.response.seal.CreateLegalImageSealResponse;
import com.timevale.saas.common.manage.core.model.response.seal.CreateLegalTemplateResponse;
import com.timevale.saas.common.manage.core.model.response.seal.CreateOfficialTemplateSealResponse;
import com.timevale.saas.common.manage.core.model.response.seal.CreateOrgImageSealResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetLegalAuthDetailResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetLegalAuthStatusResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetLegalTemplateDetailResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetOfficialTemplateDetailResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetSealBySealIdsResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetSealCreateConfigResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetSealListResponse;
import com.timevale.saas.common.manage.core.model.response.seal.LegalAuthOnlineSignResponse;
import com.timevale.saas.common.manage.core.model.response.seal.OfflineApplyLegalAuthResponse;
import com.timevale.saas.common.manage.core.model.response.seal.SealSimpleListResponse;

/**
 * <AUTHOR>
 * @since 2022/7/4 saas企业/法人章相关
 */
public interface SealOrganizationService {

    /**
     * 查询硬仗列表
     *
     * @param appId
     * @param orgOid
     * @param request
     * @return
     */
    GetSealListResponse getSealList(String appId, String orgOid, GetSealListRequest request);

    /**
     * 按印章类型查询
     *
     * @param appId
     * @param orgOid
     * @param request
     * @return
     */
    SealSimpleListResponse querySealListByBizTypes(String appId, String orgOid, QuerySealListRequest request);

    /**
     * 根据印章id批量查询印章信息
     *
     * @param request
     * @return
     */
    GetSealBySealIdsResponse getSealsBySealIds(String appId, GetSealBySealIdsRequest request);

    /**
     * 设置默认企业章
     *
     * @param orgOid
     * @param request
     * @return
     */
    boolean setDefaultSeal(
            String appId, String operatorOid, String orgOid, SetOrgDefaultSealRequest request);

    /**
     * 删除印章
     *
     * @param appId
     * @param orgOid
     * @param operatorOid
     * @param sealId
     * @return
     */
    boolean deleteSeal(String appId, String orgOid, String operatorOid, String sealId);

    /**
     * 新增企业模板印章
     *
     * @param appId
     * @param orgOid
     * @param operatorOid
     * @param request
     * @return
     */
    CreateOfficialTemplateSealResponse createOfficialTemplateSeal(
            String appId,
            String orgOid,
            String operatorOid,
            CreateOfficialTemplateSealRequest request);

    /**
     * 新增企业图片印章
     *
     * @param appId
     * @param orgOid
     * @param operatorOid
     * @param request
     * @return
     */
    CreateOrgImageSealResponse createOrgImageSeal(
            String appId, String orgOid, String operatorOid, CreateOrgImageSealRequest request);

    /**
     * 获取印章创建的配置信息
     *
     * @param appId
     * @param bizType
     * @return
     */
    GetSealCreateConfigResponse getSealCreateConfig(String appId, String bizType);

    /**
     * 更新印章名称
     *
     *
     * @param appId
     * @param operatorOid
     * @param orgOid
     * @param request
     * @return
     */
    boolean updateSealAlias(
            String appId, String operatorOid, String orgOid, UpdateAliasRequest request);

    /**
     * 查询企业模版章详情
     *
     *
     * @param appId
     * @param operatorOid
     * @param orgOid
     * @param sealId
     * @return
     */
    GetOfficialTemplateDetailResponse getOfficialTemplateDetail(
            String appId, String operatorOid, String orgOid, String sealId);

    /**
     * 法人章在线授权
     *
     * @param operatorOid
     * @param request
     * @return
     */
    LegalAuthOnlineSignResponse legalOnlineAuthSign(
            String operatorOid, LegalAuthOnlineSignRequest request);

    /**
     * 法人章在线签署催签
     *
     * @param orgOid
     * @param operatorOid
     */
    boolean legalOnlineSignUrge(String orgOid, String operatorOid);

    /**
     * 线下法人章授权申请
     *
     * @param request
     * @return
     */
    OfflineApplyLegalAuthResponse legalOfflineAuthApply(
            String operatorOid, OfflineApplyLegalAuthRequest request);

    /**
     * 创建法人章
     *
     * @param appId
     * @param orgOid
     * @param operatorOid
     * @param request
     * @return
     */
    CreateLegalTemplateResponse createLegalTemplateSeal(
            String appId, String orgOid, String operatorOid, CreateLegalTemplateRequest request);

    /**
     * 创建法人图片章
     *
     * @param appId
     * @param orgOid
     * @param operatorOid
     * @param request
     * @return
     */
    CreateLegalImageSealResponse createLegalImageSeal(
            String appId, String orgOid, String operatorOid, CreateLegalImageSealRequest request);
    /**
     * 获取法人章详情
     *
     * @param orgOid
     * @param sealId
     * @return
     */
    GetLegalTemplateDetailResponse getLegalTemplateDetail(
            String appId, String operatorOid, String orgOid, String sealId);

    /**
     * 预览企业模版章
     *
     * @param orgOid
     * @param request
     * @return
     */
    String previewOfficialTemplate(String orgOid, PreviewOfficialTemplateRequest request);

    /**
     * 预览法人章
     *
     * @param orgOid
     * @param request
     * @return
     */
    String previewLegalSeal(String orgOid, PreviewLegalTemplateRequest request);

    /**
     * 获取法人章的授权详情
     *
     * @param orgOid
     * @param operatorOid
     * @return
     */
    GetLegalAuthDetailResponse getLegalAuthDetail(String orgOid, String operatorOid);

    /**
     * 获取法人章的授权信息
     * @param orgOid
     * @param operatorOid
     * @return
     */
    GetLegalAuthStatusResponse getLegalAuthInfo(String orgOid, String operatorOid);


    /**
     * 取消法人章线上授权
     *
     * @param operatorOid
     * @param request
     */
    boolean cancelOnlineLegalSealAuth(String operatorOid, CancelOnlineLegalAuthRequest request);

    /**
     * 取消法人章线下授权
     *
     * @param operatorOid
     * @param request
     */
    boolean cancelOfflineLegalSealAuth(String operatorOid, RevokeOfflineLegalAuthRequest request);

    /**
     * 法人章重新授权
     *
     * @param operatorOid
     * @param orgOid
     */
    boolean recoverLegalAuth(String orgOid, String operatorOid);

    /**
     * 获取对应印章已创建数量
     *
     * @param orgOid
     * @param sealTypeEnum
     * @return
     */
    GetSealCreatedCountResponse getSealCreatedCount(String orgOid, SealTypeEnum sealTypeEnum);
}
