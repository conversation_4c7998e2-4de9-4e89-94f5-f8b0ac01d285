package com.timevale.saas.common.manage.core.service.authrelation;

import com.google.common.collect.Lists;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizSceneEnum;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.common.service.exception.SaasCommonBizException;
import com.timevale.saas.common.manage.core.model.model.UserAccount;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipAndFunctionBean;
import com.timevale.saas.common.manage.core.service.conf.SaasCommonConfig;
import com.timevale.saas.common.manage.core.service.seal.SealSourceService;
import com.timevale.saas.common.manage.core.service.usercenter.UserCenterService;
import com.timevale.saas.common.manage.core.service.vip.factory.SaasVipServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.timevale.saas.common.manage.common.service.exception.ResultEnum.ACCOUNT_ID_INVALID;
import static com.timevale.saas.common.manage.common.service.exception.ResultEnum.ACCOUNT_VIP_FUNCTION_INVALID_UPGRADE;

/**
 * 印章权限校验帮助类
 *
 * <AUTHOR>
 * @since 2023/11/15
 */
@Component
public class SealPermissionCheckHelper {

    @Autowired private AuthRelationCoreService authRelationCoreService;
    @Autowired private UserCenterService userCenterService;
    @Autowired private SealSourceService sealSourceService;
    @Autowired private SaasCommonConfig saasCommonConfig;
    @Autowired private SaasVipServiceFactory saasVipServiceFactory;

    /**
     * 校验印章权限 支持：企业间授权关系校验、印章权限校验 临时使用，当印章相关接口迁移到saas-biz后，使用注解校验
     *
     * @param parentOid 主企业oid
     * @param childOid 子企业oid
     * @param operatorId 操作人oid
     */
    public void checkSealPermission(
            String parentOid,
            String childOid,
            String operatorId,
            String resourceKey,
            String privilegeKey) {
        checkSealPermission(parentOid, childOid, operatorId, resourceKey, privilegeKey, null);
    }

    public void checkSealPermissionAndVipFunction(
            String currentTenantId,
            String parentOid,
            String childOid,
            String operatorId,
            String resourceKey,
            String privilegeKey,
            String funcCode) {
        checkSealPermission(parentOid, childOid, operatorId, resourceKey, privilegeKey, null);
        checkFunction(currentTenantId, funcCode);
    }

    public void checkSealPermission(
            String parentOid,
            String childOid,
            String operatorId,
            String resourceKey,
            String privilegeKey,
            ResultEnum errorEnum) {
        if(!saasCommonConfig.getRelationPrivilegeCheckOff()){
            //校验开关不关闭，校验关联企业
            checkSealAuthRelation(parentOid, childOid);
        }
        if (StringUtils.isNoneBlank(resourceKey, privilegeKey)) {
            sealSourceService.checkSealPrivilege(operatorId, parentOid, resourceKey, privilegeKey, errorEnum);
        }
    }

    /**
     * 校验关联企业的印章管理授权
     *
     * @param parentOid
     * @param childOid
     */
    public void checkSealAuthRelation(String parentOid, String childOid) {
        if (StringUtils.isBlank(childOid) || StringUtils.equals(parentOid, childOid)) {
            // childOid为空兼容上线还没有子企业需要校验的场景
            return;
        }
        UserAccount parent = userCenterService.getAccountBaseByOid(parentOid);
        UserAccount child = userCenterService.getAccountBaseByOid(childOid);
        if (parent == null
                || StringUtils.isBlank(parent.getGid())
                || child == null
                || StringUtils.isBlank(child.getGid())) {
            throw new SaasCommonBizException(ACCOUNT_ID_INVALID);
        }

        authRelationCoreService.checkHeadOfficeAndAuthResources(
                parent.getGid(),
                child.getGid(),
                Lists.newArrayList(AuthRelationBizSceneEnum.SEAL_MANAGE.getCode()));
    }

    private void checkFunction(String accountOid,String funcCode){
        VipAndFunctionBean vipAndFunctionBean =
                saasVipServiceFactory.getService().queryVipFunctionInfo(accountOid,funcCode);
        if(!vipAndFunctionBean.isEnable()){
            throw new SaasCommonBizException(
                    ACCOUNT_VIP_FUNCTION_INVALID_UPGRADE,
                    vipAndFunctionBean.getVipName(),
                    vipAndFunctionBean.getName());
        }

    }
}
