package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.common.service.model.base.BaseResult;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskAddInput;
import com.timevale.saas.common.manage.common.service.model.input.SaasTaskUpdateInput;
import com.timevale.saas.common.manage.common.service.model.output.SaasTaskResultOutput;
import com.timevale.saas.common.manage.common.service.model.output.SaasTaskUpdateOutput;
import com.timevale.saas.common.manage.common.service.model.output.SaasUnderwayTaskResultOutput;
import com.timevale.saas.common.manage.core.model.request.QuerySaasTaskRequest;
import com.timevale.saas.common.manage.core.model.response.ExportFailedTaskResponse;
import com.timevale.saas.common.manage.core.model.response.HandlerTaskResultResponse;
import com.timevale.saas.common.manage.core.model.response.QueryFailedTaskResponse;
import com.timevale.saas.common.manage.core.model.response.QuerySaasTaskResponse;
import com.timevale.saas.common.manage.core.model.response.bean.TaskBean;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
public interface SaasTaskService {
    /**
     * 新增任务， 支持批量，一次批量上限为100
     * @param input
     */
    void addTask(SaasTaskAddInput input);

    SaasTaskUpdateOutput updateTask(SaasTaskUpdateInput input);

    TaskBean querySaasTaskById(String taskId);

    TaskBean querySaasTaskByBizId(String bizId, Integer taskType);

    QuerySaasTaskResponse querySaasTasks(QuerySaasTaskRequest request);

    QuerySaasTaskResponse querySubTasks(
            String taskId, List<Integer> statusList, Integer pageNum, Integer pageSize);

    QueryFailedTaskResponse queryFailTasks(
            String accountId, String taskId, Integer pageNum, Integer pageSize);

    ExportFailedTaskResponse exportFailTasks(String accountId, String taskId);

    boolean hasNewTasks(String accountId);

    /**
     * 根据用户id判断是否存在进行中的任务
     * @param accountId
     * @return
     */
    boolean hasExecutingTasks(String accountId);

    /**
     * 根据业务id判断是否存在进行中的任务
     * @param bizId
     * @param taskType
     * @return null-表示没有进行中的任务
     */
    Integer executingTaskType(String bizId, Integer taskType);

    /**
     * 查询任务结果，仅返回存储的结果信息，不处理
     * @param taskId
     * @param accountId
     * @return
     */
    SaasTaskResultOutput queryTaskResult(String taskId, String bizId, Integer taskType, String accountId);

    /**
     * 查询任务结果并处理，
     * @param taskId
     * @param accountId
     * @return
     */
    BaseResult<HandlerTaskResultResponse> handleTaskResult(String taskId, String accountId);

    /**
     * 取消任务
     *
     * @param taskId
     * @param accountId
     * @return
     */
    void cancelTask(String taskId, String accountId);

    /**
     * 判断当前用户是否有该类型的任务正在执行中
     * @param accountId
     * @param taskType
     * @return
     */
    Boolean existExecutingParentTaskByAccountIdAndType(String accountId, Integer taskType);

    SaasUnderwayTaskResultOutput queryUnderwayTaskByAccountIdAndType(String accountId, Integer taskType);
}
