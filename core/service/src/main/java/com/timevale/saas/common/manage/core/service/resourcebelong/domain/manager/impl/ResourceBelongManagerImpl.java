package com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager.impl;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saas.common.manage.common.dal.dao.resourcebelong.ResourceBelongDAO;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.convert.ResourceBelongDomainConverter;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager.ResourceBelongManager;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.model.ResourceBelongModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/7/3 20:41
 */
@Component
public class ResourceBelongManagerImpl implements ResourceBelongManager {

    @Autowired
    private ResourceBelongDAO resourceBelongDAO;

    @Override
    public int save(List<ResourceBelongModel> list) {
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return resourceBelongDAO.batchSave(ResourceBelongDomainConverter.model2do(list));
    }

    @Override
    public int delete(List<Long> ids) {
        return resourceBelongDAO.deleteByIds(ids);
    }

    @Override
    public int deleteByResource(List<String> resourceIds, Integer resourceType) {
        return resourceBelongDAO.deleteByResource(resourceIds, resourceType);
    }

    @Override
    public List<ResourceBelongModel> listByResource(String resourceId, Integer resourceType) {
        return ResourceBelongDomainConverter.do2Model(resourceBelongDAO.listByResource(resourceId, resourceType));
    }

    @Override
    public List<String> checkHaveDataResourceIds(List<String> resourceIds, Integer resourceType) {
        if (CollectionUtils.isEmpty(resourceIds) || null == resourceType) {
            return new ArrayList<>();
        }
        return resourceBelongDAO.checkHaveDataResourceIds(resourceIds, resourceType);
    }

    @Override
    public List<ResourceBelongModel> listByResource(List<String> resourceIds, Integer resourceType) {
        return ResourceBelongDomainConverter.do2Model(resourceBelongDAO.listByResourceIds(resourceIds, resourceType));
    }

    @Override
    public List<ResourceBelongModel> listByResourceWithDeptId(Set<String> resourceIds, String directDeptId) {
        return ResourceBelongDomainConverter.do2Model(resourceBelongDAO.listByResourceIdsWithDeptId(resourceIds, directDeptId));
    }

    @Override
    public List<ResourceBelongModel> listByDirectDeptId(String directDeptId, int limit) {
        return ResourceBelongDomainConverter.do2Model(resourceBelongDAO.listByDirectDeptId(directDeptId, limit));
    }

    @Override
    public int batchUpdatePath(Collection<Long> resourceBelongIds, List<String> newPath) {
        if (CollectionUtils.isEmpty(resourceBelongIds) || CollectionUtils.isEmpty(newPath)) {
            return 0;
        }
        return resourceBelongDAO.batchUpdatePath(resourceBelongIds, newPath.toString());
    }

    @Override
    public List<ResourceBelongModel> listScrollByDirectDeptId(String directDeptId, Long minId, int limit) {
        return ResourceBelongDomainConverter.do2Model(resourceBelongDAO.listScrollByDirectDeptId(directDeptId, minId, limit));
    }
}
