package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigQueryDetailInput;
import com.timevale.saas.common.manage.common.service.model.input.AuditLogSubscriptionConfigQueryInput;
import com.timevale.saas.common.manage.common.service.model.output.AuditLogSubscriptionConfigOutput;
import com.timevale.saas.common.manage.common.service.model.output.bean.AuditLogSubscriptionConfigDTO;
import com.timevale.saas.common.manage.core.service.mq.model.MemberDeleteMsgEntity;

/**
 * AuditLogSubscriptionConfigService
 *
 * <AUTHOR>
 * @since 2025/2/20 上午10:30
 */
public interface AuditLogSubscriptionConfigService {

    AuditLogSubscriptionConfigOutput queryConfigList(AuditLogSubscriptionConfigQueryInput input);

    AuditLogSubscriptionConfigOutput queryConfigOriginalList(AuditLogSubscriptionConfigQueryInput input);

    /**
     * 走缓存，任务处理时使用的rpc
     *
     * @param input
     * @return
     */
    AuditLogSubscriptionConfigDTO getOpenConfigDetail(AuditLogSubscriptionConfigQueryDetailInput input);

    AuditLogSubscriptionConfigDTO getDetail(String configId, String tenantGid);

    void changeStatus(String configId, String tenantGid);

    void changeAccount(String configId, String tenantGid, String account);

    void deleteSubscribeAccount(MemberDeleteMsgEntity msgEntity);
}
