package com.timevale.saas.common.manage.core.service.authrelation;

import com.alibaba.fastjson.JSON;
import com.timevale.framework.puppeteer.ConfigService;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationShareConfigTemplateDO;
import com.timevale.saas.common.manage.common.service.exception.ResultEnum;
import com.timevale.saas.common.manage.core.service.conf.bean.AuthRelationConfig;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by tian<PERSON><PERSON> on 2022/4/14
 */
@Service
public class AuthRelationConfigCenter {

    private static final String AUTH_RELATION_CONFIG = "authRelationConfig";

    private static final String AUTH_RELATION_SHARE_CONFIG_TEMPLATE = "authRelationShareConfigTemplate";

    public List<AuthRelationShareConfigTemplateDO> shareConfigTemplate() {

        String configValue = ConfigService.getAppConfig().getProperty(AUTH_RELATION_SHARE_CONFIG_TEMPLATE, null);
        if (StringUtils.isBlank(configValue)) {
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()),
                    "关联企业授权 authRelationShareConfigTemplate 未配置");
        }
        return JSON.parseArray(configValue, AuthRelationShareConfigTemplateDO.class);
    }
    public List<String> shareConfigKeys() {
        return shareConfigTemplate().stream().map(AuthRelationShareConfigTemplateDO::getConfigKey).collect(Collectors.toList());
    }


    public AuthRelationConfig getConfig() {
        String configValue = ConfigService.getAppConfig().getProperty(AUTH_RELATION_CONFIG, null);
        if (StringUtils.isBlank(configValue)) {
            throw new BaseBizRuntimeException(String.valueOf(ResultEnum.COMMON.getCode()),
                    "关联关系授权中心核心配置未设置");
        }
        AuthRelationConfig config = JSON.parseObject(configValue, AuthRelationConfig.class);
        return config;
    }

}
