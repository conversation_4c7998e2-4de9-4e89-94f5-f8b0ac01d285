package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationLogDO;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationDeleteInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationDingSignDeleteInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationOrderInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationParentDeleteAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationSignFailureInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationSignSuccessInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationStartContractSuccessCommonInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationStartContractSuccessInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.ChildTenantDirectRescindAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.ChildTenantRescindAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.CreateAuthRelationStartContractSuccessInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.DirectRescindAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.ParentTenantRescindAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.SignRescindAuthRelationInput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationDeleteAuthRelationOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationDingSignDeleteOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationParentDeleteAuthRelationOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationSignFailureOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationSignSuccessOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationStartContractSuccessCommonOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationStartContractSuccessOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.ChildTenantDirectRescindAuthRelationOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.ChildTenantRescindAuthRelationOutput;
import com.timevale.saas.common.manage.core.service.conf.bean.AuthRelationConfig;

import java.util.List;

/** Created by tianlei on 2022/2/17 */
public interface AuthRelationCoreService {

    AuthRelationStartContractSuccessOutput startContractSuccess(
            AuthRelationStartContractSuccessInput input);

    /** 该方法主要用于，钉签过来的非正式授权的数据，进行正式授权时使用 */
    AuthRelationStartContractSuccessCommonOutput startContractSuccessCommon(
            AuthRelationStartContractSuccessCommonInput input);

    AuthRelationSignSuccessOutput signSuccess(AuthRelationSignSuccessInput input);

    AuthRelationSignFailureOutput signFailure(AuthRelationSignFailureInput input);

    Integer getEffectiveUnit();

    /** 曾经是否成为过主企业 */
    Boolean checkTenantWasParent(String tenantGid);

    /** 主企业删除授权关系 */
    AuthRelationParentDeleteAuthRelationOutput parentDeleteAuthRelation(
            AuthRelationParentDeleteAuthRelationInput input);

    /** 主企业解除授权 */
    ChildTenantRescindAuthRelationOutput parentTenantRescindAuthRelation(
            ParentTenantRescindAuthRelationInput input);

    /** 关联企业解除非正式授权 */
    ChildTenantDirectRescindAuthRelationOutput childTenantDirectRescindAuthRelation(
            ChildTenantDirectRescindAuthRelationInput input);

    /** 关联企业解除正式授权，通过签解约数的形式 */
    ChildTenantRescindAuthRelationOutput childTenantRescindAuthRelation(
            ChildTenantRescindAuthRelationInput input);

    /** 钉签删除数据 */
    AuthRelationDingSignDeleteOutput delete(AuthRelationDingSignDeleteInput input);

    /** 定期扫描是否到期 每天0点扫描一次 */
    void authRelationExpire(AuthRelationDO authRelationDO);

    AuthRelationLogDO getAuthRelationLastLog(Long authRelationId);

    /** 获取关联企业 有效 + 非正式生效的 授权关系 */
    AuthRelationDO getEffectiveOrInformalityEffectiveAuthRelation(String childTenantGid);

    /** 获取关联企业 有效 + 待生效的 授权关系 */
    List<AuthRelationDO> getEffectiveAndUnEffectiveAuthRelationByChildTenantGid(
            String childTenantGid);

    /** 获取配置值 */
    AuthRelationConfig getConfig();

    void directRescindAuthRelation(DirectRescindAuthRelationInput input);

    /** 关联企业解除正式授权，通过签解约数的形式 */
    void signRescindAuthRelation(SignRescindAuthRelationInput input);

    /** 删除授权关系 */
    AuthRelationDeleteAuthRelationOutput deleteAuthRelation(AuthRelationDeleteInput input);

    AuthRelationStartContractSuccessOutput createAuthRelationAfterContractStartSuccess(
            CreateAuthRelationStartContractSuccessInput input);

    void updateAuthRelationOrder(AuthRelationOrderInput input);

    /**
     * 校验授权关系，及是否总部oid为正式总部，及授权场景是否存在
     *
     * @param headOfficeGid 总部企业oid
     * @param childTenantGid childTenantOid
     * @param bizSceneList 校验的授权场景列表
     */
    void checkHeadOfficeAndAuthResources(
            String headOfficeGid, String childTenantGid, List<String> bizSceneList);
}
