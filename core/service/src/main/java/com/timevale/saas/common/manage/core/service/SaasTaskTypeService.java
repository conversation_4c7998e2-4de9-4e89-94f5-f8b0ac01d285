package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.TaskTypeAddRequest;
import com.timevale.saas.common.manage.core.model.request.TaskTypeUpdateRequest;
import com.timevale.saas.common.manage.core.service.handler.bean.TaskTypeBean;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
public interface SaasTaskTypeService {

    void add(TaskTypeAddRequest input);

    void update(TaskTypeUpdateRequest input);

    List<TaskTypeBean> list();
}
