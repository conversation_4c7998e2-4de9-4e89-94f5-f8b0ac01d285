package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationShareConfigDO;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationUpdateShareConfigInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.GetAuthRelationShareConfigInput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.GetAuthRelationShareConfigOutput;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by tianlei on 2022/4/14
 */
public interface AuthRelationShareConfigCoreService {

    GetAuthRelationShareConfigOutput getAuthRelationShareConfig(GetAuthRelationShareConfigInput input);

    List<AuthRelationShareConfigDO> getShareConfigSaveDataWhenBuildRelation(Long authRelationId);

    List<AuthRelationShareConfigDO> getShareConfigSaveDataWhenBuildRelation(Long authRelationId, List<String> shareConfigs);

    List<AuthRelationShareConfigDO> listShareConfigSaveDataWhenBuildRelation(List<Long> authRelationIds, List<String> shareConfigs);

    /**
     * 所有的配置key
     */
    List<String> allShareConfigKey();

    /**
     * 获取配置
     * @param   authRelationId  authRelationId
     * @return  配置Id
     */
    List<AuthRelationShareConfigDO> getShareConfigCloseDataWhenRescindRelation(Long authRelationId);

    List<AuthRelationShareConfigDO> listShareConfigCloseDataWhenRescindRelation(List<Long> authRelationIds);

    boolean isOpen(String configValue);

    String closeBoolValue();

    String openBoolValue();

    /**
     * 更新配置
     */
    void updateShareConfig(AuthRelationUpdateShareConfigInput input);

    List<AuthRelationShareConfigDO> listByAuthRelationIdListAndConfigKey(@Param("authRelationIds") List<Long> authRelationIds,
            @Param("configKey") String configKey, @Param("configValue") String configValue);

}
