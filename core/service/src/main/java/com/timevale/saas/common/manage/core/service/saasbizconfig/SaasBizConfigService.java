package com.timevale.saas.common.manage.core.service.saasbizconfig;

import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.ChangeSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.GetSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.ChangeSaasBizConfigsOutput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.GetSaasBizConfigsOutput;

/**
 * Saas配置信息服务
 *
 * <AUTHOR>
 * @since 2023-07-31 13:35
 */
public interface SaasBizConfigService {
    /**
     * 查询配置信息，目前只提供根据配置键查询（目前最多支持10个）
     *
     * @return
     */
    GetSaasBizConfigsOutput getBizConfigs(GetSaasBizConfigsInput input);

    /**
     * 更新配置信息，不存在则新增
     *
     * @return
     */
    ChangeSaasBizConfigsOutput changeBizConfigs(ChangeSaasBizConfigsInput input);
}
