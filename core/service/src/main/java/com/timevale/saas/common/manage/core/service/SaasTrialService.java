package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.model.AccountTrialFunc;
import com.timevale.saas.common.manage.core.service.bo.OrgTrialResult;
import com.timevale.saas.common.manage.core.service.bo.QueryOrgTrial;
import com.timevale.saas.common.manage.core.service.bo.SaveOrgTrial;

import java.util.Date;

/**
 * saas试用功能的service接口
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
public interface SaasTrialService {

    /**
     * 获取用户试用功能列表
     * @param accountGid 用户gid
     * @return
     */
    AccountTrialFunc queryAccountTrialFunc(String accountGid);

    /**
     * 保存企业试用的功能信息
     *
     * @param input
     */
    Date saveOrgTrailFunction(SaveOrgTrial input);

    /**
     * 查询企业试用的功能信息
     *
     * @param input
     * @return
     */
    OrgTrialResult queryOrgTrailFunction(QueryOrgTrial input);

}
