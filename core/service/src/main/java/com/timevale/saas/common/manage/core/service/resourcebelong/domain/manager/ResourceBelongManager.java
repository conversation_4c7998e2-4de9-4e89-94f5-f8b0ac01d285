package com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager;

import com.timevale.saas.common.manage.core.service.resourcebelong.domain.model.ResourceBelongModel;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/7/3 20:40
 */
public interface ResourceBelongManager {

    /**
     * 批量保存
     */
    int save(List<ResourceBelongModel> list);

    /**
     * 批量删除
     */
    int delete(List<Long> ids);


    /**
     * 批量删除
     */
    int deleteByResource(List<String> resourceIds, Integer resourceType);


    int batchUpdatePath(Collection<Long> resourceBelongIds, List<String> newPath);

    /**
     * 根据资源获取归属
     */
    List<ResourceBelongModel> listByResource(String resourceId, Integer resourceType);

    List<String> checkHaveDataResourceIds(List<String> resourceIds,
                                          Integer resourceType);

    /**
     * 查询归属资源
     *
     * @param resourceIds
     * @param resourceType
     * @return
     */
    List<ResourceBelongModel> listByResource(List<String> resourceIds, Integer resourceType);

    /**
     * 查询归属资源
     *
     * @param resourceIds
     * @param directDeptId
     * @return
     */
    List<ResourceBelongModel> listByResourceWithDeptId(Set<String> resourceIds, String directDeptId);

    /**
     * 做资源归属变更查询使用
     * 做资源归属变更时，根据部门获取
     *
     * @param directDeptId 直属部门
     * @param limit        限制
     */
    List<ResourceBelongModel> listByDirectDeptId(String directDeptId, int limit);

    /**
     * order by id asc
     */
    List<ResourceBelongModel> listScrollByDirectDeptId(String directDeptId, Long minId, int limit);

}
