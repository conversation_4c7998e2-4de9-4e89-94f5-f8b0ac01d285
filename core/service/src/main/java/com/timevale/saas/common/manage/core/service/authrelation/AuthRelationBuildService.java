package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationSignSuccessInput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationSignSuccessOutput;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationLogModel;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;

/**
 * Created by tianlei on 2022/2/28
 */
public interface AuthRelationBuildService {


    AuthRelationSignSuccessOutput signSuccess(AuthRelationSignSuccessInput input);

    Pair<Date, Date> calEndDate(Date startDate, Integer duration);

    void changeBestAuthRelationLog(AuthRelationDO authRelationDO,AuthRelationLogModel bestAuthRelationLog);

    AuthRelationLogModel getBestAuthRelationLog(AuthRelationLogModel logData, AuthRelationDO authRelationDO);

    /**
     * 授权关系变更推送用户中心的处理
     * 用户中心对应接口都是异步操作的
     * @param authTenantOid
     * @param parentTenantOid
     * @param childTenantOid
     * @param authResource
     */
    void relationChangePushUserCenterHandler(String authTenantOid, String parentTenantOid, String childTenantOid, List<String> authResource);
}
