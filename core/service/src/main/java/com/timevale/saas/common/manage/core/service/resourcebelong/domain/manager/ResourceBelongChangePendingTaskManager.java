package com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager;

import com.timevale.saas.common.manage.common.dal.dataobject.ResourceBelongChangePendingTaskDO;

import java.util.List;

/**
 * 资源归属变更待执行任务服务类
 *
 * <AUTHOR>
 * @since 2025-02-14
 */
public interface ResourceBelongChangePendingTaskManager {

    /**
     * 保存待执行任务
     *
     * @param resourceBelongChangePendingTaskDO
     */
    void saveTask(ResourceBelongChangePendingTaskDO resourceBelongChangePendingTaskDO);

    /**
     * 删除待执行任务
     *
     * @param taskId
     */
    void deleteTask(Long taskId);

    /**
     * 查询待执行任务列表
     *
     * @param startId
     * @param limit
     * @return
     */
    List<ResourceBelongChangePendingTaskDO> queryByChangeType(List<String> changeTypes, Long startId, int limit);
}
