package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.urimanage.forward.*;
import com.timevale.saas.common.manage.core.model.response.urimanage.forward.RequestForwardDetailResponse;
import com.timevale.saas.common.manage.core.model.response.urimanage.forward.RequestForwardListResponse;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
public interface SaasRequestForwardService {

    /**
     * 查询接口转发及参数替换配置列表
     *
     * @param request
     * @return
     */
    RequestForwardListResponse list(RequestForwardListRequest request);

    /**
     * 查询接口转发及参数替换配置详情
     *
     * @param id
     * @return
     */
    RequestForwardDetailResponse detail(Long id);

    /**
     * 新增接口转发及参数替换配置
     *
     * @param request
     */
    Long addForward(RequestForwardAddRequest request);

    /**
     * 编辑接口转发及参数替换配置
     *
     * @param id
     * @param request
     */
    void updateForward(Long id, RequestForwardUpdateRequest request);

    /**
     * 停用/启用接口转发及参数替换配置
     *
     * @param id
     * @param request
     */
    void updateForwardStatus(Long id, RequestForwardUpdateStatusRequest request);

    /**
     * 批量更新接口转发及参数替换配置状态
     *
     * @param request
     */
    void batchUpdateForwardStatus(BatchRequestForwardUpdateStatusRequest request);
}
