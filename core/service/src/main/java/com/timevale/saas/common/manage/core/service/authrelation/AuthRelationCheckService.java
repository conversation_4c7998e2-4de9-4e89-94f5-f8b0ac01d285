package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationLogDO;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationBuildBeforeCheckInput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationBuildBeforeCheckOutput;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationLogModel;

/**
 * Created by tianlei on 2022/2/28
 */
public interface AuthRelationCheckService {

    AuthRelationBuildBeforeCheckOutput startBeforeCheck(AuthRelationBuildBeforeCheckInput input);

    void buildBeforeCheck(AuthRelationLogModel authRelationLogDO, AuthRelationDO authRelationDO);


    boolean checkOneLevelCount(String parentTenantGid, Integer addCount);
}
