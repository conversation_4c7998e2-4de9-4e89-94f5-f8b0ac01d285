package com.timevale.saas.common.manage.core.service.authrelation;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.base.elock.util.LockUtil;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.saas.common.base.util.ExceptionLogUtil;
import com.timevale.saas.common.manage.common.dal.dao.authrelation.AuthRelationDAO;
import com.timevale.saas.common.manage.common.dal.dao.authrelation.AuthRelationShareConfigDAO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationShareConfigDO;
import com.timevale.saas.common.manage.common.dal.query.authrelation.AuthRelationLogQuery;
import com.timevale.saas.common.manage.common.dal.query.authrelation.AuthRelationQuery;
import com.timevale.saas.common.manage.common.service.constant.AuthRelationShareConfigKeyConstant;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationBizHierarchyTypeEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationLogBizVersionEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationLogStatusEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationStatusEnum;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationDTO;
import com.timevale.saas.common.manage.core.model.enums.YesOrNoEnum;
import com.timevale.saas.common.manage.core.service.authrelation.domain.manager.AuthRelationLogManager;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationLogModel;
import com.timevale.saas.common.manage.core.service.billing.BillingService;
import com.timevale.saas.common.manage.core.service.converter.AuthRelationConverter;
import com.timevale.saas.common.manage.core.service.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/** Created by tianlei on 2022/2/28 */
@Slf4j
@Service
public class AuthRelationTaskService {

    @Autowired private AuthRelationDAO authRelationDAO;


    @Autowired private AuthRelationNotifyService authRelationNotifyService;

    @Autowired private AuthRelationCoreService authRelationCoreService;

    @Autowired private AuthRelationLogManager authRelationLogManager;

    @Autowired private AuthRelationBuildService authRelationBuildService;
    @Autowired private BillingService billingService;
    @Autowired private AuthRelationCacheService authRelationCacheService;
    @Autowired private AuthRelationShareConfigDAO shareConfigDAO;
    @Resource private LockFactory lockFactory;

    public void authRelation10PointNotifyTask() {

        log.info("authRelation 10 point notify start");

        // 有效的， 今天0点 < 到期时间 < 明天0点
        {
            // 到期当天上午10点
            AuthRelationQuery query = new AuthRelationQuery();
            query.setMinEffectiveEndTime(TimeUtil.getToday0Point());
            query.setMaxEffectiveEndTime(TimeUtil.getToday23_59_59Point());
            query.setBizHierarchyTypeList(AuthRelationBizHierarchyTypeEnum.DIRECT_HIGHER_UP_LIST);
            List<AuthRelationDO> list = allDataByQuery(query);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(e -> {

                    String handleCacheKey =  todayExpireHandleKey(e.getParentTenantOid());
                    // 防止定时任务重复跑，导致重复通知
                    if (TedisUtil.get(handleCacheKey) != null) {
                        return;
                    }

                    Long authRelationId = e.getId();
                    try {
                        String parentTenantOid = getParentTenantOid(e);
                        // 填充通知内容
                        log.info("authRelation today expire 10 point notify authRelationId: {}", authRelationId);
                        authRelationNotifyService.authRelationExpire(parentTenantOid, e.getChildTenantName(), "今天");
                        // 一级处理过了缓存1天
                        TedisUtil.set(handleCacheKey, "1", 1, TimeUnit.DAYS);
                    } catch (Exception ex) {
                        ExceptionLogUtil.traceLog(log, ex, "authRelation expire today 10 point failure : {}", authRelationId);
                    }
                });
            }
        }

        // 有效的， 今天0点 < 到期时间 - 15 天 < 明天0点
        {
            AuthRelationQuery query = new AuthRelationQuery();
            query.setMinEffectiveEndTime(DateUtils.addDays(TimeUtil.getToday0Point(), 15));
            query.setMaxEffectiveEndTime(DateUtils.addDays(TimeUtil.getToday23_59_59Point(), 15));
            List<AuthRelationDO> list = allDataByQuery(query);
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(e -> {

                    String handleCacheKey = expireBeforeSomeDayHandleKey(e.getParentTenantOid());
                    // 防止定时任务重复跑，导致重复通知
                    if (TedisUtil.get(handleCacheKey) != null) {
                        return;
                    }
                    Long authRelationId = e.getId();
                    try {

                        String parentTenantOid = getParentTenantOid(e);
                        // 填充通知内容
                        authRelationNotifyService.authRelationExpire(parentTenantOid, e.getChildTenantName(), "15天后");
                        // 一级处理过了缓存1天
                        TedisUtil.set(handleCacheKey, "1", 1, TimeUnit.DAYS);
                    } catch (Exception ex) {
                        ExceptionLogUtil.traceLog(log, ex, "authRelation expire will 15 day 10 point failure : {}", authRelationId);
                    }
                });
            }
        }
    }

    private String getParentTenantOid(AuthRelationDO e) {
        String parentTenantOid = e.getParentTenantOid();
        if (StringUtils.isNotEmpty(e.getAuthTenantGid())) {
            parentTenantOid = e.getAuthTenantOid();
        }
        return parentTenantOid;
    }

    // 定时扫描失效的进行失效
    public void authRelationExpireTask() {
        log.info("authRelation expire start");
        AuthRelationQuery query = new AuthRelationQuery();
        query.setMaxEffectiveEndTime(new Date());
        query.setPageIndex(1);
        query.setPageSize(50);
        List<Integer> statusList = Arrays.asList(AuthRelationStatusEnum.EFFECTIVE.getCode(),
                AuthRelationStatusEnum.INFORMALITY_EFFECTIVE.getCode());
        query.setStatusList(statusList);
        query.setBizHierarchyTypeList(AuthRelationBizHierarchyTypeEnum.DIRECT_HIGHER_UP_LIST);
        // i给定个失败值，防止某个关系一直无变更状态
        for (int i = 1; i < 100; i++) {
            List<AuthRelationDO> list = authRelationDAO.listByQuery(query);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            list.forEach(e -> {
                try {
                    Elock lock = lockFactory.getLock(lockName(e.getId()));
                    if (lock.tryLock(5, TimeUnit.SECONDS)) {
                        try {
                            log.info("auth relation expire id : {}", e.getId());
                            authRelationCoreService.authRelationExpire(e);
                        } finally {
                            lock.unlock();
                        }
                    }
                } catch (Exception ex) {
                    ExceptionLogUtil.traceLog(log, ex, "authRelation expire rescind failure authRelationId : {}", e.getId());
                }
            });
        }
    }

    private String todayExpireHandleKey(String tenantOid) {
        return "authRelationExpirePoint:" + tenantOid;
    }

    private String expireBeforeSomeDayHandleKey(String tenantOid) {
        return "authRelationWillSomeDayExpirePoint:" + tenantOid;
    }

    private List<AuthRelationDO> allDataByQuery(AuthRelationQuery query) {
        if (null == query) {
            return new ArrayList<>();
        }
        query.setPageIndex(1);
        query.setPageSize(50);
        query.setStatusList(Arrays.asList(AuthRelationStatusEnum.EFFECTIVE.getCode()));
        List<AuthRelationDO> allList = new ArrayList<>();
        for (int i = 1; ; i++) {
            query.setPageIndex(i);
            List<AuthRelationDO> list = authRelationDAO.listByQuery(query);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            allList.addAll(list);
            if (list.size() < query.getPageSize()) {
                break;
            }
        }
        return allList;
    }

    /**
     * 定时更新授权记录
     */
//    @Deprecated
//    public void authRelationEffectiveChangeTask() {
//        List<AuthRelationDO> waitEffectiveList = queryList(AuthRelationStatusEnum.WAIT_EFFECTIVE.getCode());
//        //更新待生效的记录为生效中
//        if (CollectionUtils.isNotEmpty(waitEffectiveList)) {
//            for (AuthRelationDO authRelationDO : waitEffectiveList) {
//                if (authRelationDO.getEffectiveStartTime().after(new Date())) {
//                    AuthRelationDO authRelationUpdate = new AuthRelationDO();
//                    authRelationUpdate.setId(authRelationDO.getId());
//                    authRelationUpdate.setStatus(AuthRelationStatusEnum.EFFECTIVE.getCode());
//                    authRelationDAO.updateById(authRelationUpdate);
//                }
//            }
//        }
//
//        List<AuthRelationDO> effectiveList = queryList(AuthRelationStatusEnum.WAIT_EFFECTIVE.getCode());
//        if (CollectionUtils.isEmpty(effectiveList)) {
//            return;
//        }
//        for (AuthRelationDO authRelationDO : effectiveList) {
//            Long authRelationId = authRelationDO.getId();
//            List<AuthRelationLogDO> authRelationLogDOS = authRelationLogDAO.listByAuthRelationId(authRelationId, null, null);
//            if (CollectionUtils.isEmpty(authRelationLogDOS) || authRelationLogDOS.size() <= 1) {
//                continue;
//            }
//            //多条判断是否需要变更授权关系
//            //判断当前时间资源最多的授权记录
//            AuthRelationLogDO temp = null;
//            for (AuthRelationLogDO authRelationLogDO : authRelationLogDOS) {
//                if (temp == null) {
//                    temp = authRelationLogDO;
//                } else {
//                    if (temp.getAuthLevel() < authRelationLogDO.getAuthLevel()) {
//                        // 需要进行替换
//                        if (!authRelationDO.getLastAuthRelationLogId().equals(authRelationLogDO.getId())) {
//                            AuthRelationDO authRelationUpdate = new AuthRelationDO();
//                            authRelationUpdate.setId(authRelationDO.getId());
//                            authRelationUpdate.setLastAuthRelationLogId(authRelationLogDO.getId());
//                            authRelationUpdate.setEffectiveStartTime(authRelationLogDO.getEffectiveStartTime());
//                            authRelationUpdate.setEffectiveEndTime(authRelationLogDO.getEffectiveEndTime());
//                            authRelationUpdate.setAuthLevel(authRelationLogDO.getAuthLevel());
//                            authRelationDAO.updateById(authRelationUpdate);
//                        }
//                    }
//                }
//            }
//        }
//    }

    /**
     * 从授权记录，找出最优的一条授权进行替换
     */
    public void changeAuthRelationFromAuthRelationLogTask(String childTenantGid) {

        Date now = new Date();
        AuthRelationLogQuery logQuery = new AuthRelationLogQuery();
        if (StringUtils.isNotBlank(childTenantGid)) {
            logQuery.setChildTenantGid(childTenantGid);
        }
        logQuery.setBizVersion(AuthRelationLogBizVersionEnum.ONE.getCode());
        logQuery.setStatusList(Arrays.asList(AuthRelationLogStatusEnum.CONTRACT_SIGN_SUCCESS.getCode(),
                AuthRelationLogStatusEnum.LAST_SUCCESS.getCode()));
        logQuery.setMaxEffectiveStartTime(now);
        logQuery.setMinEffectiveEndTime(now);
        logQuery.setPageIndex(1);
        logQuery.setPageSize(100);
        for (int i = 1 ; i < 1000 ; i ++) {
            logQuery.setPageIndex(i);
            List<AuthRelationLogModel> logModels = authRelationLogManager.listByQuery(logQuery);
            if (CollectionUtils.isEmpty(logModels)) {
                break;
            }
            // 业务逻辑
            Set<Long> authRelationIds = new HashSet<>();
            for (AuthRelationLogModel logModel : logModels) {
                if (null != logModel.getAuthRelationId()) {
                    authRelationIds.add(logModel.getAuthRelationId());
                }
            }
            List<AuthRelationDO> authRelationDOList = authRelationDAO.listByIds(authRelationIds).stream()
                            .filter(elm -> YesOrNoEnum.NO.getCode().equals(elm.getDeleted()))
                            .filter(elm -> AuthRelationStatusEnum.EFFECTIVE.getCode().equals(elm.getStatus()) ||
                                    AuthRelationStatusEnum.WAIT_EFFECTIVE.getCode().equals(elm.getStatus()))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(authRelationDOList)) {
                continue;
            }
            authRelationDOList.forEach(elm -> {
                AuthRelationLogModel bestAuthRelationLog = authRelationBuildService.getBestAuthRelationLog(null,elm);
                if(bestAuthRelationLog == null){
                    return;
                }
                try {
                    Elock lock = lockFactory.getLock(lockName(elm.getId()));
                    if (lock.tryLock(5, TimeUnit.SECONDS)) {
                        try {
                            log.info("auth relation may change best authRelation authRelationLogId authRelationId: {}", elm.getId());
                            authRelationBuildService.changeBestAuthRelationLog(elm, bestAuthRelationLog);
                        } finally {
                            lock.unlock();
                        }
                    }
                } catch (Exception ex) {
                    ExceptionLogUtil.traceLog(log, ex, "auth relation may change failure authRelationId : {}", elm.getId());
                }
            });
            if (logModels.size() < logQuery.getPageSize()) {
                break;
            }
        }
    }

    private String lockName(Long authRelationLogId) {
        return LockUtil.getLockName("authRelationChangeTaskLock:" + authRelationLogId);
    }

//    private List<AuthRelationDO> queryList(Integer status) {
//        List<AuthRelationDO> allList = new ArrayList<>();
//        AuthRelationQuery query = new AuthRelationQuery();
//        query.setPageIndex(1);
//        query.setPageSize(50);
//        query.setStatusList(Collections.singletonList(status));
//        for (int i = 1; ; i++) {
//            query.setPageIndex(i);
//            List<AuthRelationDO> list = authRelationDAO.listByQuery(query);
//            if (CollectionUtils.isEmpty(list)) {
//                break;
//            }
//            allList.addAll(list);
//            if (list.size() < query.getPageSize()) {
//                break;
//            }
//        }
//        return allList;
//    }

    public void syncRelationBillingTask(
            Integer pageSize,
            Integer syncSize,
            Integer configSyncSize,
            boolean hasInvalid,
            String syncChildGid,
            String syncParentGid) {
        syncSize = syncSize != null ? syncSize : 10;
        configSyncSize = configSyncSize != null ? configSyncSize : 10;
        pageSize = pageSize != null ? pageSize : 100;
        Integer pageNum = 1;
        AuthRelationQuery query = new AuthRelationQuery();
        query.setPageSize(pageSize);
        // 主子企业只能指定同步一种
        if (StringUtils.isNotBlank(syncParentGid)) {
            query.setParentTenantGid(syncParentGid);
        } else if (StringUtils.isNotBlank(syncChildGid)) {
            query.setChildTenantGid(syncChildGid);
        }
        List<Integer> statusList = new ArrayList<>();
        statusList.add(AuthRelationStatusEnum.EFFECTIVE.getCode());
        if (hasInvalid) {
            statusList.add(AuthRelationStatusEnum.INVALID.getCode());
        }
        query.setStatusList(statusList);
        query.setOrderByClause("order by id asc");

        List<AuthRelationDO> authRelationList;
        Set<String> childGid = new HashSet<>();
        List<AuthRelationDO> allEffectiveRelation = new ArrayList<>();
        do {
            query.setPageIndex(pageNum);
            authRelationList = authRelationDAO.listByQuery(query);

            List<AuthRelationDTO> removeRelation = new ArrayList<>();
            authRelationList.forEach(
                    p -> {
                        AuthRelationDTO dto = AuthRelationConverter.do2DTO(p);
                        if (p.getStatus().equals(AuthRelationStatusEnum.EFFECTIVE.getCode())) {
                            allEffectiveRelation.add(p);
                            childGid.add(p.getChildTenantGid());
                        } else {
                            removeRelation.add(dto);
                        }
                    });

            // 推送失效数据
            pushRelationConfig(false, removeRelation, configSyncSize);
            pushRelation(false, removeRelation, syncSize);
            pageNum++;
            log.info("sync billing pageNo : {} , pageSize : {}", pageNum, authRelationList.size());
        } while (authRelationList.size() == pageSize);
        // 处理有效数据

        if (CollectionUtils.isEmpty(allEffectiveRelation)
                || (StringUtils.isNotBlank(syncParentGid)
                        && Objects.equals(
                                allEffectiveRelation.get(0).getAuthTenantGid(), syncParentGid))) {
            log.info("no data or syncParentGid : {} not parentOid stop sync", syncParentGid);
        }

        if (CollectionUtils.isNotEmpty(childGid)) {
            List<AuthRelationDTO> addRelation =
                    childGid.stream()
                            .map(
                                    p ->
                                            authRelationCacheService
                                                    .getTopEffectiveAuthRelationByAuthRelationList(
                                                            allEffectiveRelation, p))
                            .map(AuthRelationConverter::do2DTO)
                            .collect(Collectors.toList());
            List<List<AuthRelationDTO>> groupData = Lists.partition(addRelation, 1000);
            for (List<AuthRelationDTO> list : groupData) {
                pushRelation(true, list, syncSize);
                // 查询签署分享配置
                List<AuthRelationShareConfigDO> configList =
                        shareConfigDAO.listByAuthRelationIdsAndConfigKey(
                                list.stream()
                                        .map(AuthRelationDTO::getAuthRelationId)
                                        .collect(Collectors.toList()),
                                AuthRelationShareConfigKeyConstant.SHARE_SIGN);
                // 获取开启配置id
                Set<Long> idList =
                        configList.stream()
                                .filter(config -> Boolean.parseBoolean(config.getConfigValue()))
                                .map(AuthRelationShareConfigDO::getAuthRelationId)
                                .collect(Collectors.toSet());
                // 过滤出需要推送的关系对象
                List<AuthRelationDTO> pushRelationConfig =
                        list.stream()
                                .filter(p -> idList.contains(p.getAuthRelationId()))
                                .collect(Collectors.toList());
                pushRelationConfig(true, pushRelationConfig, configSyncSize);
            }
        }
    }

    private void pushRelation(boolean syncType, List<AuthRelationDTO> relationList, int syncSize) {
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }
        log.info(
                "task push billing relation {} data count {} ,data json {}",
                syncType ? "add" : "remove",
                relationList.size(),
                JSONObject.toJSONString(
                        relationList.stream()
                                .map(p -> p.getParentTenantGid() + " - " + p.getChildTenantGid())
                                .collect(Collectors.toList())));
        List<List<AuthRelationDTO>> pushParamList = Lists.partition(relationList, syncSize);
        pushParamList.forEach(
                p -> {
                    try {
                        billingService.syncUserAuth(syncType, p);
                    } catch (Exception e) {
                        log.warn("task sync error msg : {}", e.getMessage());
                    }
                });
    }

    private void pushRelationConfig(
            boolean syncType, List<AuthRelationDTO> relationList, int syncSize) {
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }
        log.info(
                "task push billing config {} data count {} ,data json {}",
                syncType ? "add" : "remove",
                relationList.size(),
                JSONObject.toJSONString(
                        relationList.stream()
                                .map(p -> p.getParentTenantGid() + " - " + p.getChildTenantGid())
                                .collect(Collectors.toList())));
        Map<String, List<AuthRelationDTO>> relationMap =
                relationList.stream()
                        .collect(Collectors.groupingBy(AuthRelationDTO::getParentTenantGid));
        for (List<AuthRelationDTO> list : relationMap.values()) {
            List<List<AuthRelationDTO>> pushParamList = Lists.partition(list, syncSize);
            pushParamList.forEach(
                    p -> {
                        try {
                            billingService.syncOrderAuthRules(syncType, p);
                        } catch (Exception e) {
                            log.warn("task sync error msg : {}", e.getMessage());
                        }
                    });
        }
    }
}
