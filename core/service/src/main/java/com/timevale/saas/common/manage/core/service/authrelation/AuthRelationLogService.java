package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationLogDO;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationCreateLogInput;
import com.timevale.saas.common.manage.common.service.model.input.authrelation.AuthRelationUpdateLogInput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationCreateLogOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationLogDetailOutput;
import com.timevale.saas.common.manage.common.service.model.output.authrelation.AuthRelationUpdateLogOutput;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationLogModel;

import java.util.List;

/**
 * Created by tian<PERSON><PERSON> on 2022/2/17
 */
public interface AuthRelationLogService {

    AuthRelationCreateLogOutput createAuthRelationLog(AuthRelationCreateLogInput input);

    AuthRelationUpdateLogOutput updateAuthRelationLog(AuthRelationUpdateLogInput input);

    AuthRelationLogModel getAuthRelationLogByProcessId(String processId);

    AuthRelationLogDO getAuthRelationLogByAuthRelationId(Long authRelationId);

    List<AuthRelationLogDO> getAuthRelationLogByAuthRelationIds(List<Long> authRelationIds, List<Integer> statusList);

    AuthRelationLogDetailOutput getAuthRelationLogById(Long authRelationLogId);

    List<AuthRelationLogDO> getAuthRelationLogByIds(List<Long> authRelationLogIds);

    List<AuthRelationLogModel> listAuthRelationGid(String authTenantGid, String childTenantGid, String currTenantGid);

    AuthRelationLogDO getAuthRelationLogByFlowId(String flowId);

    Boolean checkExistFlowId(String flowId);

}
