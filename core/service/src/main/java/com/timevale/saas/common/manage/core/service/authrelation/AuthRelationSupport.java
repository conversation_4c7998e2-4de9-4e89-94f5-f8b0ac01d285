package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationLogBizVersionEnum;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationLogStatusEnum;
import com.timevale.saas.common.manage.core.service.authrelation.domain.manager.AuthRelationLogManager;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationLogModel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/5/26 15:15
 */
@Component
public class AuthRelationSupport {

    private static final int LIMIT_SIZE = 100;
    private static final List<Integer> MATH_STATUS_LIST =
            Arrays.asList(AuthRelationLogStatusEnum.CONTRACT_SIGN_SUCCESS.getCode(),
            AuthRelationLogStatusEnum.LAST_SUCCESS.getCode());
    private static final List<Integer> WAIT_AUTH_STATUS_LIST =
            Arrays.asList(AuthRelationLogStatusEnum.CONTRACT_START_COMPLETE.getCode());
    private static final List<Integer> ALL_MATCH_STATUS_LIST = new ArrayList<>();
    static {
        ALL_MATCH_STATUS_LIST.addAll(MATH_STATUS_LIST);
        ALL_MATCH_STATUS_LIST.addAll(WAIT_AUTH_STATUS_LIST);
    }

    @Autowired
    private AuthRelationLogManager authRelationLogManager;


    public AuthRelationLogModel getBestAuthRelationLog(Long authRelationId, AuthRelationLogModel tempAddMatch) {
        return getBestAuthRelationLog(authRelationId, false, null, tempAddMatch);
    }

    public AuthRelationLogModel getBestAuthRelationLog(List<AuthRelationLogModel> logModels) {
        if (CollectionUtils.isEmpty(logModels)) {
            return null;
        }
        return getBestAuthRelationLog(logModels, false, null);
    }

    public AuthRelationLogModel getBestAuthRelationLog(Long authRelationId, boolean onlyFindEffective,
                                                       Long excludeAuthRelationLogId, AuthRelationLogModel tempAddMatch) {
        List<AuthRelationLogModel> logModels = listNotEffectiveAuthRelationLog(authRelationId, false).stream()
                .filter(elm -> null == excludeAuthRelationLogId || !Objects.equals(elm.getId(), excludeAuthRelationLogId))
                .collect(Collectors.toList());
        return getBestAuthRelationLog(logModels, onlyFindEffective, tempAddMatch);
    }




    /**
     * 待生效，待授权的数据
     */
    public List<AuthRelationLogModel> listNotEffectiveAuthRelationLog(Long authRelationId, boolean includeWaitAuth) {

        return listNotEffectiveAuthRelationLog(Arrays.asList(authRelationId), includeWaitAuth);
    }

    public List<AuthRelationLogModel> listNotEffectiveAuthRelationLog(List<Long> authRelationIds, boolean includeWaitAuth) {

        List<Integer> statusList = includeWaitAuth ? ALL_MATCH_STATUS_LIST: MATH_STATUS_LIST;
        return authRelationLogManager.listAvailable(authRelationIds, statusList,
                AuthRelationLogBizVersionEnum.ONE.getCode(), LIMIT_SIZE);
    }

    public boolean waitAuth(Integer status) {
        return WAIT_AUTH_STATUS_LIST.contains(status);
    }

    public static AuthRelationLogModel getBestAuthRelationLog(List<AuthRelationLogModel> logModels, boolean onlyFindEffective,
                                                              AuthRelationLogModel tempAddMatch) {
        Date now = new Date();
        List<AuthRelationLogModel> firstStepMatchLog = logModels.stream()
                .filter(log ->
                        // 时间过滤，未过期的都要找出来
                        onlyFindEffective ? (now.compareTo(log.getEffectiveStartTime()) >= 0 || now.compareTo(log.getEffectiveEndTime()) < 0) :
                                now.compareTo(log.getEffectiveEndTime()) < 0
                ).filter(log ->
                        // 状态过滤
                        AuthRelationLogStatusEnum.CONTRACT_SIGN_SUCCESS.getCode().equals(log.getStatus()) ||
                                AuthRelationLogStatusEnum.LAST_SUCCESS.getCode().equals(log.getStatus())
                )
                .collect(Collectors.toList());
        // 1.先找有效的，同时有效，取授权等级大的，授权等级相同，就取结束时间晚的
        List<AuthRelationLogModel> effective = new ArrayList<>();
        // 2.无有效的找开始时间最近的，开始时间相同的，取授权等级大的，授权等级相同的，取结束时间晚的
        List<AuthRelationLogModel> unEffective = new ArrayList<>();

        for (AuthRelationLogModel log : firstStepMatchLog) {
            if (null == log.getEffectiveStartTime() || null == log.getEffectiveEndTime()) {
                continue;
            }
            if (now.compareTo(log.getEffectiveEndTime()) >= 0) {
                // 过期的不要
                continue;
            }
            if (!(AuthRelationLogStatusEnum.CONTRACT_SIGN_SUCCESS.getCode().equals(log.getStatus()) ||
                    AuthRelationLogStatusEnum.LAST_SUCCESS.getCode().equals(log.getStatus()))) {
                continue;
            }
            if (now.compareTo(log.getEffectiveStartTime()) >= 0) {
                effective.add(log);
            } else {
                unEffective.add(log);
            }
        }

        if (null != tempAddMatch) {
            if (tempAddMatch.getEffectiveStartTime().compareTo(now) > 0) {
                unEffective.add(tempAddMatch);
            } else {
                effective.add(tempAddMatch);
            }
        }

        if (CollectionUtils.isNotEmpty(effective)) {
            effective.sort(
                    Comparator.comparing(AuthRelationLogModel::getEffectiveEndTime)
                            .reversed()
                            .thenComparing(AuthRelationLogModel::getCreateTime)
                            .reversed());
            return effective.get(0);
        }
        if (CollectionUtils.isEmpty(unEffective)) {
            return null;
        }
        unEffective.sort(Comparator.comparing(AuthRelationLogModel::getEffectiveStartTime)
                .thenComparing(AuthRelationLogModel::getEffectiveEndTime).reversed());
        return unEffective.get(0);
    }
    

}
