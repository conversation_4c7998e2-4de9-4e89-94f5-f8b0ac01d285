package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.OrganizationInfoRequest;
import com.timevale.saas.common.manage.core.model.response.QueryOrgInfoListResponse;
import com.timevale.saas.common.manage.core.model.response.QueryOrganizationInfoResponse;

/**
 * <AUTHOR>
 * @since 2021-02-01
 */
public interface OrganizationService {

    QueryOrgInfoListResponse queryOrgInfosByName(String orgName);

    /**
     * 获取企业相关信息
     *
     * @param request 请求参数
     * @return QueryOrganizationInfoResponse
     */
    QueryOrganizationInfoResponse queryInfo(String  clientId, OrganizationInfoRequest request);
}
