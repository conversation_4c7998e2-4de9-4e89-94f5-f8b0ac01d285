package com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager.impl;

import com.timevale.saas.common.manage.common.dal.dao.ResourceBelongChangePendingTaskDAO;
import com.timevale.saas.common.manage.common.dal.dataobject.ResourceBelongChangePendingTaskDO;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager.ResourceBelongChangePendingTaskManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-02-14
 */
@Service
public class ResourceBelongChangePendingTaskManagerImpl implements ResourceBelongChangePendingTaskManager {

    @Autowired
    ResourceBelongChangePendingTaskDAO jobPendingTaskDAO;

    @Override
    public void saveTask(ResourceBelongChangePendingTaskDO resourceBelongChangePendingTaskDO) {
        jobPendingTaskDAO.insert(resourceBelongChangePendingTaskDO);
    }

    @Override
    public void deleteTask(Long taskId) {
        jobPendingTaskDAO.deleteById(taskId);
    }

    @Override
    public List<ResourceBelongChangePendingTaskDO> queryByChangeType(List<String> changeTypes, Long startId, int limit) {
        return jobPendingTaskDAO.query(changeTypes, startId, limit);
    }
}
