package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.domain.DomainWhiteAddRequest;
import com.timevale.saas.common.manage.core.model.response.domain.DomainWhiteListResponse;

/**
 * <AUTHOR>
 * @since 2022/3/8
 */
public interface AppDomainWhiteService {

    /**
     * 获取域名白名单列表信息
     *
     * @param request 请求参数
     * @return DomainWhiteListResponse
     */
    DomainWhiteListResponse getList(Integer pageSize, Integer pageNo);

    /**
     * 新增域名白名单
     *
     * @param request 请求参数
     * @return Long
     */
    Long add(DomainWhiteAddRequest request);

    /**
     * 删除白名单信息
     *
     * @param id 问卷id
     */
    void del(Long id);

    /**
     * 校验当前地址是否是白名单
     *
     * @param url 域名地址
     * @return true or false
     */
    Boolean check(String url);
}
