package com.timevale.saas.common.manage.core.service.resourcebelong;

import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongBatchDeleteInput;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongBatchSaveInput;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongGetInput;
import com.timevale.saas.common.manage.common.service.model.output.resourcebelong.ResourceBelongBatchSaveOutput;
import com.timevale.saas.common.manage.common.service.model.output.resourcebelong.ResourceBelongDTO;
import com.timevale.saas.common.manage.common.service.model.output.resourcebelong.ResourceBelongGetOutput;
import com.timevale.saas.common.manage.core.service.mq.model.DeptChangeMessage;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/4 10:50
 */
public interface ResourceBelongService {

    /**
     * 批量保存资源归属
     */
    void batchSaveResourceBelong(ResourceBelongBatchSaveInput input);

    /**
     * 根据资源获取资源归属
     */
    List<ResourceBelongDTO> getResourceBelong(ResourceBelongGetInput input);

    /**
     * 资源删除
     */
    void deleteResourceBelong(ResourceBelongBatchDeleteInput input);

    /**
     * 部门删除
     */
    void deptDelete(DeptChangeMessage message);

    /**
     * 部门修改父类
     */
//    void deptChangeParent(DeptChangeMessage message);

    /**
     * 执行部门变更定时任务
     */
    void executeDeptChangeTask();

    /**
     * 真正处理部门变更消息
     */
    void processDeptChangeMessage(DeptChangeMessage deptChangeMessage);

}
