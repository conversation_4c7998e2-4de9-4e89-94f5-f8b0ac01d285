package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.core.model.model.authrelation.AuthRelationGetAuthLevelRequest;

import java.util.List;

/**
 * Created by tianlei on 2022/5/27
 */
public interface AuthRelationAuthLevelService {



    Integer getAuthLevel(AuthRelationGetAuthLevelRequest request);

    /**
     * 根据业务场景获取level
     */
    Integer getNeedAuthLevelAndCheck(String useScene);

    /**
     * 获取最高等级level
     */
    Integer maxAuthLevel();

    /**
     * 获取最低等级level
     */
    Integer minAuthLevel();

    /**
     * 获取所有等级
     */
    List<Integer> allAuthLevel();

}
