package com.timevale.saas.common.manage.core.service.authrelation.domain.manager;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationShareConfigDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2024/2/2 15:48
 */
public interface AuthRelationShareConfigManager {

     AuthRelationShareConfigDO getByAuthRelationIdsAndConfigKey(Long authRelationId, String configKey);
}
