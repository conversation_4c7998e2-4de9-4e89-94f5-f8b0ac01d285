package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationShareConfigDO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/7/22 19:34
 */
public interface AuthRelationSyncBillCenterService {


    /**
     * 监控下异常数据
     */
    void monitorErrorData(Long authRelationId);

    /**
     * 解除关系同步到计费
     */
    void expireRescind(AuthRelationDO currentAuthRelationDO, List<AuthRelationDO> multiWriteAuthRelationDO);


    /**
     * 过期解除关系，替换另外一有效记录
     */
    void expireRescindChangeOtherBestLogSync(List<AuthRelationShareConfigDO> shareConfigUpdateData,
                                             AuthRelationDO currentAuthRelationDO,
                                             List<AuthRelationDO> multiWriteAuthRelationDO);
}
