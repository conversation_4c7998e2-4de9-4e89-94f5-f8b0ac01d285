package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.response.QrCodeResponse;
import com.timevale.saas.common.manage.core.model.response.SMSTemplateResponse;

/**
 * <AUTHOR>
 * @date 2022/6/8
 **/
public interface ToolCenterService {

    /**
     * 获取微信二维码
     * @param sceneId
     * @param sceneValue
     * @param expire
     * @return
     */
    QrCodeResponse getWeChatQrcode(String sceneId, String sceneValue, Long expire);

    /**
     * 获取短信模版
     * @param templateId
     * @return
     */
    SMSTemplateResponse getSMSTemplate(String templateId);
}
