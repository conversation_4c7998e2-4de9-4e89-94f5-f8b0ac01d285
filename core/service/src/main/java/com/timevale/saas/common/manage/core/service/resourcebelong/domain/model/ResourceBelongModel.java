package com.timevale.saas.common.manage.core.service.resourcebelong.domain.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/3 20:38
 */
@Data
public class ResourceBelongModel {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源类型 合同 相对方
     */
    private Integer resourceType;

    /**
     * 企业oid
     */
    private String subjectOid;

    /**
     * 企业gid
     */
    private String subjectGid;

    /**
     * 直属部门
     */
    private String directDeptId;

    /**
     * 完整的部门路径 直属部门 -> 上级 -> xxx -> 顶级
     */
    private List<String> deptPath;

    /**
     * 建立关系的原因
     */
    private String reason;

    // Getters and Setters (省略)
}
