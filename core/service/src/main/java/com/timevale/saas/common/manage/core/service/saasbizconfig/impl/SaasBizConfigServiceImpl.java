package com.timevale.saas.common.manage.core.service.saasbizconfig.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.timevale.saas.common.manage.common.dal.dao.SaasBizConfigDAO;
import com.timevale.saas.common.manage.common.dal.dataobject.SaasBizConfigDO;
import com.timevale.saas.common.manage.common.service.model.bean.saasbizconfig.SaasBizConfig;
import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.ChangeSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.input.saasbizconfig.GetSaasBizConfigsInput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.ChangeSaasBizConfigsOutput;
import com.timevale.saas.common.manage.common.service.model.output.saasbizconfig.GetSaasBizConfigsOutput;
import com.timevale.saas.common.manage.core.service.saasbizconfig.SaasBizConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-07-31 13:35
 */
@Service
public class SaasBizConfigServiceImpl implements SaasBizConfigService {
    @Autowired private SaasBizConfigDAO saasBizConfigDAO;
    @Autowired private TransactionTemplate transactionTemplate;

    @Override
    public GetSaasBizConfigsOutput getBizConfigs(GetSaasBizConfigsInput input) {
        List<SaasBizConfigDO> saasBizConfigs = saasBizConfigDAO.queryByKeysOid(input.getKey(), input.getOid());
        GetSaasBizConfigsOutput output = new GetSaasBizConfigsOutput();
        output.setOid(input.getOid());
        output.setGid(saasBizConfigs.stream()
                .filter(saasBizConfig -> saasBizConfig.getGid() != null)
                .findAny()
                .map(SaasBizConfigDO::getGid)
                .orElse(null));


        output.setSaasBizConfigList(saasBizConfigs.stream()
                .map(list -> {
                    SaasBizConfig saasBizConfig = new SaasBizConfig();
                    saasBizConfig.setKey(list.getKey());
                    saasBizConfig.setConfigInfo(list.getConfigInfo());
                    return saasBizConfig;
                })
                .collect(Collectors.toList()));

        return output;
    }

    @Override
    public ChangeSaasBizConfigsOutput changeBizConfigs(ChangeSaasBizConfigsInput input) {
        // 将所有要更新或新增的数据提取出来，便于后续分类更新或是新增
        List<SaasBizConfigDO> inputList =
                input.getSaasBizConfigList().stream()
                        .map(
                                bizConfig -> {
                                    SaasBizConfigDO saasBizConfig = new SaasBizConfigDO();
                                    saasBizConfig.setKey(bizConfig.getKey());
                                    saasBizConfig.setConfigInfo(bizConfig.getConfigInfo());
                                    saasBizConfig.setOid(input.getOid());
                                    saasBizConfig.setGid(input.getGid());
                                    return saasBizConfig;
                                })
                        .collect(Collectors.toList());

        // 批量查询一次，将查询结果放到set中，数据库中唯一索引时oid与key，传入的input中oid是相同，因此只需存储key
        Set<String> set =
                saasBizConfigDAO
                        .queryByKeysOid(
                                input.getSaasBizConfigList().stream()
                                        .map(SaasBizConfig::getKey)
                                        .collect(Collectors.toList()),
                                input.getOid())
                        .stream()
                        .map(SaasBizConfigDO::getKey)
                        .collect(Collectors.toSet());

        // key在set中的放入修改列表，反之放入新增列表
        Map<Boolean, List<SaasBizConfigDO>> partitionedMap =
                inputList.stream()
                        .collect(Collectors.partitioningBy(list -> set.contains(list.getKey())));
        List<SaasBizConfigDO> insertList = partitionedMap.get(false);
        List<SaasBizConfigDO> updateList = partitionedMap.get(true);

        // 有则修改，无则添加
        transactionTemplate.execute(
                status -> {
                    if (!CollectionUtils.isEmpty(updateList)) {
                        saasBizConfigDAO.batchUpdateByUniOidType(updateList);
                    }
                    if (!CollectionUtils.isEmpty(insertList)) {
                        saasBizConfigDAO.batchInsertByUniOidType(insertList);
                    }
                    return null;
                });

        return new ChangeSaasBizConfigsOutput(true);
    }
}
