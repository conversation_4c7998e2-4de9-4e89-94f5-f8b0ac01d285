package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.common.service.model.input.ScrollTenantByFuncCodeInput;
import com.timevale.saas.common.manage.common.service.model.input.ScrollTenantVipInput;
import com.timevale.saas.common.manage.common.service.model.input.vip.VipRecycleInput;
import com.timevale.saas.common.manage.core.model.request.vip.SaveAccountVipRequest;
import com.timevale.saas.common.manage.core.model.response.vip.QueryAccountVipResponse;
import com.timevale.saas.common.manage.core.model.response.vip.QueryCommodityClassifyResponse;
import com.timevale.saas.common.manage.core.model.response.vip.bean.AccountVip;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipAndFunctionBean;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipFunctionBean;
import com.timevale.saas.common.manage.core.model.response.vip.bean.VipLevel;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-08-20
 */
public interface SaasVipService {

    /**
     * 是否使用当前service
     *
     * @return
     */
    boolean useService();

    /**
     * 保存用户计费会员订单数据
     *
     * @param request
     */
    boolean saveAccountBillVip(SaveAccountVipRequest request);

    /**
     * 作废计费会员订单数据
     * @param orderId 订单id
     */
    void cancelAccountBillVip(String orderId);

    /**
     * 赠送会员
     *
     * @param gid
     * @param level
     * @param effectiveTo
     */
    void presentAccountVip(String gid, Integer level, Date effectiveTo);

    /**
     * 取消赠送
     *
     * @param gid
     * @param vipCode
     */
    void cancelPresent(String gid, String vipCode);

    /**
     * 会员版本回收
     * @param vipRecycleInput
     */
    void vipRecycle(VipRecycleInput vipRecycleInput);

    /**
     * 主要用于接口层功能权限校验，可通过开关限制是否校验，不校验默认接口功能有权限
     *
     * @param accountId
     * @param functionCode
     * @return
     */
    boolean checkFunctionValid(String accountId, String functionCode);

    /**
     * 获取指定功能信息
     *
     * @param accountId
     * @param functionCode
     * @return
     */
    VipAndFunctionBean queryVipFunctionInfo(String accountId, String functionCode);

    /**
     * 根据用户gid获取指定功能信息
     *
     * @param accountGid
     * @param functionCode
     * @return
     */
    VipAndFunctionBean queryVipFunctionInfoByGid(String accountGid, String functionCode);

    /**
     * 获取用户所有功能信息
     *
     * @param accountId 账号id
     * @return 会员功能信息
     */
    List<VipFunctionBean> queryVipFunctions(String accountId);

    /**
     * 获取用户会员信息
     *
     * @param accountId
     * @param withFuncs
     * @return
     */
    QueryAccountVipResponse queryAccountVip(String accountId, boolean withFuncs);

    /**
     * 获取用户会员信息 根据 gid
     *
     * @param accountGid gid
     * @param withFuncs 是否查询fun
     * @return 会员信息
     */
    QueryAccountVipResponse queryAccountVipByGid(String accountGid, boolean withFuncs);

    /**
     * 根据用户gid查询用户自身的会员版本信息
     *
     * @param accountGid
     * @return
     */
    AccountVip queryAccountVip(String accountGid);

    /**
     * @param allTenantGids
     * @param n
     * @return
     */
    List<QueryAccountVipResponse> filterTenantsExpireInNDay(List<String> allTenantGids, Integer n);

    /**
     * 获取商品分类
     *
     * @return
     */
    QueryCommodityClassifyResponse queryCommodityClassify();

    /**
     * 获取vip最小支持的功能版本，只包含saas可可购买版本
     *
     * @param functionCode
     * @return
     */
    VipLevel getVipSatisfyMinVersion(String functionCode);

    /**
     * 查询满足指定会员等级列表的企业
     *
     * @param input
     * @return
     */
    List<AccountVip> scrollTenantByLevels(ScrollTenantVipInput input);

    /**
     * 查询支持指定功能的会员版本对应的企业列表
     *
     * @param input
     * @return
     */
    List<AccountVip> scrollTenantByFuncCode(ScrollTenantByFuncCodeInput input);

    /** 删除 saas_vip_function 缓存 */
    void deleteLevelVipFunctionCache(List<Integer> levels);

    /**
     * 校验用户是否存在支持指定功能的会员版本的有效下单/赠送记录
     *
     * @param gid
     * @param functionCode
     * @return
     */
    boolean checkAccountFunctionVipRecordExists(String gid, String functionCode);

}
