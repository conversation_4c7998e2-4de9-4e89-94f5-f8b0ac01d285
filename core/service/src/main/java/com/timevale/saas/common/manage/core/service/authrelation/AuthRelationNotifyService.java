package com.timevale.saas.common.manage.core.service.authrelation;

/**
 * Created by tian<PERSON><PERSON> on 2022/2/28
 */
public interface AuthRelationNotifyService {

    void sendAuthRelationBuildNotify(String parentTenantOid, String childTenantOid);

    // 主企业解除授权
    void parentRescindAuthRelation(String parentTenantOid, String childTenantOid);

    // 关联企业解除授权
    void childRescindAuthRelation(String parentTenantOid, String childTenantOid);

    // 授权即将到期
    void authRelationWillExpire(String parentTenantOid, String childTenantName);

    // 授权到期
    void authRelationExpire(String parentTenantOid, String childTenantName, String appointedDay);

    void rescindAuthRelation(String initiatorName, String notifyName, String notifyOid);

}
