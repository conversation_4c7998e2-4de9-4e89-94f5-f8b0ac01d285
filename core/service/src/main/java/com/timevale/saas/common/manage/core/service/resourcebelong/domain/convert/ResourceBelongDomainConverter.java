package com.timevale.saas.common.manage.core.service.resourcebelong.domain.convert;

import com.alibaba.fastjson.JSON;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.dal.dataobject.resourcebelong.ResourceBelongDO;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.model.ResourceBelongModel;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/4 11:18
 */
public class ResourceBelongDomainConverter {


    public static ResourceBelongDO model2do(ResourceBelongModel data) {
        if (null == data) {
            return null;
        }
        ResourceBelongDO afterData = new ResourceBelongDO();
        afterData.setId(data.getId());
        afterData.setCreateTime(data.getCreateTime());
        afterData.setUpdateTime(data.getUpdateTime());
        afterData.setResourceId(data.getResourceId());
        afterData.setResourceType(data.getResourceType());
        afterData.setSubjectOid(data.getSubjectOid());
        afterData.setSubjectGid(data.getSubjectGid());
        afterData.setDirectDeptId(data.getDirectDeptId());
        if (CollectionUtils.isNotEmpty(data.getDeptPath())) {
            afterData.setDeptPath(JSON.toJSONString(data.getDeptPath()));
        }
        afterData.setReason(data.getReason());
        return afterData;
    }


    public static List<ResourceBelongDO> model2do(List<ResourceBelongModel> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        List<ResourceBelongDO> afterDataList = new ArrayList();
        for (ResourceBelongModel data : dataList) {
            afterDataList.add(model2do(data));
        }
        return afterDataList;
    }


    public static ResourceBelongModel do2Model(ResourceBelongDO data) {
        if (null == data) {
            return null;
        }
        ResourceBelongModel afterData = new ResourceBelongModel();
        afterData.setId(data.getId());
        afterData.setCreateTime(data.getCreateTime());
        afterData.setUpdateTime(data.getUpdateTime());
        afterData.setResourceId(data.getResourceId());
        afterData.setResourceType(data.getResourceType());
        afterData.setSubjectOid(data.getSubjectOid());
        afterData.setSubjectGid(data.getSubjectGid());
        afterData.setDirectDeptId(data.getDirectDeptId());
        if (StringUtils.isNotBlank(data.getDeptPath())) {
            afterData.setDeptPath(JSON.parseArray(data.getDeptPath(), String.class));
        }
        afterData.setReason(data.getReason());
        return afterData;
    }


    public static List<ResourceBelongModel> do2Model(List<ResourceBelongDO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        List<ResourceBelongModel> afterDataList = new ArrayList();
        for (ResourceBelongDO data : dataList) {
            afterDataList.add(do2Model(data));
        }
        return afterDataList;
    }

}
