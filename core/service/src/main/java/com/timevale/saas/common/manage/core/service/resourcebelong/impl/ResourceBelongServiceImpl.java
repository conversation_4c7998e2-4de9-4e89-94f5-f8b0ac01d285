package com.timevale.saas.common.manage.core.service.resourcebelong.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.timevale.saas.common.manage.common.dal.dataobject.ResourceBelongChangePendingTaskDO;
import com.timevale.saas.common.manage.common.service.constant.ResourceBelongTopicConstant;
import com.timevale.saas.common.manage.common.service.message.resourcebelong.ResourceBelongChangeMessage;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongBatchDeleteInput;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongBatchSaveInput;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongGetInput;
import com.timevale.saas.common.manage.common.service.model.input.resourcebelong.ResourceBelongSaveInput;
import com.timevale.saas.common.manage.common.service.model.output.resourcebelong.ResourceBelongDTO;
import com.timevale.saas.common.manage.core.model.enums.BizIdTypeEnum;
import com.timevale.saas.common.manage.core.model.enums.ChangeTypeEnum;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager.ResourceBelongChangePendingTaskManager;
import com.timevale.saas.common.manage.core.service.converter.ResourceBelongConverter;
import com.timevale.saas.common.manage.core.service.mq.model.DeptChangeMessage;
import com.timevale.saas.common.manage.core.service.mq.producer.DefaultProducer;
import com.timevale.saas.common.manage.core.service.resourcebelong.ResourceBelongService;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.manager.ResourceBelongManager;
import com.timevale.saas.common.manage.core.service.resourcebelong.domain.model.ResourceBelongModel;
import com.timevale.saas.common.manage.core.service.util.AssertX;
import com.timevale.saas.common.validator.util.ValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/4 10:53
 */
@Slf4j
@Service
public class ResourceBelongServiceImpl implements ResourceBelongService {

    @Autowired private ResourceBelongManager resourceBelongManager;
    @Autowired private DefaultProducer defaultProducer;
    @Autowired private TransactionTemplate transactionTemplate;
    @Resource private ResourceBelongChangePendingTaskManager resourceBelongChangePendingTaskManager;

    @Override
    public void batchSaveResourceBelong(ResourceBelongBatchSaveInput input) {
        ValidationUtil.validateBean(input);
        List<ResourceBelongModel> dataList = ResourceBelongConverter.saveInput2Model(input);
        Integer resourceType = input.getResourceType();

        // 这里数据不会有太多条 每个合同的归属，或者每个相对方的归属
        // 考虑下资源删除的问题，1.合同删除没问题 2.相对方删除会有问题吗， 会提供删除模式
        Boolean success = transactionTemplate.execute(status -> {
            try {
                if (Boolean.TRUE.equals(input.getAddBeforeShouldDeleteOldData())) {
                    // 添加之前删除之前的老数据
                    List<String> resourceIds = input.getDataList().stream()
                            .map(ResourceBelongSaveInput::getResourceId)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(resourceIds)) {
                        resourceBelongManager.deleteByResource(resourceIds, resourceType);
                    }
                }

                try {
                    resourceBelongManager.save(dataList);
                } catch (DuplicateKeyException dke) {
                    if (dataList.size() != 1) {
                        dataList.forEach(elm -> {
                            try {
                                resourceBelongManager.save(Lists.newArrayList(elm));
                            } catch (DuplicateKeyException singleDke) {
                                log.warn("resource belong save dlp data : {}", JSON.toJSONString(elm));
                            }
                        });
                    }
                }
                return true;
            } catch (Exception e) {
                log.error("resource belong batch save", e);
                status.setRollbackOnly();
                return false;
            }
        });

        AssertX.isTrue(success, "更新数据失败");

        // 去重
        Set<String> resourceIds = dataList.stream()
                .map(ResourceBelongModel::getResourceId).collect(Collectors.toSet());

        // 发消息
        resourceIds.forEach(resourceId -> {
            ResourceBelongChangeMessage message = new ResourceBelongChangeMessage();
            message.setResourceId(resourceId);
            message.setResourceType(resourceType);
            defaultProducer.sendOrder(ResourceBelongTopicConstant.TOPIC, ResourceBelongTopicConstant.DEFAULT_TAG,
                    resourceId, JSON.toJSONString(message));
        });
    }

    @Override
    public List<ResourceBelongDTO> getResourceBelong(ResourceBelongGetInput input) {
        ValidationUtil.validateBean(input);
        List<ResourceBelongModel> list =
                resourceBelongManager.listByResource(input.getResourceId(), input.getResourceType());
        return ResourceBelongConverter.model2DTO(list);
    }

    @Override
    public void deleteResourceBelong(ResourceBelongBatchDeleteInput input) {
        Integer resourceBelongType = input.getResourceBelongType();
        List<String> resourceIds = input.getResourceIds();
        List<String> haveDataResourceIds =
                resourceBelongManager.checkHaveDataResourceIds(resourceIds, resourceBelongType);
        if (CollectionUtils.isEmpty(haveDataResourceIds)) {
            return;
        }

        // 这里数据不会太多一般就几条
        resourceBelongManager.deleteByResource(haveDataResourceIds, resourceBelongType);
        // 发送变更消息
        haveDataResourceIds.forEach(resourceId -> {
            ResourceBelongChangeMessage message = new ResourceBelongChangeMessage();
            message.setResourceId(resourceId);
            message.setResourceType(resourceBelongType);
            defaultProducer.sendOrder(ResourceBelongTopicConstant.TOPIC, ResourceBelongTopicConstant.DEFAULT_TAG,
                    resourceId, JSON.toJSONString(message));
        });
    }

    @Override
    public void deptDelete(DeptChangeMessage deptChangeMessage) {
        String deptId = deptChangeMessage.getOwner().getAutoIncId().toString();
        List<ResourceBelongModel> resourceBelongs = resourceBelongManager.listByDirectDeptId(deptId, 1);
        if (CollectionUtils.isEmpty(resourceBelongs)) {
            log.info("deleted dept not exist resource belongs, deptId:{}", deptId);
            return;
        }
        ResourceBelongChangePendingTaskDO pendingTask = new ResourceBelongChangePendingTaskDO();
        pendingTask.setBizId(deptChangeMessage.getOwner().getDeptId());
        pendingTask.setBizType(BizIdTypeEnum.DEPT.getType());
        pendingTask.setChangeType(ChangeTypeEnum.DEPT_DELETE.getType());
        pendingTask.setChangeData(JSON.toJSONString(deptChangeMessage));
        resourceBelongChangePendingTaskManager.saveTask(pendingTask);
    }

    @Override
    public void executeDeptChangeTask() {
        // 查询待执行任务列表
        List<String> changeTypes = Lists.newArrayList(ChangeTypeEnum.DEPT_DELETE.getType());
        List<ResourceBelongChangePendingTaskDO> jobPendingTasks = resourceBelongChangePendingTaskManager.queryByChangeType(changeTypes, null, 10);
        // 执行任务
        jobPendingTasks.forEach(i -> {
            if (StringUtils.isBlank(i.getChangeData())) {
                return;
            }
            DeptChangeMessage deptChangeMessage = JSON.parseObject(i.getChangeData(), DeptChangeMessage.class);
            // 处理部门变更
            processDeptChangeMessage(deptChangeMessage);
            log.info("resource belong dept change handle complete, deptId: {}", i.getBizId());
            // 移除定时任务待执行任务
            resourceBelongChangePendingTaskManager.deleteTask(i.getId());
        });
    }

    @Override
    public void processDeptChangeMessage(DeptChangeMessage deptChangeMessage) {

        if (null == deptChangeMessage.getOwner() || null == deptChangeMessage.getOwner().getAutoIncId()) {
            return;
        }
        // 获取原来的部门id
        String deptId = deptChangeMessage.getOwner().getAutoIncId().toString();
        // 获取最新部门路径
        List<String> newDeptPath = null;
        // 获取最新直属部门id
        String newDirectDeptId = null;
        if (CollectionUtils.isNotEmpty(deptChangeMessage.getParentList())) {
            newDeptPath = deptChangeMessage.getParentList().stream().map(DeptChangeMessage.DeptId::getAutoIncId).map(String::valueOf)
                    .collect(Collectors.toList());
            newDirectDeptId = deptChangeMessage.getParentList().get(deptChangeMessage.getParentList().size() - 1)
                    .getAutoIncId().toString();
        }
        // 处理部门变更资源归属
        handleResourceBelongDeptChange(deptId, newDirectDeptId, newDeptPath);
        log.info("processDeptChangeMessage success, deptId: {}, newDirectDeptId: {}, newDeptPath: {}", deptId, newDirectDeptId, newDeptPath);
    }

    /**
     * 处理部门变更资源归属
     * @param deptId 原来的部门id
     * @param newDeptId 新的部门id
     * @param newDeptPath 新的部门路径
     */
    private void handleResourceBelongDeptChange(String deptId, String newDeptId,  List<String> newDeptPath) {
        List<ResourceBelongModel> resourceBelongList = resourceBelongManager.listByDirectDeptId(deptId, 500);
        if (CollectionUtils.isEmpty(resourceBelongList)) {
            return;
        }
        List<Long> resourceBelongIds = new ArrayList<>();
        Map<String, Set<Integer>> resourceIdResourceTypeMap = new HashMap<>();
        for (ResourceBelongModel resourceBelongModel : resourceBelongList) {
            resourceBelongIds.add(resourceBelongModel.getId());
            resourceIdResourceTypeMap
                    .computeIfAbsent(resourceBelongModel.getResourceId(), key -> new HashSet<>())
                    .add(resourceBelongModel.getResourceType());
        }
        Set<String> resourceIds = resourceIdResourceTypeMap.keySet();
        String subjectOid = resourceBelongList.get(0).getSubjectOid();
        String subjectGid = resourceBelongList.get(0).getSubjectGid();
        // 构建新资源归属记录列表
        List<ResourceBelongModel> saveDataList =
                buildNewResourceLongList(resourceIds, newDeptId, newDeptPath, subjectOid, subjectGid, resourceIdResourceTypeMap);
        Boolean success = transactionTemplate.execute(status -> {
            try {
                // 调整归属
                // 删除原来的
                resourceBelongManager.delete(resourceBelongIds);
                // 保存新的
                if (CollectionUtils.isNotEmpty(saveDataList)) {
                    resourceBelongManager.save(saveDataList);
                }
                return true;
            } catch (Exception e) {
                log.error("resource belong dept delete ", e);
                status.setRollbackOnly();
                return false;
            }
        });
        AssertX.isTrue(success, "更新数据失败");

        // 发消息
        resourceIds.forEach(resourceId ->
                resourceIdResourceTypeMap.get(resourceId).forEach(resourceType -> {
                    ResourceBelongChangeMessage message = new ResourceBelongChangeMessage();
                    message.setResourceId(resourceId);
                    message.setResourceType(resourceType);
                    defaultProducer.sendOrder(ResourceBelongTopicConstant.TOPIC, ResourceBelongTopicConstant.DEFAULT_TAG,
                            resourceId, JSON.toJSONString(message));
                })
        );
        // 继续处理部门变更资源归属
        handleResourceBelongDeptChange(deptId, newDeptId, newDeptPath);
    }

    /**
     * 构建新资源归属记录列表
     * @param resourceIds 资源id列表
     * @param newDeptId 新部门id
     * @param newDeptPath 新部门路径
     * @param subjectOid 主体oid
     * @param subjectGid 主体gid
     * @param resourceIdResourceTypeMap 资源类型Map
     * @return 资源归属记录列表
     */
    private List<ResourceBelongModel> buildNewResourceLongList(Set<String> resourceIds, String newDeptId, List<String> newDeptPath, String subjectOid, String subjectGid, Map<String, Set<Integer>> resourceIdResourceTypeMap) {
        List<ResourceBelongModel> newResourceBelongList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(newDeptPath)) {
            return newResourceBelongList;
        }
        // 查询新部门id下是否已经存在指定资源id归属记录
        List<ResourceBelongModel> newDeptResourceBelongList = resourceBelongManager.listByResourceWithDeptId(resourceIds, newDeptId);
        Map<String, ResourceBelongModel> newDeptResourceBelongModelMap = newDeptResourceBelongList.stream().collect(Collectors.toMap(i -> buildResourceBelongUniqueKey(i.getResourceId(), i.getResourceType(), newDeptId), Function.identity()));
        // 构建新的资源归属记录
        resourceIds.forEach(resourceId -> {
            resourceIdResourceTypeMap.get(resourceId).forEach(resourceType -> {
                // 如果已经存在资源归属记录， 跳过
                if (newDeptResourceBelongModelMap.containsKey(buildResourceBelongUniqueKey(resourceId, resourceType, newDeptId))) {
                    return;
                }
                // 构建资源归属记录
                ResourceBelongModel saveInput = new ResourceBelongModel();
                saveInput.setResourceId(resourceId);
                saveInput.setResourceType(resourceType);
                saveInput.setSubjectOid(subjectOid);
                saveInput.setSubjectGid(subjectGid);
                saveInput.setDeptPath(newDeptPath);
                saveInput.setDirectDeptId(newDeptId);
                saveInput.setReason("");
                newResourceBelongList.add(saveInput);
            });
        });
        return newResourceBelongList;
    }

    /**
     * 组装资源归属记录唯一标识
     * @param resourceId 资源id
     * @param resourceType 资源类型
     * @param deptId 部门id
     * @return 唯一标识
     */
    private String buildResourceBelongUniqueKey(String resourceId, Integer resourceType, String deptId) {
        return String.format("%s:%s:%s", resourceId, resourceType, deptId);
    }

    // 多组织，现在暂不支持 部门调整上级部门，代码先保留
//    @Override
//    public void deptChangeParent(DeptChangeMessage deptChangeMessage) {
//        String deptId = deptChangeMessage.getOwner().getAutoIncId().toString();
//        List<String> newDeptPath = deptChangeMessage.getParentList()
//                .stream()
//                .map(DeptChangeMessage.DeptId::getAutoIncId).map(String::valueOf)
//                .collect(Collectors.toList());
//
//        Long minxId = null;
//        for (; ; ) {
//            List<ResourceBelongModel> resourceBelongList =
//                    resourceBelongManager.listScrollByDirectDeptId(deptId, minxId, 100);
//            if (CollectionUtils.isEmpty(resourceBelongList)) {
//                break;
//            }
//            minxId = resourceBelongList.get(resourceBelongList.size() - 1).getId();
//            List<Long> resourceBelongIds = new ArrayList<>();
//            Map<String, Set<Integer>> resourceIdResourceTypeMap = new HashMap<>();
//            //
//            Set<String> resourceIds = new HashSet<>();
//            for (ResourceBelongModel resourceBelongModel : resourceBelongList) {
//                resourceBelongIds.add(resourceBelongModel.getId());
//                resourceIdResourceTypeMap
//                        .computeIfAbsent(resourceBelongModel.getResourceId(), key -> new HashSet<>())
//                        .add(resourceBelongModel.getResourceType());
//                resourceIds.add(resourceBelongModel.getResourceId());
//            }
//            resourceBelongManager.batchUpdatePath(resourceBelongIds, newDeptPath);
//            //todo redis 存储下进度
//            // 发消息
//            resourceIds.forEach(resourceId ->
//                    resourceIdResourceTypeMap.get(resourceId).forEach(resourceType -> {
//                        ResourceBelongChangeMessage message = new ResourceBelongChangeMessage();
//                        message.setResourceId(resourceId);
//                        message.setResourceType(resourceType);
//                        defaultProducer.sendOrder(ResourceBelongTopicConstant.TOPIC, ResourceBelongTopicConstant.DEFAULT_TAG,
//                                resourceId, JSON.toJSONString(message));
//                    })
//            );
//        }
//    }

}
