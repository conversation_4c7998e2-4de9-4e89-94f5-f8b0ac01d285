package com.timevale.saas.common.manage.core.service.authrelation.domain.manager;

import com.timevale.saas.common.manage.common.dal.query.authrelation.AuthRelationLogQuery;
import com.timevale.saas.common.manage.core.service.authrelation.domain.model.AuthRelationLogModel;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/25 16:36
 */
public interface AuthRelationLogManager {

    /**
     * 保存
     */
    int save(AuthRelationLogModel data);

    /**
     * 更新
     */
    int updateById(AuthRelationLogModel data);

    AuthRelationLogModel getById(Long id);

    AuthRelationLogModel getByFlowId(String flowId);

    AuthRelationLogModel getByProcessId(String processId);

    List<AuthRelationLogModel> listByIds(List<Long> ids);

    List<AuthRelationLogModel> listByAuthRelationId(Long authRelationId, Integer offset, Integer pageSize);

    List<AuthRelationLogModel> listByAuthTenantGidAndChildTenantGid(String authTenantGid, String childTenantGid, String currTenantGid);

    /**
     * 用于查询可用记录
     */
    List<AuthRelationLogModel> listAvailable(Long authRelationId,
            List<Integer> statusList,
                                             Integer bizVersion,
                                             Integer size);
    /**
     * 用于查询可用记录
     */
    List<AuthRelationLogModel> listAvailable(List<Long> authRelationIds,
                                             List<Integer> statusList,
                                             Integer bizVersion,
                                             Integer size);

    /**
     * 复合查询
     */
    List<AuthRelationLogModel> listByQuery(AuthRelationLogQuery query);

}
