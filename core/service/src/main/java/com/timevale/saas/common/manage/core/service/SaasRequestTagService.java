package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.urimanage.tag.RequestTagAddRequest;
import com.timevale.saas.common.manage.core.model.request.urimanage.tag.RequestTagUpdateRequest;
import com.timevale.saas.common.manage.core.model.response.urimanage.tag.RequestTagListResponse;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
public interface SaasRequestTagService {

    RequestTagListResponse list(Integer type);
    
    void addTag(RequestTagAddRequest request);

    void updateTag(String tag, RequestTagUpdateRequest request);

    void deleteTag(String tag, Integer type);
}
