package com.timevale.saas.common.manage.core.service.authrelation.domain.model;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/5/25 16:31
 */
@Data
public class AuthRelationLogModel {

    private Long id;

    private Date createTime;

    private Date updateTime;

    private Long authRelationId;

    private String authTenantGid;

    private String authTenantOid;

    private String parentTenantGid;

    private String parentTenantOid;

    private String childTenantGid;

    private String childTenantOid;

    private Integer status;

    private Date effectiveStartTime;

    private Date effectiveEndTime;

    /**
     * 结束时间还没到，用户取消了
     */
    private Date trueEffectiveEndTime;

    private Integer effectiveUnit;

    private Integer effectiveAmount;

    private Integer authReason;

    private String failReason;

    private String billingBizId;

    private String freezeInfo;

    private String processId;

    private String flowId;

    /**
     * 构建时的授权等级
     * @see this#authResource
     */
    @Deprecated
    private Integer authLevel;

    /**
     * 原来都是0, 多组织后是1
     */
    private Integer bizVersion;

    /**
     * 扩展字段
     */
    private AuthRelationLogExtraModel extra;

    public AuthRelationLogExtraModel getExtra() {
        if (null == extra) {
            extra = new AuthRelationLogExtraModel();
        }
        return extra;
    }

    private List<String> authResource;

    /**
     * 二级资源，key为一级授权资源code
     */
    private Map<String, List<String>> secondAuthResource;

    private String operatorOid;
}
