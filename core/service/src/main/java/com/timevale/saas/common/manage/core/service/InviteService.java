package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.common.dal.dataobject.InviteRecordDO;
import com.timevale.saas.common.manage.core.model.model.UserAccount;
import com.timevale.saas.common.manage.core.model.request.ChangeInviteSubjectRequest;
import com.timevale.saas.common.manage.core.model.request.StartInviteRequest;
import com.timevale.saas.common.manage.core.model.response.QueryInviteHasRecordResponse;
import com.timevale.saas.common.manage.core.model.response.QueryInviteInfoResponse;
import com.timevale.saas.common.manage.core.model.response.QueryInviteListResponse;

import java.util.List;

/**
 * <AUTHOR>
 *
 * @date 2022/11/14
 */
public interface InviteService {

    /**
     * 发起邀请
     * @param operationId
     * @param tenantId
     * @param request
     * @return
     */
    String sendInvite(String operationId,String tenantId,StartInviteRequest request);

    /**
     * 发起邀请校验
     *
     * @param operationId
     * @param tenantId
     * @param request
     */
    void sendInviteCheck(String operationId,String tenantId,StartInviteRequest request);

    /**
     * 修改邀请企业信息
     *
     * @param operationId
     * @param inviteId
     * @param request
     */
    void updateInviteSubject(String operationId,String inviteId, ChangeInviteSubjectRequest request);

    /**
     * 查询邀请记录列表
     *
     * @param operatorId
     * @param tenantId
     * @param readStatus
     * @param pageNo
     * @param pageSize
     * @return
     */
    QueryInviteListResponse queryInviteList(String operatorId, String tenantId,Integer readStatus, Integer pageNo, Integer pageSize);

    /**
     * 根据操作人查询邀请详情（优先返回最新进行中的邀请）
     *
     * @param operatorOid
     * @param tenantId
     * @param inviteId
     * @return
     */
    QueryInviteInfoResponse queryInviteRecordInfo(String operatorOid,String tenantId, String inviteId);

    /**
     * 已读操作
     *  @param operatorId
     * @param tenantId
     */
    void changeRead(String operatorId, String tenantId);

    /**
     * 是否存在邀请记录
     * @param operatorId
     * @return
     */
    QueryInviteHasRecordResponse hasRecord(String operatorId);

    /**
     * 邀请实名处理逻辑
     *
     * @param account 实名账号
     * @param serviceId 实名认证流程id
     */
    void inviteRealName(UserAccount account,String serviceId);
}
