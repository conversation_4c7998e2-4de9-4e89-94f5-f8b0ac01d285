package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.model.SubjectFlagDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/2/13
 * 主体相关
 */
public interface SubjectsService {
	/**
	 * 获取用户的主体信息，包含企业和个人主体
	 * @param userOid
	 * @param orgFlags
	 * @return
	 */
	 List<SubjectFlagDTO> getSubjects(String userOid, Set<String> orgFlags);

    void batchDeleteUsersFromOOrg(String operatorOid,String orgOid, List<String> deleteUserOidList);
}
