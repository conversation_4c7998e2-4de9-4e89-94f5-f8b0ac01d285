package com.timevale.saas.common.manage.core.service.authrelation;

import com.timevale.saas.common.manage.core.model.bo.authrelation.AuthRelationOperatorLogBO;
import com.timevale.saas.common.manage.core.model.request.authrelation.AuthRelationOperatorListRequest;

/**
 * @Author:jiany<PERSON>
 * @since 2022-04-29 15:23
 */
public interface AuthRelationOperatorService {

	AuthRelationOperatorLogBO getOperatorLogs(AuthRelationOperatorListRequest listRequest);

	String getDownloadUrl();

	String getAuthDownloadUrl();

	String getDownloadUrlByFileKey(String fileKey);
}
