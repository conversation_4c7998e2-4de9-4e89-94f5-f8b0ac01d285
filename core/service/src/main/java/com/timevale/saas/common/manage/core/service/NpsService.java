package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.nps.NpsInfoCallBackRequest;
import com.timevale.saas.common.manage.core.model.request.nps.NpsInfoSaveRequest;
import com.timevale.saas.common.manage.core.model.response.nps.NpsInfoAdminDetailResponse;
import com.timevale.saas.common.manage.core.model.response.nps.NpsInfoListResponse;
import com.timevale.saas.common.manage.core.model.response.nps.NpsInfoPageResponse;

/**
 * <AUTHOR>
 * @since 2022/1/4
 */
public interface NpsService {

    /**
     * 获取nps埋点配置问卷信息
     *
     * @param productCode 渠道码
     * @param machineId 设备号
     * @return 返回埋点信息
     */
    NpsInfoListResponse getNpsList(String productCode);

    /**
     * 新增问卷信息
     *
     * @param request
     */
    Long add(NpsInfoSaveRequest request);

    /**
     * 删除问卷信息
     *
     * @param id 问卷id
     */
    void del(Long id);

    /**
     * 修改问卷信息
     *
     * @param request 问卷信息
     * @param id 问卷id
     */
    void update(NpsInfoSaveRequest request, Long id);

    /**
     * 管理段搜索列表
     *
     * @param productCode 产品编码
     * @param areaCode 位置区域
     * @param status 状态
     * @param name 名称
     * @param pageSize 每页数量
     * @param pageNo 页码
     * @return 问卷信息列表
     */
    NpsInfoPageResponse list(
            String productCode,
            String areaCode,
            Integer status,
            String name,
            Integer pageSize,
            Integer pageNo);

    /**
     * 管理查询详细
     *
     * @param id 问卷id
     * @return 问卷详情信息
     */
    NpsInfoAdminDetailResponse info(Long id);

    /**
     * 栏目内容上/下架
     *
     * @param id 问卷id
     * @param status 上下架状态： 0 下架，1上架
     */
    void enable(Long id, Integer status);

    /**
     * 用户显示问卷后回调接口： 增加问卷透出数量，增加用户维度已透出问卷记录
     *
     * @param request 问卷请求参数
     */
    void callback(NpsInfoCallBackRequest request);
}
