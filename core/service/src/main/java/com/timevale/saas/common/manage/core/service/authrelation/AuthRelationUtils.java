package com.timevale.saas.common.manage.core.service.authrelation;

import com.alibaba.fastjson.JSON;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationDO;
import com.timevale.saas.common.manage.common.dal.dataobject.authrelation.AuthRelationExtra;
import com.timevale.saas.common.manage.common.service.enums.authrelation.AuthRelationTagEnum;

import java.util.List;

/**
 * Created by tianlei on 2022/5/9
 */
public class AuthRelationUtils {

    public static AuthRelationExtra getExtra(AuthRelationDO authRelationDO) {

        String extra = authRelationDO.getExtra();
        if (StringUtils.isBlank(extra)) {
            return new AuthRelationExtra();
        }
        AuthRelationExtra authRelationExtra = JSON.parseObject(extra, AuthRelationExtra.class);
        return authRelationExtra;
    }

    public static boolean offlineAdd(AuthRelationExtra authRelationExtra) {
        return null != authRelationExtra && offlineAdd(authRelationExtra.getTags());
    }

    public static boolean offlineAdd(List<Long> tags) {
        return CollectionUtils.isNotEmpty(tags) && tags.contains(AuthRelationTagEnum.OFFLINE_ADD.getCode());
    }

}
