package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.common.service.model.output.UserActionOutput;
import com.timevale.saas.common.manage.core.model.request.UserActionRequest;

/**
 * 用户行为记录service
 * <AUTHOR>
 */
public interface UserActionService {

    /**
     * 创建用户行为记录
     * @param request
     */
    void create(UserActionRequest request);

    /**
     * 查询用户行为记录
     * @param request
     * @return
     */
    UserActionOutput query(UserActionRequest request);


    /**
     * 清理过期数据
     */
    void clearExpiredUserAction();

}
