package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.urimanage.limit.*;
import com.timevale.saas.common.manage.core.model.response.urimanage.limit.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-21
 */
public interface SaasRequestAccessLimitService {

    /**
     * 查询访问限制配置列表
     *
     * @param request
     * @return
     */
    RequestAccessLimitListResponse list(RequestAccessLimitListRequest request);

    /**
     * 新增访问限制配置
     *
     * @param request
     * @return
     */
    Long addLimit(RequestAccessLimitAddRequest request);

    /**
     * 更新访问限制配置
     *
     * @param id
     * @param request
     */
    void updateLimit(Long id, RequestAccessLimitUpdateRequest request);

    /**
     * 单个更新访问限制状态
     *
     * @param request
     */
    void updateLimitStatus(Long id, RequestAccessLimitUpdateStatusRequest request);

    /**
     * 批量更新访问限制状态
     *
     * @param request
     */
    void batchUpdateLimitStatus(BatchRequestAccessLimitUpdateStatusRequest request);

    /**
     * 批量修改访问限制配置信息, 暂时只支持修改业务域/负责人
     *
     * @param request
     */
    void batchUpdateLimitInfo(BatchRequestAccessLimitUpdateInfoRequest request);

    /**
     * 获取指定接口的限制配置信息
     *
     * @param uri
     * @return
     */
    RequestAccessLimitCheckResponse checkUriLimit(String uri);

    /**
     * 根据id查询访问限制配置
     *
     * @param id
     * @return
     */
    RequestAccessLimitDetailResponse queryLimitDetail(Long id);

    /**
     * 根据uri查询访问限制配置
     *
     * @param uri
     * @return
     */
    RequestAccessLimitQueryResponse queryUriLimit(String uri);

    /**
     * 校验接口限制是否已存在
     *
     * @param uri
     * @return
     */
    boolean checkUriExisted(String uri);

    /** 清除今天之前已废弃的接口配置列表 */
    void clearAbandoned();

    /**
     * 新增访问限制配置
     *
     * @param request
     * @return
     */
    void batchAddLimit(List<RequestAccessLimitAddRequest> request);

    /**
     * 导出访问限制
     * @param request
     * @return
     */
    RequestAccessLimitExportResponse exportUriLimits(RequestAccessLimitExportRequest request);

    /**
     * 导入访问限制
     * @param request
     * @return
     */
    RequestAccessLimitImportResponse importUriLimits(RequestAccessLimitImportRequest request);
}
