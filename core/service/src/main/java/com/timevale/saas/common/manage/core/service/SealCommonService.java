package com.timevale.saas.common.manage.core.service;

import com.timevale.saas.common.manage.core.model.request.seal.CompressSealRequest;
import com.timevale.saas.common.manage.core.model.response.seal.CompressSealResponse;
import com.timevale.saas.common.manage.core.model.response.seal.GetAuditRefuseInfoResponse;

/**
 * <AUTHOR>
 * @since 2022/7/7 印章通用能力服务
 */
public interface SealCommonService {
    /**
     * 获取印章审核拒绝原因
     *
     * @param sealId
     * @return
     */
    GetAuditRefuseInfoResponse getAuditRefuse(String sealId);

    /**
     * 压缩、透明化处理印章
     *
     * @param request
     * @return
     */
    CompressSealResponse compressSeal(CompressSealRequest request);
}
