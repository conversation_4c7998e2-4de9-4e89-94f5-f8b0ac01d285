<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.timevale.saas-common-manage</groupId>
		<artifactId>saas-common-manage-parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../../pom.xml</relativePath>
	</parent>

	<artifactId>saas-common-manage-core-service</artifactId>
	<name>saas-common-manage/service</name>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>saas-common-manage-spi-invoker</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>saas-common-manage-core-model</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.spring.boot</groupId>
			<artifactId>elock-spring-boot-starter</artifactId>
		</dependency>
		<!--对象映射转换-->
		<dependency>
			<groupId>ma.glasnost.orika</groupId>
			<artifactId>orika-core</artifactId>
			<version>1.5.2</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-api</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.google.zxing</groupId>
			<artifactId>core</artifactId>
			<version>3.3.3</version>
		</dependency>

		<!-- 分布式锁-->
<!--		<dependency>-->
<!--			<groupId>com.timevale.base</groupId>-->
		<!--			<artifactId>eLock</artifactId>-->
		<!--			<version>0.0.22-SNAPSHOT</version>-->
		<!--		</dependency>-->

		<dependency>
			<groupId>com.timevale.gray-config-manage</groupId>
			<artifactId>gray-config-manage-facade</artifactId>
			<version>${gray-config-manage-facade-version}</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.sealmanager</groupId>
			<artifactId>sealmanager-common-service-facade</artifactId>
			<version>${sealmanager.common.service.facade}</version>
		</dependency>

		<dependency>
			<groupId>com.timevale.saas.common</groupId>
			<artifactId>all</artifactId>
			<version>1.0.13</version>
			<exclusions>
				<exclusion>
					<groupId>org.mybatis.spring.boot</groupId>
					<artifactId>mybatis-spring-boot-starter</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.timevale.spring.boot</groupId>
					<artifactId>sands-spring-boot-starter</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.timevale.saas</groupId>
			<artifactId>common-exception-util</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.timevale.saas</groupId>
			<artifactId>common-privilege-util</artifactId>
			<version>1.0.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.timevale.easun</groupId>
                    <artifactId>easun-facade</artifactId>
                </exclusion>
            </exclusions>
		</dependency>
		<dependency>
			<groupId>com.timevale.dayu</groupId>
			<artifactId>dayu-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.timevale.dayu</groupId>
			<artifactId>sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>com.timevale.saas</groupId>
			<artifactId>tracking-util</artifactId>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.account</groupId>
            <artifactId>account-organization-facade</artifactId>
            <version>2.6.7-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.timevale.saas</groupId>
            <artifactId>common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.timevale.dayu</groupId>
            <artifactId>config-facade</artifactId>
        </dependency>
    </dependencies>
</project>
